/**
 * @file ui-helpers.js
 * @description UI helper functions for Atlas25 Web App
 * @module utils/ui-helpers
 */

import stateManager from '../core/state/state-manager.js';

/**
 * Show a notification
 * @param {string} message - Notification message
 * @param {Object} [options={}] - Notification options
 * @param {string} [options.type='info'] - Notification type ('info', 'success', 'warning', 'error')
 * @param {number} [options.duration=5000] - Duration in milliseconds (0 for no auto-hide)
 * @param {boolean} [options.dismissible=true] - Whether the notification is dismissible
 * @returns {string} - Notification ID
 */
export function showNotification(message, options = {}) {
  const { type = 'info', duration = 5000, dismissible = true } = options;
  
  // Use state manager if available
  if (stateManager) {
    return stateManager.addNotification({
      message,
      type,
      duration,
      dismissible
    });
  }
  
  // Fallback to DOM-based notification
  return createDOMNotification(message, { type, duration, dismissible });
}

/**
 * Create a DOM-based notification
 * @param {string} message - Notification message
 * @param {Object} options - Notification options
 * @returns {string} - Notification ID
 * @private
 */
function createDOMNotification(message, options) {
  const { type, duration, dismissible } = options;
  
  // Create notification container if it doesn't exist
  let container = document.querySelector('.atlas25-notifications');
  
  if (!container) {
    container = document.createElement('div');
    container.className = 'atlas25-notifications';
    document.body.appendChild(container);
    
    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .atlas25-notifications {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-width: 300px;
      }
      
      .atlas25-notification {
        padding: 12px 16px;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        animation: atlas25-notification-in 0.3s ease-out;
      }
      
      .atlas25-notification.closing {
        animation: atlas25-notification-out 0.3s ease-in forwards;
      }
      
      .atlas25-notification-content {
        flex: 1;
        margin-right: 10px;
      }
      
      .atlas25-notification-close {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 16px;
        color: inherit;
        opacity: 0.7;
      }
      
      .atlas25-notification-close:hover {
        opacity: 1;
      }
      
      .atlas25-notification.info {
        background-color: #e3f2fd;
        color: #0d47a1;
        border-left: 4px solid #2196f3;
      }
      
      .atlas25-notification.success {
        background-color: #e8f5e9;
        color: #1b5e20;
        border-left: 4px solid #4caf50;
      }
      
      .atlas25-notification.warning {
        background-color: #fff8e1;
        color: #ff8f00;
        border-left: 4px solid #ffc107;
      }
      
      .atlas25-notification.error {
        background-color: #fbe9e7;
        color: #c41c00;
        border-left: 4px solid #f44336;
      }
      
      @keyframes atlas25-notification-in {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      
      @keyframes atlas25-notification-out {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }
  
  // Create notification element
  const id = `notification-${Date.now()}`;
  const notification = document.createElement('div');
  notification.className = `atlas25-notification ${type}`;
  notification.id = id;
  
  // Create content
  const content = document.createElement('div');
  content.className = 'atlas25-notification-content';
  content.textContent = message;
  notification.appendChild(content);
  
  // Create close button if dismissible
  if (dismissible) {
    const closeButton = document.createElement('button');
    closeButton.className = 'atlas25-notification-close';
    closeButton.innerHTML = '&times;';
    closeButton.addEventListener('click', () => {
      closeNotification(id);
    });
    notification.appendChild(closeButton);
  }
  
  // Add to container
  container.appendChild(notification);
  
  // Auto-hide after duration
  if (duration > 0) {
    setTimeout(() => {
      closeNotification(id);
    }, duration);
  }
  
  return id;
}

/**
 * Close a notification
 * @param {string} id - Notification ID
 */
export function closeNotification(id) {
  // Use state manager if available
  if (stateManager) {
    stateManager.removeNotification(id);
    return;
  }
  
  // Fallback to DOM-based notification
  const notification = document.getElementById(id);
  
  if (notification) {
    notification.classList.add('closing');
    
    // Remove after animation
    notification.addEventListener('animationend', () => {
      notification.remove();
    });
  }
}

/**
 * Show a loading spinner
 * @param {HTMLElement|string} container - Container element or selector
 * @param {Object} [options={}] - Loading spinner options
 * @param {string} [options.size='medium'] - Spinner size ('small', 'medium', 'large')
 * @param {string} [options.color='#2196f3'] - Spinner color
 * @param {string} [options.message='Loading...'] - Loading message
 * @returns {Object} - Loading spinner control object
 */
export function showLoading(container, options = {}) {
  const { size = 'medium', color = '#2196f3', message = 'Loading...' } = options;
  
  // Get container element
  const containerElement = typeof container === 'string' 
    ? document.querySelector(container) 
    : container;
    
  if (!containerElement) {
    console.error('Container element not found');
    return null;
  }
  
  // Create loading element
  const loadingElement = document.createElement('div');
  loadingElement.className = 'atlas25-loading';
  
  // Set size
  let spinnerSize;
  switch (size) {
    case 'small':
      spinnerSize = '20px';
      break;
    case 'large':
      spinnerSize = '40px';
      break;
    default:
      spinnerSize = '30px';
      break;
  }
  
  // Add styles
  const style = document.createElement('style');
  style.textContent = `
    .atlas25-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }
    
    .atlas25-spinner {
      width: ${spinnerSize};
      height: ${spinnerSize};
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top-color: ${color};
      animation: atlas25-spin 1s linear infinite;
    }
    
    .atlas25-loading-message {
      margin-top: 10px;
      font-size: 14px;
      color: #666;
    }
    
    @keyframes atlas25-spin {
      to {
        transform: rotate(360deg);
      }
    }
  `;
  document.head.appendChild(style);
  
  // Create spinner
  const spinner = document.createElement('div');
  spinner.className = 'atlas25-spinner';
  loadingElement.appendChild(spinner);
  
  // Add message if provided
  if (message) {
    const messageElement = document.createElement('div');
    messageElement.className = 'atlas25-loading-message';
    messageElement.textContent = message;
    loadingElement.appendChild(messageElement);
  }
  
  // Add to container
  containerElement.appendChild(loadingElement);
  
  // Return control object
  return {
    update: (newMessage) => {
      const messageElement = loadingElement.querySelector('.atlas25-loading-message');
      if (messageElement && newMessage) {
        messageElement.textContent = newMessage;
      }
    },
    hide: () => {
      loadingElement.remove();
    }
  };
}

/**
 * Show a modal dialog
 * @param {Object} options - Modal options
 * @param {string} options.title - Modal title
 * @param {string|HTMLElement} options.content - Modal content
 * @param {Array} [options.buttons=[]] - Modal buttons
 * @param {boolean} [options.closeOnBackdrop=true] - Whether to close on backdrop click
 * @param {boolean} [options.closeOnEscape=true] - Whether to close on escape key
 * @param {string} [options.size='medium'] - Modal size ('small', 'medium', 'large')
 * @returns {Object} - Modal control object
 */
export function showModal(options) {
  const { 
    title, 
    content, 
    buttons = [], 
    closeOnBackdrop = true, 
    closeOnEscape = true,
    size = 'medium'
  } = options;
  
  // Create modal container if it doesn't exist
  let container = document.querySelector('.atlas25-modals');
  
  if (!container) {
    container = document.createElement('div');
    container.className = 'atlas25-modals';
    document.body.appendChild(container);
    
    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .atlas25-modals {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        pointer-events: none;
      }
      
      .atlas25-modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: atlas25-fade-in 0.3s ease-out;
        pointer-events: auto;
      }
      
      .atlas25-modal-backdrop.closing {
        animation: atlas25-fade-out 0.3s ease-in forwards;
      }
      
      .atlas25-modal {
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        max-height: 90vh;
        animation: atlas25-scale-in 0.3s ease-out;
        overflow: hidden;
      }
      
      .atlas25-modal.closing {
        animation: atlas25-scale-out 0.3s ease-in forwards;
      }
      
      .atlas25-modal.small {
        width: 300px;
      }
      
      .atlas25-modal.medium {
        width: 500px;
      }
      
      .atlas25-modal.large {
        width: 800px;
      }
      
      .atlas25-modal-header {
        padding: 16px;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      
      .atlas25-modal-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .atlas25-modal-close {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 20px;
        color: #999;
      }
      
      .atlas25-modal-close:hover {
        color: #333;
      }
      
      .atlas25-modal-body {
        padding: 16px;
        overflow-y: auto;
      }
      
      .atlas25-modal-footer {
        padding: 16px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 8px;
      }
      
      .atlas25-modal-button {
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }
      
      .atlas25-modal-button.primary {
        background-color: #2196f3;
        color: white;
        border: none;
      }
      
      .atlas25-modal-button.secondary {
        background-color: #f5f5f5;
        color: #333;
        border: 1px solid #ddd;
      }
      
      .atlas25-modal-button.danger {
        background-color: #f44336;
        color: white;
        border: none;
      }
      
      @keyframes atlas25-fade-in {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }
      
      @keyframes atlas25-fade-out {
        from {
          opacity: 1;
        }
        to {
          opacity: 0;
        }
      }
      
      @keyframes atlas25-scale-in {
        from {
          transform: scale(0.8);
          opacity: 0;
        }
        to {
          transform: scale(1);
          opacity: 1;
        }
      }
      
      @keyframes atlas25-scale-out {
        from {
          transform: scale(1);
          opacity: 1;
        }
        to {
          transform: scale(0.8);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }
  
  // Create modal elements
  const id = `modal-${Date.now()}`;
  
  const backdrop = document.createElement('div');
  backdrop.className = 'atlas25-modal-backdrop';
  backdrop.id = `${id}-backdrop`;
  
  const modal = document.createElement('div');
  modal.className = `atlas25-modal ${size}`;
  modal.id = id;
  
  // Create header
  const header = document.createElement('div');
  header.className = 'atlas25-modal-header';
  
  const titleElement = document.createElement('h3');
  titleElement.className = 'atlas25-modal-title';
  titleElement.textContent = title;
  header.appendChild(titleElement);
  
  const closeButton = document.createElement('button');
  closeButton.className = 'atlas25-modal-close';
  closeButton.innerHTML = '&times;';
  closeButton.addEventListener('click', () => {
    closeModal(id);
  });
  header.appendChild(closeButton);
  
  modal.appendChild(header);
  
  // Create body
  const body = document.createElement('div');
  body.className = 'atlas25-modal-body';
  
  if (typeof content === 'string') {
    body.innerHTML = content;
  } else {
    body.appendChild(content);
  }
  
  modal.appendChild(body);
  
  // Create footer with buttons
  if (buttons.length > 0) {
    const footer = document.createElement('div');
    footer.className = 'atlas25-modal-footer';
    
    buttons.forEach(button => {
      const buttonElement = document.createElement('button');
      buttonElement.className = `atlas25-modal-button ${button.type || 'secondary'}`;
      buttonElement.textContent = button.text;
      
      if (button.onClick) {
        buttonElement.addEventListener('click', () => {
          button.onClick();
          
          if (button.closeOnClick !== false) {
            closeModal(id);
          }
        });
      } else {
        buttonElement.addEventListener('click', () => {
          closeModal(id);
        });
      }
      
      footer.appendChild(buttonElement);
    });
    
    modal.appendChild(footer);
  }
  
  // Add to container
  backdrop.appendChild(modal);
  container.appendChild(backdrop);
  
  // Close on backdrop click if enabled
  if (closeOnBackdrop) {
    backdrop.addEventListener('click', (event) => {
      if (event.target === backdrop) {
        closeModal(id);
      }
    });
  }
  
  // Close on escape key if enabled
  if (closeOnEscape) {
    const escapeHandler = (event) => {
      if (event.key === 'Escape') {
        closeModal(id);
        document.removeEventListener('keydown', escapeHandler);
      }
    };
    
    document.addEventListener('keydown', escapeHandler);
  }
  
  // Return control object
  return {
    id,
    close: () => closeModal(id),
    update: (newContent) => {
      const bodyElement = modal.querySelector('.atlas25-modal-body');
      
      if (bodyElement) {
        if (typeof newContent === 'string') {
          bodyElement.innerHTML = newContent;
        } else {
          bodyElement.innerHTML = '';
          bodyElement.appendChild(newContent);
        }
      }
    }
  };
}

/**
 * Close a modal
 * @param {string} id - Modal ID
 */
export function closeModal(id) {
  const modal = document.getElementById(id);
  const backdrop = document.getElementById(`${id}-backdrop`);
  
  if (modal && backdrop) {
    modal.classList.add('closing');
    backdrop.classList.add('closing');
    
    // Remove after animation
    backdrop.addEventListener('animationend', () => {
      backdrop.remove();
    });
  }
}

/**
 * Format a date
 * @param {Date|string|number} date - Date to format
 * @param {Object} [options={}] - Format options
 * @param {string} [options.format='medium'] - Format type ('short', 'medium', 'long', 'full')
 * @param {boolean} [options.includeTime=false] - Whether to include time
 * @param {string} [options.locale] - Locale to use (defaults to browser locale)
 * @returns {string} - Formatted date
 */
export function formatDate(date, options = {}) {
  const { format = 'medium', includeTime = false, locale } = options;
  
  // Convert to Date object if needed
  const dateObj = date instanceof Date ? date : new Date(date);
  
  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    console.error('Invalid date:', date);
    return 'Invalid date';
  }
  
  // Format options
  const dateFormatOptions = {};
  
  switch (format) {
    case 'short':
      dateFormatOptions.year = 'numeric';
      dateFormatOptions.month = 'numeric';
      dateFormatOptions.day = 'numeric';
      break;
      
    case 'medium':
      dateFormatOptions.year = 'numeric';
      dateFormatOptions.month = 'short';
      dateFormatOptions.day = 'numeric';
      break;
      
    case 'long':
      dateFormatOptions.year = 'numeric';
      dateFormatOptions.month = 'long';
      dateFormatOptions.day = 'numeric';
      break;
      
    case 'full':
      dateFormatOptions.year = 'numeric';
      dateFormatOptions.month = 'long';
      dateFormatOptions.day = 'numeric';
      dateFormatOptions.weekday = 'long';
      break;
      
    default:
      dateFormatOptions.year = 'numeric';
      dateFormatOptions.month = 'short';
      dateFormatOptions.day = 'numeric';
      break;
  }
  
  // Add time if requested
  if (includeTime) {
    dateFormatOptions.hour = 'numeric';
    dateFormatOptions.minute = 'numeric';
  }
  
  // Format date
  return dateObj.toLocaleDateString(locale, dateFormatOptions);
}

/**
 * Format a number
 * @param {number} number - Number to format
 * @param {Object} [options={}] - Format options
 * @param {number} [options.decimals] - Number of decimal places
 * @param {string} [options.decimalSeparator='.'] - Decimal separator
 * @param {string} [options.thousandsSeparator=','] - Thousands separator
 * @param {string} [options.prefix=''] - Prefix
 * @param {string} [options.suffix=''] - Suffix
 * @returns {string} - Formatted number
 */
export function formatNumber(number, options = {}) {
  const { 
    decimals, 
    decimalSeparator = '.', 
    thousandsSeparator = ',',
    prefix = '',
    suffix = ''
  } = options;
  
  // Check if number is valid
  if (isNaN(number)) {
    console.error('Invalid number:', number);
    return 'Invalid number';
  }
  
  // Format number
  let formatted = number;
  
  // Apply decimal places if specified
  if (decimals !== undefined) {
    formatted = number.toFixed(decimals);
  }
  
  // Split into integer and decimal parts
  const parts = formatted.toString().split('.');
  
  // Add thousands separator to integer part
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
  
  // Join with decimal separator
  formatted = parts.join(decimalSeparator);
  
  // Add prefix and suffix
  return `${prefix}${formatted}${suffix}`;
}

/**
 * Debounce a function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
export function debounce(func, wait) {
  let timeout;
  
  return function(...args) {
    const context = this;
    
    clearTimeout(timeout);
    
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
}

/**
 * Throttle a function
 * @param {Function} func - Function to throttle
 * @param {number} limit - Limit in milliseconds
 * @returns {Function} - Throttled function
 */
export function throttle(func, limit) {
  let inThrottle;
  
  return function(...args) {
    const context = this;
    
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}