/**
 * @file export-helpers.js
 * @description Helper functions for exporting data.
 * @module utils/export
 */

/**
 * Converts an array of objects to a CSV string.
 * @param {Array<Object>} data - The data to convert.
 * @returns {string} The CSV string.
 */
function convertToCSV(data) {
  if (!data || data.length === 0) {
    return '';
  }

  const headers = Object.keys(data[0]);
  const rows = data.map(obj => 
    headers.map(header => {
      let field = obj[header] === null || obj[header] === undefined ? '' : obj[header];
      field = String(field).replace(/"/g, '""');
      if (/[",\n]/.test(field)) {
        field = `"${field}"`;
      }
      return field;
    }).join(',')
  );

  return [headers.join(','), ...rows].join('\n');
}

/**
 * Triggers a download of a CSV file.
 * @param {Array<Object>} data - The data to export.
 * @param {string} filename - The name of the file to download.
 */
export function exportToCSV(data, filename = 'export.csv') {
  const csvString = convertToCSV(data);
  if (!csvString) {
    console.warn('No data to export.');
    return;
  }

  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}