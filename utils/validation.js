/**
 * @file validation.js
 * @description Validation utility functions for Atlas25 Web App
 * @module utils/validation
 */

/**
 * Validate an email address
 * @param {string} email - Email address to validate
 * @returns {boolean} - True if email is valid
 */
export function isValidEmail(email) {
  if (!email) return false;
  
  // Basic email regex pattern
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
}

/**
 * Validate a password
 * @param {string} password - Password to validate
 * @param {Object} [options={}] - Validation options
 * @param {number} [options.minLength=8] - Minimum password length
 * @param {boolean} [options.requireUppercase=true] - Require uppercase letter
 * @param {boolean} [options.requireLowercase=true] - Require lowercase letter
 * @param {boolean} [options.requireNumber=true] - Require number
 * @param {boolean} [options.requireSpecial=false] - Require special character
 * @returns {Object} - Validation result with isValid and errors
 */
export function validatePassword(password, options = {}) {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumber = true,
    requireSpecial = false
  } = options;
  
  const errors = [];
  
  if (!password) {
    errors.push('Password is required');
    return { isValid: false, errors };
  }
  
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  
  if (requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (requireNumber && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (requireSpecial && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate a phone number
 * @param {string} phone - Phone number to validate
 * @param {string} [countryCode='US'] - Country code for validation
 * @returns {boolean} - True if phone number is valid
 */
export function isValidPhone(phone, countryCode = 'US') {
  if (!phone) return false;
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Basic validation based on country code
  switch (countryCode) {
    case 'US':
    case 'CA':
      // North American Numbering Plan: 10 digits
      return digits.length === 10 || (digits.length === 11 && digits.charAt(0) === '1');
      
    case 'UK':
      // UK: 10 or 11 digits
      return digits.length === 10 || digits.length === 11;
      
    case 'AU':
      // Australia: 10 digits
      return digits.length === 10;
      
    default:
      // Default: 7-15 digits
      return digits.length >= 7 && digits.length <= 15;
  }
}

/**
 * Validate a URL
 * @param {string} url - URL to validate
 * @param {Object} [options={}] - Validation options
 * @param {boolean} [options.requireProtocol=true] - Require protocol (http/https)
 * @param {Array<string>} [options.allowedProtocols=['http:', 'https:']] - Allowed protocols
 * @returns {boolean} - True if URL is valid
 */
export function isValidUrl(url, options = {}) {
  const {
    requireProtocol = true,
    allowedProtocols = ['http:', 'https:']
  } = options;
  
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    
    if (requireProtocol && !allowedProtocols.includes(urlObj.protocol)) {
      return false;
    }
    
    return true;
  } catch (error) {
    // If URL constructor throws, the URL is invalid
    return false;
  }
}

/**
 * Validate a date
 * @param {Date|string} date - Date to validate
 * @param {Object} [options={}] - Validation options
 * @param {Date|string} [options.minDate] - Minimum date
 * @param {Date|string} [options.maxDate] - Maximum date
 * @returns {boolean} - True if date is valid
 */
export function isValidDate(date, options = {}) {
  const { minDate, maxDate } = options;
  
  // Convert to Date object if needed
  const dateObj = date instanceof Date ? date : new Date(date);
  
  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    return false;
  }
  
  // Check min date
  if (minDate) {
    const minDateObj = minDate instanceof Date ? minDate : new Date(minDate);
    if (dateObj < minDateObj) {
      return false;
    }
  }
  
  // Check max date
  if (maxDate) {
    const maxDateObj = maxDate instanceof Date ? maxDate : new Date(maxDate);
    if (dateObj > maxDateObj) {
      return false;
    }
  }
  
  return true;
}

/**
 * Validate a credit card number using Luhn algorithm
 * @param {string} cardNumber - Credit card number to validate
 * @returns {boolean} - True if credit card number is valid
 */
export function isValidCreditCard(cardNumber) {
  if (!cardNumber) return false;
  
  // Remove all non-digit characters
  const digits = cardNumber.replace(/\D/g, '');
  
  // Check length (most credit cards are 13-19 digits)
  if (digits.length < 13 || digits.length > 19) {
    return false;
  }
  
  // Luhn algorithm
  let sum = 0;
  let shouldDouble = false;
  
  // Loop through digits in reverse order
  for (let i = digits.length - 1; i >= 0; i--) {
    let digit = parseInt(digits.charAt(i), 10);
    
    if (shouldDouble) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    shouldDouble = !shouldDouble;
  }
  
  return sum % 10 === 0;
}

/**
 * Validate a form field
 * @param {string} value - Field value
 * @param {Object} rules - Validation rules
 * @returns {Object} - Validation result with isValid and errors
 */
export function validateField(value, rules) {
  const errors = [];
  
  // Required validation
  if (rules.required && (!value || value.trim() === '')) {
    errors.push(rules.requiredMessage || 'This field is required');
  }
  
  // Skip other validations if value is empty and not required
  if (!value && !rules.required) {
    return { isValid: true, errors: [] };
  }
  
  // Minimum length validation
  if (rules.minLength && value.length < rules.minLength) {
    errors.push(rules.minLengthMessage || `Must be at least ${rules.minLength} characters`);
  }
  
  // Maximum length validation
  if (rules.maxLength && value.length > rules.maxLength) {
    errors.push(rules.maxLengthMessage || `Must be no more than ${rules.maxLength} characters`);
  }
  
  // Pattern validation
  if (rules.pattern && !rules.pattern.test(value)) {
    errors.push(rules.patternMessage || 'Invalid format');
  }
  
  // Email validation
  if (rules.email && !isValidEmail(value)) {
    errors.push(rules.emailMessage || 'Invalid email address');
  }
  
  // URL validation
  if (rules.url && !isValidUrl(value, rules.urlOptions)) {
    errors.push(rules.urlMessage || 'Invalid URL');
  }
  
  // Phone validation
  if (rules.phone && !isValidPhone(value, rules.countryCode)) {
    errors.push(rules.phoneMessage || 'Invalid phone number');
  }
  
  // Custom validation
  if (rules.validate && typeof rules.validate === 'function') {
    const customResult = rules.validate(value);
    
    if (customResult !== true) {
      errors.push(customResult || rules.validateMessage || 'Invalid value');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate a form
 * @param {Object} values - Form values
 * @param {Object} validationSchema - Validation schema
 * @returns {Object} - Validation result with isValid, errors, and firstError
 */
export function validateForm(values, validationSchema) {
  const errors = {};
  let isValid = true;
  let firstError = null;
  
  // Validate each field
  Object.keys(validationSchema).forEach(field => {
    const rules = validationSchema[field];
    const value = values[field];
    
    const result = validateField(value, rules);
    
    if (!result.isValid) {
      errors[field] = result.errors;
      isValid = false;
      
      if (!firstError) {
        firstError = {
          field,
          message: result.errors[0]
        };
      }
    }
  });
  
  return {
    isValid,
    errors,
    firstError
  };
}

/**
 * Format a credit card number with spaces
 * @param {string} cardNumber - Credit card number to format
 * @returns {string} - Formatted credit card number
 */
export function formatCreditCard(cardNumber) {
  if (!cardNumber) return '';
  
  // Remove all non-digit characters
  const digits = cardNumber.replace(/\D/g, '');
  
  // Format based on card type
  if (/^3[47]/.test(digits)) {
    // American Express: 4-6-5
    return digits.replace(/^(\d{4})(\d{6})(\d{5})$/, '$1 $2 $3');
  } else {
    // Other cards: 4-4-4-4
    return digits.replace(/(\d{4})(?=\d)/g, '$1 ').trim();
  }
}

/**
 * Format a phone number
 * @param {string} phone - Phone number to format
 * @param {string} [countryCode='US'] - Country code for formatting
 * @returns {string} - Formatted phone number
 */
export function formatPhone(phone, countryCode = 'US') {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Format based on country code
  switch (countryCode) {
    case 'US':
    case 'CA':
      // North American format: (XXX) XXX-XXXX
      if (digits.length === 10) {
        return digits.replace(/^(\d{3})(\d{3})(\d{4})$/, '($1) $2-$3');
      } else if (digits.length === 11 && digits.charAt(0) === '1') {
        return digits.replace(/^1(\d{3})(\d{3})(\d{4})$/, '1 ($1) $2-$3');
      }
      break;
      
    case 'UK':
      // UK format varies by number type
      if (digits.length === 11 && digits.startsWith('07')) {
        // Mobile: 07XXX XXXXXX
        return digits.replace(/^(\d{5})(\d{6})$/, '$1 $2');
      } else if (digits.length === 10) {
        // Landline: 02X XXXX XXXX
        return digits.replace(/^(\d{3})(\d{4})(\d{3})$/, '$1 $2 $3');
      }
      break;
      
    case 'AU':
      // Australia format: XXXX XXX XXX
      if (digits.length === 10) {
        return digits.replace(/^(\d{4})(\d{3})(\d{3})$/, '$1 $2 $3');
      }
      break;
  }
  
  // Default: group in 3s
  return digits.replace(/(\d{3})(?=\d)/g, '$1 ').trim();
}

/**
 * Sanitize HTML to prevent XSS
 * @param {string} html - HTML to sanitize
 * @returns {string} - Sanitized HTML
 */
export function sanitizeHtml(html) {
  if (!html) return '';
  
  // Create a temporary element
  const tempElement = document.createElement('div');
  
  // Set the HTML content
  tempElement.textContent = html;
  
  // Return the sanitized HTML
  return tempElement.innerHTML;
}

/**
 * Escape HTML special characters
 * @param {string} text - Text to escape
 * @returns {string} - Escaped text
 */
export function escapeHtml(text) {
  if (!text) return '';
  
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * Validate a file
 * @param {File} file - File to validate
 * @param {Object} [options={}] - Validation options
 * @param {number} [options.maxSize] - Maximum file size in bytes
 * @param {Array<string>} [options.allowedTypes] - Allowed MIME types
 * @param {Array<string>} [options.allowedExtensions] - Allowed file extensions
 * @returns {Object} - Validation result with isValid and errors
 */
export function validateFile(file, options = {}) {
  const { maxSize, allowedTypes, allowedExtensions } = options;
  const errors = [];
  
  if (!file) {
    errors.push('No file provided');
    return { isValid: false, errors };
  }
  
  // Check file size
  if (maxSize && file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024) * 10) / 10;
    errors.push(`File size exceeds maximum allowed size (${maxSizeMB} MB)`);
  }
  
  // Check file type
  if (allowedTypes && allowedTypes.length > 0) {
    if (!allowedTypes.includes(file.type)) {
      errors.push(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`);
    }
  }
  
  // Check file extension
  if (allowedExtensions && allowedExtensions.length > 0) {
    const extension = file.name.split('.').pop().toLowerCase();
    
    if (!allowedExtensions.includes(extension)) {
      errors.push(`File extension not allowed. Allowed extensions: ${allowedExtensions.join(', ')}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}