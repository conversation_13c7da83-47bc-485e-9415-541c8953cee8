/**
 * @file api-helpers.js
 * @description API helper functions for Atlas25 Web App
 * @module utils/api-helpers
 */

import firebaseService from '../core/firebase/firebase-service.js';
import offlineManager from '../core/offline/offline-manager.js';

/**
 * Base API URL
 * @type {string}
 */
const API_BASE_URL = 'https://api.atlas25.nba.com';

/**
 * Default request options
 * @type {Object}
 */
const DEFAULT_OPTIONS = {
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 30000, // 30 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
  useFirebase: true,
  offlineSupport: true
};

/**
 * Make an API request
 * @param {string} endpoint - API endpoint
 * @param {Object} [options={}] - Request options
 * @returns {Promise<any>} - Response data
 */
export async function apiRequest(endpoint, options = {}) {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const { method = 'GET', body, headers, timeout, retries, retryDelay, useFirebase, offlineSupport } = mergedOptions;
  
  // Check if we should use Firebase
  if (useFirebase && endpoint.startsWith('/')) {
    return firebaseRequest(endpoint, mergedOptions);
  }
  
  // Check if we're offline and this request supports offline mode
  if (!navigator.onLine && offlineSupport) {
    return handleOfflineRequest(endpoint, mergedOptions);
  }
  
  // Prepare URL
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
  
  // Prepare request options
  const fetchOptions = {
    method,
    headers: { ...DEFAULT_OPTIONS.headers, ...headers },
    ...(body && { body: typeof body === 'string' ? body : JSON.stringify(body) })
  };
  
  // Make request with timeout and retries
  return fetchWithTimeoutAndRetries(url, fetchOptions, timeout, retries, retryDelay);
}

/**
 * Make a Firebase request
 * @param {string} endpoint - Firebase endpoint
 * @param {Object} options - Request options
 * @returns {Promise<any>} - Response data
 * @private
 */
async function firebaseRequest(endpoint, options) {
  const { method = 'GET', body } = options;
  
  // Parse endpoint to get collection and document ID
  const parts = endpoint.split('/').filter(Boolean);
  const collection = parts[0];
  const docId = parts[1];
  
  try {
    switch (method) {
      case 'GET':
        if (docId) {
          return await firebaseService.getDocument(collection, docId);
        } else {
          return await firebaseService.getCollection(collection);
        }
        
      case 'POST':
        return await firebaseService.addDocument(collection, body);
        
      case 'PUT':
      case 'PATCH':
        if (!docId) {
          throw new Error('Document ID is required for PUT/PATCH requests');
        }
        return await firebaseService.updateDocument(collection, docId, body);
        
      case 'DELETE':
        if (!docId) {
          throw new Error('Document ID is required for DELETE requests');
        }
        return await firebaseService.deleteDocument(collection, docId);
        
      default:
        throw new Error(`Unsupported method: ${method}`);
    }
  } catch (error) {
    console.error(`Firebase request failed: ${endpoint}`, error);
    throw error;
  }
}

/**
 * Handle an offline request
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Request options
 * @returns {Promise<any>} - Response data
 * @private
 */
async function handleOfflineRequest(endpoint, options) {
  const { method, body } = options;
  
  // For GET requests, try to get from cache
  if (method === 'GET') {
    // Try to get from cache
    const cachedResponse = await getCachedResponse(endpoint);
    
    if (cachedResponse) {
      return {
        ...cachedResponse,
        _fromCache: true
      };
    }
    
    throw new Error('No cached data available and device is offline');
  }
  
  // For other methods, queue the request for later
  const operation = {
    endpoint: 'api',
    method,
    data: {
      url: endpoint,
      body,
      headers: options.headers
    }
  };
  
  await offlineManager.queueOperation(operation);
  
  return {
    success: true,
    queued: true,
    message: 'Request queued for processing when online'
  };
}

/**
 * Get a cached response
 * @param {string} endpoint - API endpoint
 * @returns {Promise<any|null>} - Cached response or null if not found
 * @private
 */
async function getCachedResponse(endpoint) {
  try {
    const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
    
    // Try to get from cache
    const cache = await caches.open('atlas25-api-cache');
    const response = await cache.match(url);
    
    if (!response) {
      return null;
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Failed to get cached response:', error);
    return null;
  }
}

/**
 * Fetch with timeout and retries
 * @param {string} url - URL to fetch
 * @param {Object} options - Fetch options
 * @param {number} timeout - Timeout in milliseconds
 * @param {number} retries - Number of retries
 * @param {number} retryDelay - Delay between retries in milliseconds
 * @returns {Promise<any>} - Response data
 * @private
 */
async function fetchWithTimeoutAndRetries(url, options, timeout, retries, retryDelay) {
  // Create abort controller for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  // Add signal to options
  const fetchOptions = {
    ...options,
    signal: controller.signal
  };
  
  try {
    // Try to fetch with retries
    return await fetchWithRetries(url, fetchOptions, retries, retryDelay);
  } finally {
    // Clear timeout
    clearTimeout(timeoutId);
  }
}

/**
 * Fetch with retries
 * @param {string} url - URL to fetch
 * @param {Object} options - Fetch options
 * @param {number} retries - Number of retries
 * @param {number} retryDelay - Delay between retries in milliseconds
 * @returns {Promise<any>} - Response data
 * @private
 */
async function fetchWithRetries(url, options, retries, retryDelay) {
  try {
    const response = await fetch(url, options);
    
    // Cache successful GET requests
    if (options.method === 'GET' && response.ok) {
      await cacheResponse(url, response.clone());
    }
    
    // Check if response is OK
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Request failed with status ${response.status}`);
    }
    
    // Parse response
    return await response.json();
  } catch (error) {
    // If we have retries left and it's not an abort error, retry
    if (retries > 0 && error.name !== 'AbortError') {
      console.log(`Retrying request to ${url} (${retries} retries left)`);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      
      // Retry with exponential backoff
      return fetchWithRetries(url, options, retries - 1, retryDelay * 2);
    }
    
    // No retries left or abort error, throw
    throw error;
  }
}

/**
 * Cache a response
 * @param {string} url - URL to cache
 * @param {Response} response - Response to cache
 * @returns {Promise<void>}
 * @private
 */
async function cacheResponse(url, response) {
  try {
    const cache = await caches.open('atlas25-api-cache');
    await cache.put(url, response);
  } catch (error) {
    console.error('Failed to cache response:', error);
  }
}

/**
 * Get data from API
 * @param {string} endpoint - API endpoint
 * @param {Object} [options={}] - Request options
 * @returns {Promise<any>} - Response data
 */
export async function getData(endpoint, options = {}) {
  return apiRequest(endpoint, {
    method: 'GET',
    ...options
  });
}

/**
 * Post data to API
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Data to post
 * @param {Object} [options={}] - Request options
 * @returns {Promise<any>} - Response data
 */
export async function postData(endpoint, data, options = {}) {
  return apiRequest(endpoint, {
    method: 'POST',
    body: data,
    ...options
  });
}

/**
 * Put data to API
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Data to put
 * @param {Object} [options={}] - Request options
 * @returns {Promise<any>} - Response data
 */
export async function putData(endpoint, data, options = {}) {
  return apiRequest(endpoint, {
    method: 'PUT',
    body: data,
    ...options
  });
}

/**
 * Patch data to API
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Data to patch
 * @param {Object} [options={}] - Request options
 * @returns {Promise<any>} - Response data
 */
export async function patchData(endpoint, data, options = {}) {
  return apiRequest(endpoint, {
    method: 'PATCH',
    body: data,
    ...options
  });
}

/**
 * Delete data from API
 * @param {string} endpoint - API endpoint
 * @param {Object} [options={}] - Request options
 * @returns {Promise<any>} - Response data
 */
export async function deleteData(endpoint, options = {}) {
  return apiRequest(endpoint, {
    method: 'DELETE',
    ...options
  });
}

/**
 * Upload a file
 * @param {string} endpoint - API endpoint
 * @param {File|Blob} file - File to upload
 * @param {Object} [metadata={}] - File metadata
 * @param {Object} [options={}] - Request options
 * @returns {Promise<any>} - Response data
 */
export async function uploadFile(endpoint, file, metadata = {}, options = {}) {
  // If using Firebase, use Firebase storage
  if (options.useFirebase !== false) {
    const path = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
    return firebaseService.uploadFile(path, file, metadata);
  }
  
  // Otherwise, use fetch API
  const formData = new FormData();
  formData.append('file', file);
  
  // Add metadata
  Object.entries(metadata).forEach(([key, value]) => {
    formData.append(key, value);
  });
  
  return apiRequest(endpoint, {
    method: 'POST',
    body: formData,
    headers: {}, // Let browser set content type for FormData
    ...options
  });
}