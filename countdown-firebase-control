<!-- Firebase-Based Countdown Control Strategy -->

<!-- 1. Firebase Setup -->
<!-- Create a Firebase project at https://console.firebase.google.com -->
<!-- Enable Realtime Database and set initial test rules: -->
/*
{
  "rules": {
    ".read": true,
    ".write": true
  }
}
*/

<!-- 2. Firebase Data Model -->
/*
{
  "countdownControl": {
    "status": "idle",      // "start", "pause", "resume", "reset"
    "startTimestamp": 0     // Unix timestamp in ms
  }
}
*/

<!-- 3. Dashboard Page Script (reads commands from Firebase and runs timer with smart formatting) -->
<script type="module">
  import { initializeApp } from "https://www.gstatic.com/firebasejs/10.11.0/firebase-app.js";
  import { getDatabase, ref, onValue } from "https://www.gstatic.com/firebasejs/10.11.0/firebase-database.js";

  const firebaseConfig = {
    apiKey: "YOUR_API_KEY",
    authDomain: "YOUR_DOMAIN",
    databaseURL: "YOUR_DATABASE_URL",
    projectId: "YOUR_PROJECT_ID",
    storageBucket: "YOUR_BUCKET",
    messagingSenderId: "YOUR_SENDER_ID",
    appId: "YOUR_APP_ID"
  };

  const app = initializeApp(firebaseConfig);
  const db = getDatabase(app);
  const ctrlRef = ref(db, 'countdownControl');

  let interval;
  const durationMs = 90 * 60 * 1000; // 1 hour 30 minutes
  const el = document.querySelector('.countdown-timer');

  function updateCountdown(endTime) {
    const now = Date.now();
    const remaining = endTime - now;

    if (remaining <= 0) {
      clearInterval(interval);
      el.textContent = "0 mins";
      return;
    }

    const hrs = Math.floor(remaining / 3600000);
    const mins = Math.floor((remaining % 3600000) / 60000);

    if (hrs >= 1 && mins > 0) {
      el.textContent = `${hrs} hr ${mins.toString().padStart(2, '0')} mins`;
    } else if (hrs === 1 && mins === 0) {
      el.textContent = "1 hour";
    } else {
      el.textContent = `${mins} mins`;
    }
  }

  onValue(ctrlRef, (snapshot) => {
    const data = snapshot.val();
    if (!data) return;

    if (data.status === 'start' && data.startTimestamp) {
      const endTime = data.startTimestamp + durationMs;
      clearInterval(interval);
      updateCountdown(endTime);
      interval = setInterval(() => updateCountdown(endTime), 1000);
    }

    if (data.status === 'reset') {
      clearInterval(interval);
      el.textContent = "01:30:00";
    }

    if (data.status === 'pause') {
      clearInterval(interval);
    }

    if (data.status === 'resume' && data.startTimestamp) {
      const now = Date.now();
      const elapsed = now - data.startTimestamp;
      const remaining = durationMs - elapsed;
      const newEnd = now + remaining;
      clearInterval(interval);
      updateCountdown(newEnd);
      interval = setInterval(() => updateCountdown(newEnd), 1000);
    }
  });
</script>

<!-- 4. Admin Control Page UI (buttons to trigger commands) -->
<div style="display: flex; gap: 12px; margin-top: 20px;">
  <button onclick="triggerStart()">Start</button>
  <button onclick="triggerPause()">Pause</button>
  <button onclick="triggerResume()">Resume</button>
  <button onclick="triggerReset()">Reset</button>
</div>

<!-- 5. Control Page Script -->
<script type="module">
  import { initializeApp } from "https://www.gstatic.com/firebasejs/10.11.0/firebase-app.js";
  import { getDatabase, ref, set } from "https://www.gstatic.com/firebasejs/10.11.0/firebase-database.js";

  const firebaseConfig = {
    apiKey: "YOUR_API_KEY",
    authDomain: "YOUR_DOMAIN",
    databaseURL: "YOUR_DATABASE_URL",
    projectId: "YOUR_PROJECT_ID",
    storageBucket: "YOUR_BUCKET",
    messagingSenderId: "YOUR_SENDER_ID",
    appId: "YOUR_APP_ID"
  };

  const app = initializeApp(firebaseConfig);
  const db = getDatabase(app);

  function triggerStart() {
    set(ref(db, 'countdownControl'), {
      status: "start",
      startTimestamp: Date.now()
    });
  }

  function triggerPause() {
    set(ref(db, 'countdownControl/status'), "pause");
  }

  function triggerResume() {
    set(ref(db, 'countdownControl'), {
      status: "resume",
      startTimestamp: Date.now()
    });
  }

  function triggerReset() {
    set(ref(db, 'countdownControl'), {
      status: "reset",
      startTimestamp: 0
    });
  }
</script>

<!-- Notes -->
<!-- Secure your database with authentication or write rules once working -->
<!-- Countdown display formatting rules:
     - "X hr XX mins" when over 1 hr 1 min
     - "1 hour" when exactly 1 hr 0 min
     - "XX mins" when under 1 hr
     - "0 mins" when complete -->
