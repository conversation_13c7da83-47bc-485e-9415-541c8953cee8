/**
 * @file community-impact.js
 * @description Core functionality for the Community Impact Generator
 * @module pages/community-impact
 */

import firebaseService from '../../core/firebase/firebase-service.js';
import { getCurrentUser } from '../../core/auth/auth.js';
import stateManager from '../../core/state/state-manager.js';
import { validateCause, validateHomeMarket } from '../../utils/validation.js';

/**
 * @class CommunityImpactGenerator
 * @description Handles the logic for generating and managing community impact summaries.
 */
class CommunityImpactGenerator {
  /**
   * @constructor
   * @param {object} config - Configuration options.
   * @param {string} config.llmFunctionName - The name of the Firebase Cloud Function for the LLM.
   */
  constructor(config = {}) {
    this.config = {
      llmFunctionName: 'generateImpactSummary',
      ...config,
    };

    // Ensure Firebase is initialized
    firebaseService.initialize().catch(error => {
      console.error("Failed to initialize Firebase in CommunityImpactGenerator:", error);
      stateManager.addNotification({
        message: 'Error connecting to services. Some features may not work.',
        type: 'error',
      });
    });
  }

  /**
   * Generates a community impact summary using an LLM.
   * It retrieves the current user's data, calls a cloud function,
   * and saves the result to Firestore.
   * @param {string} cause - The cause selected by the user.
   * @param {string} [customCause] - A custom cause entered by the user.
   * @returns {Promise<string>} The ID of the newly created impact summary document.
   */
  async generateImpactSummary(cause, customCause = '') {
    const user = getCurrentUser();
    if (!user) {
      throw new Error('User is not authenticated.');
    }

    const finalCause = cause === 'custom' ? customCause : cause;
    const homeMarket = user.homeMarket || 'their community';

    // Validate inputs
    if (!validateCause(finalCause) || !validateHomeMarket(homeMarket)) {
        stateManager.addNotification({ message: 'Invalid input. Please try again.', type: 'error' });
        throw new Error('Invalid input for cause or home market.');
    }

    stateManager.set('ui.communityImpact.loading', true);
    stateManager.set('ui.communityImpact.error', null);

    try {
      const payload = {
        cause: finalCause,
        homeMarket,
        userId: user.uid,
      };

      // Call the Firebase Cloud Function
      const result = await firebaseService.callFunction(this.config.llmFunctionName, payload);

      if (result.error) {
        throw new Error(result.error);
      }

      const summary = result.summary;

      // Save the summary to Firestore
      const summaryId = await this.saveImpactSummary(user.uid, {
        cause: finalCause,
        homeMarket,
        summary,
      });
      
      stateManager.set('ui.communityImpact.summary', summary);
      stateManager.set('user.communityImpactSummaryId', summaryId);

      return summaryId;
    } catch (error) {
      console.error('Error generating community impact summary:', error);
      stateManager.set('ui.communityImpact.error', 'Failed to generate summary. Please try again later.');
      stateManager.addNotification({
        message: 'An error occurred while generating your impact summary.',
        type: 'error',
      });
      // The firebaseService.callFunction handles offline queuing automatically.
      // If it fails even with that, we show an error.
      throw error;
    } finally {
      stateManager.set('ui.communityImpact.loading', false);
    }
  }

  /**
   * Saves the generated impact summary to the 'impactSummaries' collection in Firestore.
   * @param {string} userId - The ID of the user.
   * @param {object} summaryData - The data for the summary.
   * @param {string} summaryData.cause - The selected cause.
   * @param {string} summaryData.homeMarket - The user's home market.
   * @param {string} summaryData.summary - The AI-generated summary.
   * @returns {Promise<string>} The ID of the new Firestore document.
   */
  async saveImpactSummary(userId, summaryData) {
    if (!userId || !summaryData || !summaryData.summary) {
      throw new Error('Invalid data provided for saving impact summary.');
    }

    try {
      const dataToSave = {
        userId,
        ...summaryData,
        generatedAt: new Date().toISOString(),
      };
      const docId = await firebaseService.addDocument('impactSummaries', dataToSave);
      console.log(`Impact summary saved with ID: ${docId}`);
      return docId;
    } catch (error) {
      console.error('Error saving impact summary:', error);
      // firebaseService.addDocument will queue if offline
      throw error;
    }
  }

  /**
   * Retrieves a user's impact summary from Firestore.
   * @param {string} summaryId - The ID of the summary document.
   * @returns {Promise<object|null>} The summary data or null if not found.
   */
  async getImpactSummary(summaryId) {
    try {
      return await firebaseService.getDocument('impactSummaries', summaryId);
    } catch (error) {
      console.error('Error retrieving impact summary:', error);
      return null;
    }
  }
}

// Export a singleton instance
export default new CommunityImpactGenerator();