/**
 * @file community-impact_webflow-embed.js
 * @description Webflow embed for the Community Impact Generator.
 * @module pages/community-impact
 */

import communityImpactGenerator from './community-impact.js';
import stateManager from '../../core/state/state-manager.js';
import { getCurrentUser, onAuthStateChanged } from '../../core/auth/auth.js';
import { showLoading, showNotification } from '../../utils/ui-helpers.js';

/**
 * @class CommunityImpactEmbed
 * @description Manages the UI and interactions for the Community Impact Generator embed.
 *
 * @example
 * <!-- INSTRUCTIONS FOR WEBFLOW: -->
 * <!-- 1. Add a new "Embed" element to your page where you want the generator to appear. -->
 * <!-- 2. Copy and paste the following HTML into the embed element: -->
 * <!--
 * <div id="community-impact-container"></div>
 * <script type="module">
 *   import CommunityImpactEmbed from './pages/community-impact/community-impact_webflow-embed.js';
 *
 *   document.addEventListener('DOMContentLoaded', () => {
 *     const embed = new CommunityImpactEmbed({
 *       elementId: 'community-impact-container',
 *       causes: [
 *         { value: 'youth-development', label: 'Youth Development' },
 *         { value: 'health-wellness', label: 'Health & Wellness' },
 *         { value: 'environmental-sustainability', label: 'Environmental Sustainability' },
 *         { value: 'social-justice', label: 'Social Justice' },
 *         { value: 'custom', label: 'Other...' }
 *       ]
 *     });
 *     embed.init();
 *   });
 * </script>
 * -->
 * <!-- 3. Make sure the path in the import statement is correct based on your project structure. -->
 * <!-- 4. Publish your Webflow site. The component will render in the specified container. -->
 */
class CommunityImpactEmbed {
  /**
   * @constructor
   * @param {object} config - Configuration for the embed.
   * @param {string} config.elementId - The ID of the DOM element to render the component in.
   * @param {Array<object>} config.causes - A list of causes for the dropdown.
   */
  constructor(config) {
    this.config = {
      elementId: 'community-impact-container',
      causes: [],
      ...config,
    };
    this.container = document.getElementById(this.config.elementId);
    this.loadingSpinner = null;
    this.unsubscribeAuthState = null;
    this.unsubscribeState = null;
  }

  /**
   * Initializes the component, renders the UI, and sets up event listeners.
   */
  init() {
    if (!this.container) {
      console.error(`Element with ID #${this.config.elementId} not found.`);
      return;
    }
    this.render();
    this.attachEventListeners();
    this.subscribeToStateChanges();
  }

  /**
   * Renders the initial HTML structure of the component.
   */
  render() {
    const user = getCurrentUser();
    const homeMarket = user ? user.homeMarket || 'your community' : 'your community';

    this.container.innerHTML = `
      <div class="community-impact-generator">
        <h2>Make Your Impact</h2>
        <p>Select a cause you're passionate about to see how you can make a difference in ${homeMarket}.</p>
        
        <form id="impact-form">
          <div class="form-group">
            <label for="cause-select">Choose a Cause:</label>
            <select id="cause-select" name="cause" required>
              ${this.config.causes.map(cause => `<option value="${cause.value}">${cause.label}</option>`).join('')}
            </select>
          </div>
          <div class="form-group" id="custom-cause-group" style="display: none;">
            <label for="custom-cause-input">Please specify:</label>
            <input type="text" id="custom-cause-input" name="customCause" placeholder="e.g., Animal Welfare">
          </div>
          <button type="submit" id="generate-btn">Generate My Impact</button>
        </form>

        <div id="impact-result-container" style="display: none;">
          <h3>Your Personalized Impact Summary</h3>
          <div id="impact-summary" class="summary-content"></div>
        </div>

        <div id="loading-container"></div>
      </div>
    `;
  }

  /**
   * Attaches event listeners to the UI elements.
   */
  attachEventListeners() {
    const form = this.container.querySelector('#impact-form');
    const causeSelect = this.container.querySelector('#cause-select');

    form.addEventListener('submit', this.handleSubmit.bind(this));
    causeSelect.addEventListener('change', this.handleCauseChange.bind(this));
  }

  /**
   * Subscribes to authentication and application state changes.
   */
  subscribeToStateChanges() {
    this.unsubscribeAuthState = onAuthStateChanged(user => {
      if (!user) {
        this.container.innerHTML = '<p>Please sign in to use the Community Impact Generator.</p>';
      } else {
        this.render(); // Re-render to update home market text
        this.attachEventListeners();
      }
    });

    this.unsubscribeState = stateManager.subscribe(this.updateUI.bind(this));
  }

  /**
   * Handles changes in the cause selection dropdown.
   * @param {Event} e - The change event.
   */
  handleCauseChange(e) {
    const customCauseGroup = this.container.querySelector('#custom-cause-group');
    customCauseGroup.style.display = e.target.value === 'custom' ? 'block' : 'none';
  }

  /**
   * Handles the form submission to generate an impact summary.
   * @param {Event} e - The submit event.
   */
  async handleSubmit(e) {
    e.preventDefault();
    const form = e.target;
    const cause = form.elements.cause.value;
    const customCause = form.elements.customCause.value;

    if (cause === 'custom' && !customCause.trim()) {
      showNotification('Please enter your custom cause.', { type: 'warning' });
      return;
    }

    try {
      await communityImpactGenerator.generateImpactSummary(cause, customCause);
      showNotification('Your impact summary has been generated!', { type: 'success' });
    } catch (error) {
      // Error notification is handled in the core class
    }
  }

  /**
   * Updates the UI based on the current application state.
   * @param {object} state - The current application state from the state manager.
   */
  updateUI(state) {
    const loading = state.ui.communityImpact?.loading;
    const summary = state.ui.communityImpact?.summary;
    const error = state.ui.communityImpact?.error;

    const loadingContainer = this.container.querySelector('#loading-container');
    const resultContainer = this.container.querySelector('#impact-result-container');
    const summaryDiv = this.container.querySelector('#impact-summary');
    const generateBtn = this.container.querySelector('#generate-btn');

    // Handle loading state
    if (loading && !this.loadingSpinner) {
      this.loadingSpinner = showLoading(loadingContainer, { message: 'Generating summary...' });
      generateBtn.disabled = true;
    } else if (!loading && this.loadingSpinner) {
      this.loadingSpinner.hide();
      this.loadingSpinner = null;
      generateBtn.disabled = false;
    }

    // Display summary
    if (summary) {
      summaryDiv.textContent = summary;
      resultContainer.style.display = 'block';
    }

    // Display error
    if (error) {
        // Notification is already shown by the core logic.
        // We could optionally display an error message in the component itself.
    }
  }

  /**
   * Cleans up subscriptions when the component is destroyed.
   */
  destroy() {
    if (this.unsubscribeAuthState) this.unsubscribeAuthState();
    if (this.unsubscribeState) this.unsubscribeState();
  }
}

export default CommunityImpactEmbed;