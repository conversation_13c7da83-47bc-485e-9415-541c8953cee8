/**
 * @file onboarding_webflow-embed.js
 * @description Webflow embed for onboarding functionality
 * 
 * HOW TO USE:
 * 1. Add this code to a Webflow embed element
 * 2. Ensure you have the following elements with these specific classes:
 *    - .onboarding-container: Container for the onboarding process
 *    - .onboarding-progress: (Optional) Progress indicator container
 * 3. Add the following CSS to your Webflow page:
 *    .onboarding-step { display: none; }
 *    .onboarding-step.active { display: block; }
 */

// Import Onboarding (in production, this would be loaded from a CDN)
// import Onboarding from './onboarding.js';

// Placeholder for imported Onboarding class
class Onboarding {
  constructor(options = {}) {
    this.options = options;
    this.container = typeof options.container === 'string' 
      ? document.querySelector(options.container) 
      : options.container;
      
    this.steps = [
      'welcome',
      'profile',
      'preferences',
      'complete'
    ];
    
    this.currentStepIndex = 0;
    this.userData = {
      profile: {},
      preferences: {}
    };
  }
  
  async initialize() {
    // Render first step
    this.renderStep(this.steps[this.currentStepIndex]);
    console.log('Onboarding initialized');
  }
  
  renderStep(stepId) {
    if (!this.container) return;
    
    // Hide all steps
    const allSteps = this.container.querySelectorAll('.onboarding-step');
    allSteps.forEach(step => {
      step.classList.remove('active');
    });
    
    // Show current step
    const currentStep = this.container.querySelector(`.onboarding-step[data-step="${stepId}"]`);
    if (currentStep) {
      currentStep.classList.add('active');
    }
    
    // Update progress indicator
    this.updateProgress();
  }
  
  updateProgress() {
    const progressContainer = document.querySelector('.onboarding-progress');
    
    if (!progressContainer) return;
    
    // Clear progress container
    progressContainer.innerHTML = '';
    
    // Create progress steps
    this.steps.forEach((step, index) => {
      const stepElement = document.createElement('div');
      stepElement.className = 'onboarding-progress-step';
      
      if (index < this.currentStepIndex) {
        stepElement.classList.add('completed');
      } else if (index === this.currentStepIndex) {
        stepElement.classList.add('active');
      }
      
      stepElement.addEventListener('click', () => {
        // Only allow going back to previous steps
        if (index <= this.currentStepIndex) {
          this.goToStep(index);
        }
      });
      
      progressContainer.appendChild(stepElement);
    });
  }
  
  nextStep() {
    if (this.currentStepIndex < this.steps.length - 1) {
      this.currentStepIndex++;
      this.renderStep(this.steps[this.currentStepIndex]);
    }
  }
  
  prevStep() {
    if (this.currentStepIndex > 0) {
      this.currentStepIndex--;
      this.renderStep(this.steps[this.currentStepIndex]);
    }
  }
  
  goToStep(index) {
    if (index >= 0 && index < this.steps.length) {
      this.currentStepIndex = index;
      this.renderStep(this.steps[this.currentStepIndex]);
    }
  }
}

/**
 * Initialize the onboarding process in Webflow
 */
function initOnboardingWebflow() {
  try {
    console.log('Initializing onboarding in Webflow');
    
    // Find onboarding container
    const container = document.querySelector('.onboarding-container');
    
    if (!container) {
      console.warn('No onboarding container found');
      return;
    }
    
    // Create onboarding instance
    const onboarding = new Onboarding({
      container,
      onComplete: handleComplete,
      onStepChange: handleStepChange
    });
    
    // Store onboarding instance on container for later access
    container.onboarding = onboarding;
    
    // Initialize onboarding
    onboarding.initialize();
    
    // Set up event listeners for navigation buttons
    setupEventListeners(container, onboarding);
    
    console.log('Onboarding initialized');
    
    // Handle onboarding completion
    function handleComplete() {
      // Dispatch custom event
      const event = new CustomEvent('onboarding-complete', { 
        bubbles: true 
      });
      container.dispatchEvent(event);
      
      // Redirect to dashboard or home page
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 2000);
    }
    
    // Handle step change
    function handleStepChange(data) {
      // Dispatch custom event
      const event = new CustomEvent('onboarding-step-change', { 
        detail: data,
        bubbles: true 
      });
      container.dispatchEvent(event);
    }
  } catch (error) {
    console.error('Failed to initialize onboarding:', error);
  }
}

/**
 * Set up event listeners for navigation buttons
 * @param {HTMLElement} container - Onboarding container
 * @param {Onboarding} onboarding - Onboarding instance
 */
function setupEventListeners(container, onboarding) {
  // Next buttons
  const nextButtons = container.querySelectorAll('.onboarding-button.next:not([type="submit"])');
  nextButtons.forEach(button => {
    button.addEventListener('click', () => {
      onboarding.nextStep();
    });
  });
  
  // Previous buttons
  const prevButtons = container.querySelectorAll('.onboarding-button.prev');
  prevButtons.forEach(button => {
    button.addEventListener('click', () => {
      onboarding.prevStep();
    });
  });
  
  // Finish button
  const finishButton = container.querySelector('.onboarding-button.finish');
  if (finishButton) {
    finishButton.addEventListener('click', () => {
      // Show loading state
      finishButton.textContent = 'Saving...';
      finishButton.disabled = true;
      
      // Simulate saving data
      setTimeout(() => {
        if (onboarding.options.onComplete) {
          onboarding.options.onComplete();
        }
      }, 1500);
    });
  }
  
  // Form submissions
  const forms = container.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', (event) => {
      event.preventDefault();
      
      // Get step ID
      const step = form.closest('.onboarding-step');
      const stepId = step ? step.getAttribute('data-step') : null;
      
      if (stepId === 'profile') {
        handleProfileSubmit(form, onboarding);
      } else if (stepId === 'preferences') {
        handlePreferencesSubmit(form, onboarding);
      } else {
        onboarding.nextStep();
      }
    });
  });
}

/**
 * Handle profile form submission
 * @param {HTMLFormElement} form - Profile form
 * @param {Onboarding} onboarding - Onboarding instance
 */
function handleProfileSubmit(form, onboarding) {
  // Get form data
  const formData = new FormData(form);
  const profileData = {};
  
  for (const [key, value] of formData.entries()) {
    profileData[key] = value;
  }
  
  // Basic validation
  let isValid = true;
  const requiredFields = ['fullName', 'position', 'team'];
  
  requiredFields.forEach(field => {
    const input = form.querySelector(`[name="${field}"]`);
    const errorElement = input ? input.nextElementSibling : null;
    
    if (!profileData[field]) {
      isValid = false;
      
      if (errorElement) {
        errorElement.textContent = 'This field is required';
        errorElement.style.display = 'block';
      }
    } else if (errorElement) {
      errorElement.style.display = 'none';
    }
  });
  
  if (!isValid) {
    return;
  }
  
  // Update user data
  onboarding.userData.profile = {
    ...onboarding.userData.profile,
    ...profileData
  };
  
  // Go to next step
  onboarding.nextStep();
}

/**
 * Handle preferences form submission
 * @param {HTMLFormElement} form - Preferences form
 * @param {Onboarding} onboarding - Onboarding instance
 */
function handlePreferencesSubmit(form, onboarding) {
  // Get form data
  const formData = new FormData(form);
  const preferencesData = {
    notifications: formData.has('notifications'),
    theme: formData.get('theme') || 'system',
    interests: []
  };
  
  // Get interests (checkbox group)
  const interestCheckboxes = form.querySelectorAll('input[name="interests[]"]:checked');
  preferencesData.interests = Array.from(interestCheckboxes).map(checkbox => checkbox.value);
  
  // Update user data
  onboarding.userData.preferences = {
    ...onboarding.userData.preferences,
    ...preferencesData
  };
  
  // Go to next step
  onboarding.nextStep();
}

// Add basic styles
const style = document.createElement('style');
style.textContent = `
  .onboarding-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
  }
  
  .onboarding-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
  }
  
  .onboarding-progress-step {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #e0e0e0;
    cursor: pointer;
  }
  
  .onboarding-progress-step.active {
    background-color: #2196f3;
  }
  
  .onboarding-progress-step.completed {
    background-color: #4caf50;
  }
  
  .onboarding-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
  }
  
  .onboarding-description {
    margin-bottom: 30px;
    color: #666;
  }
  
  .onboarding-image {
    max-width: 100%;
    margin-bottom: 30px;
  }
  
  .onboarding-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
  }
  
  .onboarding-button {
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
  }
  
  .onboarding-button.next,
  .onboarding-button.finish {
    background-color: #2196f3;
    color: white;
    border: none;
  }
  
  .onboarding-button.prev {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
  }
  
  .form-field {
    margin-bottom: 20px;
  }
  
  .form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
  }
  
  .form-field input[type="text"],
  .form-field select,
  .form-field textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  
  .form-field textarea {
    min-height: 100px;
  }
  
  .radio-group,
  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .radio-option,
  .checkbox-option {
    display: flex;
    align-items: center;
  }
  
  .radio-option label,
  .checkbox-option label {
    margin-left: 10px;
    margin-bottom: 0;
    font-weight: normal;
  }
  
  .error-message {
    display: none;
    color: #f44336;
    font-size: 12px;
    margin-top: 5px;
  }
  
  .required {
    color: #f44336;
  }
`;

document.head.appendChild(style);

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initOnboardingWebflow);

// Fallback for Webflow's preview mode where DOMContentLoaded might have already fired
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initOnboardingWebflow, 1);
}

// Expose API for external access
window.Atlas25Onboarding = {
  goToStep: function(containerId, stepIndex) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.onboarding-container');
      
    if (container && container.onboarding) {
      container.onboarding.goToStep(stepIndex);
    }
  },
  
  nextStep: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.onboarding-container');
      
    if (container && container.onboarding) {
      container.onboarding.nextStep();
    }
  },
  
  prevStep: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.onboarding-container');
      
    if (container && container.onboarding) {
      container.onboarding.prevStep();
    }
  }
};