/**
 * @file onboarding.js
 * @description Onboarding functionality for Atlas25 Web App
 * @module pages/onboarding
 */

import firebaseService from '../../core/firebase/firebase-service.js';
import stateManager from '../../core/state/state-manager.js';
import { showNotification } from '../../utils/ui-helpers.js';
import { validateForm } from '../../utils/validation.js';

/**
 * Onboarding class for managing the onboarding process
 */
class Onboarding {
  /**
   * Create an onboarding instance
   * @param {Object} options - Configuration options
   * @param {HTMLElement|string} [options.container] - Container element or selector
   * @param {Function} [options.onComplete] - Callback function when onboarding is completed
   * @param {Function} [options.onStepChange] - Callback function when step changes
   */
  constructor(options = {}) {
    this.options = options;
    this.container = typeof options.container === 'string' 
      ? document.querySelector(options.container) 
      : options.container;
      
    this.steps = [
      'welcome',
      'profile',
      'preferences',
      'complete'
    ];
    
    this.currentStepIndex = 0;
    this.userData = {
      profile: {},
      preferences: {}
    };
    
    // Bind methods
    this.nextStep = this.nextStep.bind(this);
    this.prevStep = this.prevStep.bind(this);
    this.goToStep = this.goToStep.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
  }

  /**
   * Initialize the onboarding process
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if user is authenticated
      const user = stateManager.get('user.profile');
      
      if (!user) {
        console.error('User not authenticated');
        showNotification('Please sign in to continue', { type: 'error' });
        return;
      }
      
      // Check if user has already completed onboarding
      const onboardingComplete = stateManager.get('user.onboardingComplete');
      
      if (onboardingComplete) {
        console.log('Onboarding already completed');
        
        if (this.options.onComplete) {
          this.options.onComplete();
        }
        
        return;
      }
      
      // Load user data if available
      const userData = await firebaseService.getDocument('user_profiles', user.uid);
      
      if (userData) {
        this.userData = {
          profile: userData.profile || {},
          preferences: userData.preferences || {}
        };
      }
      
      // Render first step
      this.renderStep(this.steps[this.currentStepIndex]);
      
      console.log('Onboarding initialized');
    } catch (error) {
      console.error('Failed to initialize onboarding:', error);
      showNotification('Failed to initialize onboarding', { type: 'error' });
    }
  }

  /**
   * Render the current step
   * @param {string} stepId - ID of the step to render
   */
  renderStep(stepId) {
    if (!this.container) return;
    
    // Clear container
    this.container.innerHTML = '';
    
    // Create step container
    const stepContainer = document.createElement('div');
    stepContainer.className = 'onboarding-step';
    stepContainer.dataset.step = stepId;
    
    // Render step content based on ID
    switch (stepId) {
      case 'welcome':
        this.renderWelcomeStep(stepContainer);
        break;
        
      case 'profile':
        this.renderProfileStep(stepContainer);
        break;
        
      case 'preferences':
        this.renderPreferencesStep(stepContainer);
        break;
        
      case 'complete':
        this.renderCompleteStep(stepContainer);
        break;
        
      default:
        console.error(`Unknown step: ${stepId}`);
        break;
    }
    
    // Add to container
    this.container.appendChild(stepContainer);
    
    // Update progress indicator if exists
    this.updateProgress();
    
    // Call onStepChange callback
    if (this.options.onStepChange) {
      this.options.onStepChange({
        stepId,
        stepIndex: this.currentStepIndex,
        totalSteps: this.steps.length
      });
    }
  }

  /**
   * Render the welcome step
   * @param {HTMLElement} container - Step container
   * @private
   */
  renderWelcomeStep(container) {
    // Create welcome content
    const content = document.createElement('div');
    content.className = 'onboarding-welcome';
    
    // Add title
    const title = document.createElement('h2');
    title.className = 'onboarding-title';
    title.textContent = 'Welcome to Atlas25';
    content.appendChild(title);
    
    // Add description
    const description = document.createElement('p');
    description.className = 'onboarding-description';
    description.textContent = 'Atlas25 is your guide to the NBA Rookie Transition Program. Let\'s get you set up so you can make the most of your experience.';
    content.appendChild(description);
    
    // Add image
    const image = document.createElement('img');
    image.className = 'onboarding-image';
    image.src = 'https://example.com/images/welcome.jpg';
    image.alt = 'Welcome to Atlas25';
    content.appendChild(image);
    
    // Add to container
    container.appendChild(content);
    
    // Add navigation buttons
    const buttons = document.createElement('div');
    buttons.className = 'onboarding-buttons';
    
    const nextButton = document.createElement('button');
    nextButton.className = 'onboarding-button next';
    nextButton.textContent = 'Get Started';
    nextButton.addEventListener('click', this.nextStep);
    buttons.appendChild(nextButton);
    
    container.appendChild(buttons);
  }

  /**
   * Render the profile step
   * @param {HTMLElement} container - Step container
   * @private
   */
  renderProfileStep(container) {
    // Create profile content
    const content = document.createElement('div');
    content.className = 'onboarding-profile';
    
    // Add title
    const title = document.createElement('h2');
    title.className = 'onboarding-title';
    title.textContent = 'Your Profile';
    content.appendChild(title);
    
    // Add description
    const description = document.createElement('p');
    description.className = 'onboarding-description';
    description.textContent = 'Tell us a bit about yourself so we can personalize your experience.';
    content.appendChild(description);
    
    // Create form
    const form = document.createElement('form');
    form.className = 'onboarding-form';
    form.addEventListener('submit', (event) => {
      event.preventDefault();
      this.handleProfileSubmit(form);
    });
    
    // Add form fields
    const fields = [
      {
        id: 'fullName',
        label: 'Full Name',
        type: 'text',
        required: true,
        value: this.userData.profile.fullName || ''
      },
      {
        id: 'position',
        label: 'Position',
        type: 'select',
        options: [
          { value: '', label: 'Select your position' },
          { value: 'PG', label: 'Point Guard' },
          { value: 'SG', label: 'Shooting Guard' },
          { value: 'SF', label: 'Small Forward' },
          { value: 'PF', label: 'Power Forward' },
          { value: 'C', label: 'Center' }
        ],
        required: true,
        value: this.userData.profile.position || ''
      },
      {
        id: 'team',
        label: 'Team',
        type: 'select',
        options: [
          { value: '', label: 'Select your team' },
          { value: 'ATL', label: 'Atlanta Hawks' },
          { value: 'BOS', label: 'Boston Celtics' },
          { value: 'BKN', label: 'Brooklyn Nets' },
          { value: 'CHA', label: 'Charlotte Hornets' },
          { value: 'CHI', label: 'Chicago Bulls' },
          { value: 'CLE', label: 'Cleveland Cavaliers' },
          { value: 'DAL', label: 'Dallas Mavericks' },
          { value: 'DEN', label: 'Denver Nuggets' },
          { value: 'DET', label: 'Detroit Pistons' },
          { value: 'GSW', label: 'Golden State Warriors' },
          { value: 'HOU', label: 'Houston Rockets' },
          { value: 'IND', label: 'Indiana Pacers' },
          { value: 'LAC', label: 'Los Angeles Clippers' },
          { value: 'LAL', label: 'Los Angeles Lakers' },
          { value: 'MEM', label: 'Memphis Grizzlies' },
          { value: 'MIA', label: 'Miami Heat' },
          { value: 'MIL', label: 'Milwaukee Bucks' },
          { value: 'MIN', label: 'Minnesota Timberwolves' },
          { value: 'NOP', label: 'New Orleans Pelicans' },
          { value: 'NYK', label: 'New York Knicks' },
          { value: 'OKC', label: 'Oklahoma City Thunder' },
          { value: 'ORL', label: 'Orlando Magic' },
          { value: 'PHI', label: 'Philadelphia 76ers' },
          { value: 'PHX', label: 'Phoenix Suns' },
          { value: 'POR', label: 'Portland Trail Blazers' },
          { value: 'SAC', label: 'Sacramento Kings' },
          { value: 'SAS', label: 'San Antonio Spurs' },
          { value: 'TOR', label: 'Toronto Raptors' },
          { value: 'UTA', label: 'Utah Jazz' },
          { value: 'WAS', label: 'Washington Wizards' }
        ],
        required: true,
        value: this.userData.profile.team || ''
      },
      {
        id: 'bio',
        label: 'Bio',
        type: 'textarea',
        placeholder: 'Tell us a bit about yourself...',
        required: false,
        value: this.userData.profile.bio || ''
      }
    ];
    
    fields.forEach(field => {
      const fieldContainer = document.createElement('div');
      fieldContainer.className = 'form-field';
      
      const label = document.createElement('label');
      label.htmlFor = field.id;
      label.textContent = field.label;
      
      if (field.required) {
        label.innerHTML += ' <span class="required">*</span>';
      }
      
      fieldContainer.appendChild(label);
      
      let input;
      
      switch (field.type) {
        case 'select':
          input = document.createElement('select');
          
          field.options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.label;
            
            if (field.value === option.value) {
              optionElement.selected = true;
            }
            
            input.appendChild(optionElement);
          });
          break;
          
        case 'textarea':
          input = document.createElement('textarea');
          input.placeholder = field.placeholder || '';
          input.value = field.value;
          break;
          
        default:
          input = document.createElement('input');
          input.type = field.type;
          input.placeholder = field.placeholder || '';
          input.value = field.value;
          break;
      }
      
      input.id = field.id;
      input.name = field.id;
      input.required = field.required;
      
      fieldContainer.appendChild(input);
      
      // Add error message container
      const errorMessage = document.createElement('div');
      errorMessage.className = 'error-message';
      fieldContainer.appendChild(errorMessage);
      
      form.appendChild(fieldContainer);
    });
    
    content.appendChild(form);
    container.appendChild(content);
    
    // Add navigation buttons
    const buttons = document.createElement('div');
    buttons.className = 'onboarding-buttons';
    
    const prevButton = document.createElement('button');
    prevButton.className = 'onboarding-button prev';
    prevButton.textContent = 'Back';
    prevButton.addEventListener('click', this.prevStep);
    buttons.appendChild(prevButton);
    
    const nextButton = document.createElement('button');
    nextButton.className = 'onboarding-button next';
    nextButton.textContent = 'Next';
    nextButton.type = 'submit';
    nextButton.form = form.id;
    buttons.appendChild(nextButton);
    
    container.appendChild(buttons);
  }

  /**
   * Render the preferences step
   * @param {HTMLElement} container - Step container
   * @private
   */
  renderPreferencesStep(container) {
    // Create preferences content
    const content = document.createElement('div');
    content.className = 'onboarding-preferences';
    
    // Add title
    const title = document.createElement('h2');
    title.className = 'onboarding-title';
    title.textContent = 'Your Preferences';
    content.appendChild(title);
    
    // Add description
    const description = document.createElement('p');
    description.className = 'onboarding-description';
    description.textContent = 'Customize your experience by setting your preferences.';
    content.appendChild(description);
    
    // Create form
    const form = document.createElement('form');
    form.className = 'onboarding-form';
    form.addEventListener('submit', (event) => {
      event.preventDefault();
      this.handlePreferencesSubmit(form);
    });
    
    // Add form fields
    const fields = [
      {
        id: 'notifications',
        label: 'Notifications',
        type: 'checkbox',
        checked: this.userData.preferences.notifications !== false,
        description: 'Receive notifications about new content and updates'
      },
      {
        id: 'theme',
        label: 'Theme',
        type: 'radio',
        options: [
          { value: 'light', label: 'Light' },
          { value: 'dark', label: 'Dark' },
          { value: 'system', label: 'System Default' }
        ],
        value: this.userData.preferences.theme || 'system'
      },
      {
        id: 'interests',
        label: 'Areas of Interest',
        type: 'checkbox-group',
        options: [
          { value: 'referee-operations', label: 'Referee Operations' },
          { value: 'business-of-basketball', label: 'Business of Basketball' },
          { value: 'media', label: 'Media Training' },
          { value: 'coaching', label: 'Coaching' },
          { value: 'community-impact', label: 'Community Impact' }
        ],
        values: this.userData.preferences.interests || []
      }
    ];
    
    fields.forEach(field => {
      const fieldContainer = document.createElement('div');
      fieldContainer.className = 'form-field';
      
      const label = document.createElement('label');
      label.htmlFor = field.id;
      label.textContent = field.label;
      fieldContainer.appendChild(label);
      
      switch (field.type) {
        case 'checkbox':
          const checkbox = document.createElement('input');
          checkbox.type = 'checkbox';
          checkbox.id = field.id;
          checkbox.name = field.id;
          checkbox.checked = field.checked;
          
          const checkboxLabel = document.createElement('label');
          checkboxLabel.htmlFor = field.id;
          checkboxLabel.className = 'checkbox-label';
          checkboxLabel.textContent = field.description;
          
          fieldContainer.appendChild(checkbox);
          fieldContainer.appendChild(checkboxLabel);
          break;
          
        case 'radio':
          const radioGroup = document.createElement('div');
          radioGroup.className = 'radio-group';
          
          field.options.forEach((option, index) => {
            const radioContainer = document.createElement('div');
            radioContainer.className = 'radio-option';
            
            const radio = document.createElement('input');
            radio.type = 'radio';
            radio.id = `${field.id}-${option.value}`;
            radio.name = field.id;
            radio.value = option.value;
            radio.checked = field.value === option.value;
            
            const radioLabel = document.createElement('label');
            radioLabel.htmlFor = `${field.id}-${option.value}`;
            radioLabel.textContent = option.label;
            
            radioContainer.appendChild(radio);
            radioContainer.appendChild(radioLabel);
            radioGroup.appendChild(radioContainer);
          });
          
          fieldContainer.appendChild(radioGroup);
          break;
          
        case 'checkbox-group':
          const checkboxGroup = document.createElement('div');
          checkboxGroup.className = 'checkbox-group';
          
          field.options.forEach((option, index) => {
            const checkboxContainer = document.createElement('div');
            checkboxContainer.className = 'checkbox-option';
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `${field.id}-${option.value}`;
            checkbox.name = `${field.id}[]`;
            checkbox.value = option.value;
            checkbox.checked = field.values.includes(option.value);
            
            const checkboxLabel = document.createElement('label');
            checkboxLabel.htmlFor = `${field.id}-${option.value}`;
            checkboxLabel.textContent = option.label;
            
            checkboxContainer.appendChild(checkbox);
            checkboxContainer.appendChild(checkboxLabel);
            checkboxGroup.appendChild(checkboxContainer);
          });
          
          fieldContainer.appendChild(checkboxGroup);
          break;
      }
      
      form.appendChild(fieldContainer);
    });
    
    content.appendChild(form);
    container.appendChild(content);
    
    // Add navigation buttons
    const buttons = document.createElement('div');
    buttons.className = 'onboarding-buttons';
    
    const prevButton = document.createElement('button');
    prevButton.className = 'onboarding-button prev';
    prevButton.textContent = 'Back';
    prevButton.addEventListener('click', this.prevStep);
    buttons.appendChild(prevButton);
    
    const nextButton = document.createElement('button');
    nextButton.className = 'onboarding-button next';
    nextButton.textContent = 'Next';
    nextButton.type = 'submit';
    nextButton.form = form.id;
    buttons.appendChild(nextButton);
    
    container.appendChild(buttons);
  }

  /**
   * Render the complete step
   * @param {HTMLElement} container - Step container
   * @private
   */
  renderCompleteStep(container) {
    // Create complete content
    const content = document.createElement('div');
    content.className = 'onboarding-complete';
    
    // Add title
    const title = document.createElement('h2');
    title.className = 'onboarding-title';
    title.textContent = 'You\'re All Set!';
    content.appendChild(title);
    
    // Add description
    const description = document.createElement('p');
    description.className = 'onboarding-description';
    description.textContent = 'Thank you for completing the onboarding process. You\'re now ready to explore Atlas25!';
    content.appendChild(description);
    
    // Add image
    const image = document.createElement('img');
    image.className = 'onboarding-image';
    image.src = 'https://example.com/images/complete.jpg';
    image.alt = 'Onboarding Complete';
    content.appendChild(image);
    
    // Add to container
    container.appendChild(content);
    
    // Add navigation buttons
    const buttons = document.createElement('div');
    buttons.className = 'onboarding-buttons';
    
    const finishButton = document.createElement('button');
    finishButton.className = 'onboarding-button finish';
    finishButton.textContent = 'Get Started';
    finishButton.addEventListener('click', this.handleComplete.bind(this));
    buttons.appendChild(finishButton);
    
    container.appendChild(buttons);
  }

  /**
   * Update progress indicator
   * @private
   */
  updateProgress() {
    const progressContainer = document.querySelector('.onboarding-progress');
    
    if (!progressContainer) return;
    
    // Clear progress container
    progressContainer.innerHTML = '';
    
    // Create progress steps
    this.steps.forEach((step, index) => {
      const stepElement = document.createElement('div');
      stepElement.className = 'onboarding-progress-step';
      
      if (index < this.currentStepIndex) {
        stepElement.classList.add('completed');
      } else if (index === this.currentStepIndex) {
        stepElement.classList.add('active');
      }
      
      stepElement.addEventListener('click', () => {
        // Only allow going back to previous steps
        if (index <= this.currentStepIndex) {
          this.goToStep(index);
        }
      });
      
      progressContainer.appendChild(stepElement);
    });
  }

  /**
   * Go to the next step
   */
  nextStep() {
    if (this.currentStepIndex < this.steps.length - 1) {
      this.currentStepIndex++;
      this.renderStep(this.steps[this.currentStepIndex]);
    }
  }

  /**
   * Go to the previous step
   */
  prevStep() {
    if (this.currentStepIndex > 0) {
      this.currentStepIndex--;
      this.renderStep(this.steps[this.currentStepIndex]);
    }
  }

  /**
   * Go to a specific step
   * @param {number} index - Step index
   */
  goToStep(index) {
    if (index >= 0 && index < this.steps.length) {
      this.currentStepIndex = index;
      this.renderStep(this.steps[this.currentStepIndex]);
    }
  }

  /**
   * Handle profile form submission
   * @param {HTMLFormElement} form - Profile form
   * @private
   */
  handleProfileSubmit(form) {
    // Get form data
    const formData = new FormData(form);
    const profileData = {};
    
    for (const [key, value] of formData.entries()) {
      profileData[key] = value;
    }
    
    // Validate form data
    const validationSchema = {
      fullName: {
        required: true,
        minLength: 2,
        maxLength: 100
      },
      position: {
        required: true
      },
      team: {
        required: true
      }
    };
    
    const validationResult = validateForm(profileData, validationSchema);
    
    if (!validationResult.isValid) {
      // Show validation errors
      Object.keys(validationResult.errors).forEach(field => {
        const errorElement = form.querySelector(`#${field}`).nextElementSibling;
        
        if (errorElement) {
          errorElement.textContent = validationResult.errors[field][0];
          errorElement.style.display = 'block';
        }
      });
      
      return;
    }
    
    // Update user data
    this.userData.profile = {
      ...this.userData.profile,
      ...profileData
    };
    
    // Go to next step
    this.nextStep();
  }

  /**
   * Handle preferences form submission
   * @param {HTMLFormElement} form - Preferences form
   * @private
   */
  handlePreferencesSubmit(form) {
    // Get form data
    const formData = new FormData(form);
    const preferencesData = {
      notifications: formData.has('notifications'),
      theme: formData.get('theme') || 'system',
      interests: []
    };
    
    // Get interests (checkbox group)
    const interestCheckboxes = form.querySelectorAll('input[name="interests[]"]:checked');
    preferencesData.interests = Array.from(interestCheckboxes).map(checkbox => checkbox.value);
    
    // Update user data
    this.userData.preferences = {
      ...this.userData.preferences,
      ...preferencesData
    };
    
    // Go to next step
    this.nextStep();
  }

  /**
   * Handle form submission
   * @param {Event} event - Form submission event
   * @private
   */
  handleSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const stepId = form.closest('.onboarding-step').dataset.step;
    
    switch (stepId) {
      case 'profile':
        this.handleProfileSubmit(form);
        break;
        
      case 'preferences':
        this.handlePreferencesSubmit(form);
        break;
    }
  }

  /**
   * Handle onboarding completion
   * @private
   */
  async handleComplete() {
    try {
      // Get user ID
      const user = stateManager.get('user.profile');
      
      if (!user) {
        console.error('User not authenticated');
        return;
      }
      
      // Save user data to Firebase
      await firebaseService.updateDocument('user_profiles', user.uid, {
        profile: this.userData.profile,
        preferences: this.userData.preferences,
        onboardingComplete: true,
        updatedAt: new Date().toISOString()
      });
      
      // Update state
      stateManager.set('user.profile', {
        ...user,
        ...this.userData.profile
      });
      
      stateManager.set('user.preferences', this.userData.preferences);
      stateManager.set('user.onboardingComplete', true);
      
      // Show success notification
      showNotification('Onboarding completed successfully', { type: 'success' });
      
      // Call onComplete callback
      if (this.options.onComplete) {
        this.options.onComplete();
      }
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
      showNotification('Failed to save your preferences', { type: 'error' });
    }
  }
}

export default Onboarding;