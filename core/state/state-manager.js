/**
 * @file state-manager.js
 * @description State management interface for Atlas25 Web App
 * @module core/state
 */

import appState from './app-state.js';

/**
 * StateManager class for providing a simplified interface to app state
 */
class StateManager {
  constructor() {
    this.appState = appState;
    this.autoSave = false;
    this.autoSaveInterval = null;
    this.autoSaveKey = 'atlas25_app_state';
  }

  /**
   * Initialize the state manager
   * @param {Object} options - Configuration options
   * @param {boolean} [options.loadSaved=true] - Whether to load saved state
   * @param {boolean} [options.autoSave=false] - Whether to auto-save state
   * @param {number} [options.autoSaveInterval=30000] - Auto-save interval in ms
   * @param {string} [options.storageKey='atlas25_app_state'] - Storage key
   * @returns {Promise<void>}
   */
  async initialize(options = {}) {
    const {
      loadSaved = true,
      autoSave = false,
      autoSaveInterval = 30000,
      storageKey = 'atlas25_app_state'
    } = options;
    
    // Load saved state if requested
    if (loadSaved) {
      this.appState.loadState(storageKey);
    }
    
    // Set up auto-save if requested
    if (autoSave) {
      this.enableAutoSave(autoSaveInterval, storageKey);
    }
    
    console.log('State Manager initialized');
  }

  /**
   * Enable auto-saving of state
   * @param {number} [interval=30000] - Auto-save interval in ms
   * @param {string} [key='atlas25_app_state'] - Storage key
   */
  enableAutoSave(interval = 30000, key = 'atlas25_app_state') {
    this.autoSave = true;
    this.autoSaveKey = key;
    
    // Clear any existing interval
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }
    
    // Set up new interval
    this.autoSaveInterval = setInterval(() => {
      this.appState.persistState(this.autoSaveKey);
    }, interval);
    
    console.log(`Auto-save enabled with interval: ${interval}ms`);
  }

  /**
   * Disable auto-saving of state
   */
  disableAutoSave() {
    this.autoSave = false;
    
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
    
    console.log('Auto-save disabled');
  }

  /**
   * Save state manually
   * @param {string} [key] - Optional storage key
   */
  saveState(key) {
    this.appState.persistState(key || this.autoSaveKey);
  }

  /**
   * Load state manually
   * @param {string} [key] - Optional storage key
   * @returns {boolean} - True if state was loaded successfully
   */
  loadState(key) {
    return this.appState.loadState(key || this.autoSaveKey);
  }

  /**
   * Reset state to initial values
   */
  resetState() {
    this.appState.resetState();
    console.log('State reset to initial values');
  }

  /**
   * Get the current state
   * @returns {Object} - Current state
   */
  getState() {
    return this.appState.getState();
  }

  /**
   * Get a specific part of the state
   * @param {string} path - Dot notation path to state part
   * @returns {any} - State value at path
   */
  get(path) {
    return this.appState.getStateValue(path);
  }

  /**
   * Update a specific part of the state
   * @param {string} path - Dot notation path to state part
   * @param {any} value - New value
   */
  set(path, value) {
    this.appState.updateState(path, value);
    
    // Auto-save if enabled
    if (this.autoSave) {
      this.saveState();
    }
  }

  /**
   * Subscribe to state changes
   * @param {Function} listener - Function to call when state changes
   * @returns {Function} - Function to unsubscribe
   */
  subscribe(listener) {
    return this.appState.subscribe(listener);
  }

  /**
   * Subscribe to changes in a specific part of the state
   * @param {string} path - Dot notation path to state part
   * @param {Function} listener - Function to call when state part changes
   * @returns {Function} - Function to unsubscribe
   */
  subscribeTo(path, listener) {
    let previousValue = this.get(path);
    
    return this.appState.subscribe((state) => {
      const currentValue = this.get(path);
      
      // Only call listener if value has changed
      if (JSON.stringify(previousValue) !== JSON.stringify(currentValue)) {
        previousValue = currentValue;
        listener(currentValue, state);
      }
    });
  }

  /**
   * Dispatch an action to update state
   * @param {Object} action - Action object with type and payload
   * @returns {Object} - New state
   */
  dispatch(action) {
    const newState = this.appState.dispatch(action);
    
    // Auto-save if enabled
    if (this.autoSave) {
      this.saveState();
    }
    
    return newState;
  }

  /**
   * Update user state
   * @param {Object} userData - User data to update
   */
  updateUser(userData) {
    this.dispatch({
      type: 'SET_USER',
      payload: userData
    });
  }

  /**
   * Update UI state
   * @param {Object} uiData - UI data to update
   */
  updateUI(uiData) {
    this.dispatch({
      type: 'UPDATE_UI',
      payload: uiData
    });
  }

  /**
   * Update content state
   * @param {Object} contentData - Content data to update
   */
  updateContent(contentData) {
    this.dispatch({
      type: 'SET_CONTENT',
      payload: contentData
    });
  }

  /**
   * Set the current page
   * @param {string} pageName - Name of the current page
   */
  setCurrentPage(pageName) {
    this.updateUI({ currentPage: pageName });
  }

  /**
   * Set the current module
   * @param {string} moduleId - ID of the current module
   */
  setCurrentModule(moduleId) {
    this.updateContent({ currentModule: moduleId });
  }

  /**
   * Set the current lesson
   * @param {string} lessonId - ID of the current lesson
   */
  setCurrentLesson(lessonId) {
    this.updateContent({ currentLesson: lessonId });
  }

  /**
   * Mark a lesson as completed
   * @param {string} lessonId - ID of the completed lesson
   */
  completeLesson(lessonId) {
    const completedLessons = [...this.get('content.completedLessons') || []];
    
    if (!completedLessons.includes(lessonId)) {
      completedLessons.push(lessonId);
      this.updateContent({ completedLessons });
    }
  }

  /**
   * Save quiz results
   * @param {string} quizId - ID of the quiz
   * @param {Object} results - Quiz results
   */
  saveQuizResults(quizId, results) {
    const quizResults = { ...this.get('content.quizResults') || {} };
    quizResults[quizId] = results;
    this.updateContent({ quizResults });
  }

  /**
   * Update leaderboard data
   * @param {Array} leaderboardData - Leaderboard data
   */
  updateLeaderboard(leaderboardData) {
    this.updateContent({ leaderboard: leaderboardData });
  }

  /**
   * Add a notification
   * @param {Object} notification - Notification object
   * @param {string} notification.message - Notification message
   * @param {string} [notification.type='info'] - Notification type
   * @param {number} [notification.duration=5000] - Duration in ms
   * @returns {string} - Notification ID
   */
  addNotification(notification) {
    const id = Date.now().toString();
    const notifications = [...this.get('ui.notifications') || []];
    
    notifications.push({
      id,
      type: 'info',
      duration: 5000,
      ...notification,
      timestamp: new Date().toISOString()
    });
    
    this.updateUI({ notifications });
    
    // Auto-remove notification after duration
    if (notification.duration !== 0) {
      setTimeout(() => {
        this.removeNotification(id);
      }, notification.duration || 5000);
    }
    
    return id;
  }

  /**
   * Remove a notification
   * @param {string} id - Notification ID
   */
  removeNotification(id) {
    const notifications = [...this.get('ui.notifications') || []];
    const updatedNotifications = notifications.filter(n => n.id !== id);
    this.updateUI({ notifications: updatedNotifications });
  }

  /**
   * Clear all notifications
   */
  clearNotifications() {
    this.updateUI({ notifications: [] });
  }

  /**
   * Toggle sidebar
   * @param {boolean} [force] - Force specific state
   */
  toggleSidebar(force) {
    const currentState = this.get('ui.sidebarOpen');
    const newState = force !== undefined ? force : !currentState;
    this.updateUI({ sidebarOpen: newState });
  }

  /**
   * Set theme
   * @param {string} theme - Theme name ('light' or 'dark')
   */
  setTheme(theme) {
    this.updateUI({ theme });
    
    // Update body class for CSS
    document.body.classList.remove('theme-light', 'theme-dark');
    document.body.classList.add(`theme-${theme}`);
  }
}

// Create and export a singleton instance
const stateManager = new StateManager();
export default stateManager;