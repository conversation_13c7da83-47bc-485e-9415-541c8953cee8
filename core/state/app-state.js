/**
 * @file app-state.js
 * @description Application state management for Atlas25 Web App
 * @module core/state
 */

/**
 * Initial application state
 * @type {Object}
 */
const initialState = {
  // User state
  user: {
    isAuthenticated: false,
    profile: null,
    preferences: {},
    progress: {}
  },
  
  // UI state
  ui: {
    currentPage: null,
    isLoading: false,
    activeModal: null,
    notifications: [],
    theme: 'light',
    sidebarOpen: false
  },
  
  // Content state
  content: {
    currentModule: null,
    currentLesson: null,
    completedLessons: [],
    quizResults: {},
    leaderboard: []
  },
  
  // Offline state
  offline: {
    isOnline: navigator.onLine,
    pendingSyncs: 0,
    lastSyncTime: null
  },
  
  // App metadata
  meta: {
    version: '1.0.0',
    buildNumber: '1',
    lastUpdated: new Date().toISOString()
  }
};

/**
 * Deep clone an object
 * @param {Object} obj - Object to clone
 * @returns {Object} - Cloned object
 */
function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * AppState class for managing application state
 */
class AppState {
  constructor() {
    this.state = deepClone(initialState);
    this.listeners = [];
    this.reducers = {};
  }

  /**
   * Get the current state
   * @returns {Object} - Current state
   */
  getState() {
    return this.state;
  }

  /**
   * Get a specific part of the state
   * @param {string} path - Dot notation path to state part (e.g., 'user.profile')
   * @returns {any} - State value at path
   */
  getStateValue(path) {
    return path.split('.').reduce((obj, key) => {
      return obj && obj[key] !== undefined ? obj[key] : null;
    }, this.state);
  }

  /**
   * Register a reducer function for a specific action type
   * @param {string} actionType - Action type to handle
   * @param {Function} reducer - Reducer function (state, action) => newState
   */
  registerReducer(actionType, reducer) {
    this.reducers[actionType] = reducer;
  }

  /**
   * Dispatch an action to update state
   * @param {Object} action - Action object with type and payload
   * @returns {Object} - New state
   */
  dispatch(action) {
    console.log('Dispatching action:', action);
    
    const { type, payload } = action;
    
    // Find the appropriate reducer
    const reducer = this.reducers[type];
    
    if (!reducer) {
      console.warn(`No reducer registered for action type: ${type}`);
      return this.state;
    }
    
    // Create a new state by applying the reducer
    const newState = reducer(this.state, payload);
    
    // Update state and notify listeners
    this.state = newState;
    this.notifyListeners();
    
    return this.state;
  }

  /**
   * Subscribe to state changes
   * @param {Function} listener - Function to call when state changes
   * @returns {Function} - Function to unsubscribe
   */
  subscribe(listener) {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all listeners of state change
   */
  notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('Error in state listener:', error);
      }
    });
  }

  /**
   * Reset state to initial values
   */
  resetState() {
    this.state = deepClone(initialState);
    this.notifyListeners();
  }

  /**
   * Update a specific part of the state
   * @param {string} path - Dot notation path to state part (e.g., 'user.profile')
   * @param {any} value - New value
   */
  updateState(path, value) {
    const pathParts = path.split('.');
    const newState = deepClone(this.state);
    
    let current = newState;
    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i];
      if (!current[part]) {
        current[part] = {};
      }
      current = current[part];
    }
    
    current[pathParts[pathParts.length - 1]] = value;
    this.state = newState;
    this.notifyListeners();
  }

  /**
   * Persist state to localStorage
   * @param {string} [key='atlas25_app_state'] - Key to use for localStorage
   */
  persistState(key = 'atlas25_app_state') {
    try {
      const serializedState = JSON.stringify(this.state);
      localStorage.setItem(key, serializedState);
      console.log('State persisted to localStorage');
    } catch (error) {
      console.error('Failed to persist state:', error);
    }
  }

  /**
   * Load state from localStorage
   * @param {string} [key='atlas25_app_state'] - Key to use for localStorage
   * @returns {boolean} - True if state was loaded successfully
   */
  loadState(key = 'atlas25_app_state') {
    try {
      const serializedState = localStorage.getItem(key);
      
      if (!serializedState) {
        console.log('No saved state found');
        return false;
      }
      
      this.state = JSON.parse(serializedState);
      this.notifyListeners();
      console.log('State loaded from localStorage');
      return true;
    } catch (error) {
      console.error('Failed to load state:', error);
      return false;
    }
  }
}

// Create and export a singleton instance
const appState = new AppState();
export default appState;

// Register default reducers
appState.registerReducer('SET_USER', (state, payload) => {
  return {
    ...state,
    user: {
      ...state.user,
      ...payload
    }
  };
});

appState.registerReducer('UPDATE_UI', (state, payload) => {
  return {
    ...state,
    ui: {
      ...state.ui,
      ...payload
    }
  };
});

appState.registerReducer('SET_CONTENT', (state, payload) => {
  return {
    ...state,
    content: {
      ...state.content,
      ...payload
    }
  };
});

appState.registerReducer('UPDATE_OFFLINE_STATUS', (state, payload) => {
  return {
    ...state,
    offline: {
      ...state.offline,
      ...payload
    }
  };
});

// Listen for online/offline events
window.addEventListener('online', () => {
  appState.dispatch({
    type: 'UPDATE_OFFLINE_STATUS',
    payload: {
      isOnline: true
    }
  });
});

window.addEventListener('offline', () => {
  appState.dispatch({
    type: 'UPDATE_OFFLINE_STATUS',
    payload: {
      isOnline: false
    }
  });
});