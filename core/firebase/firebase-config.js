/**
 * @file firebase-config.js
 * @description Firebase configuration for Atlas25 Web App
 * @module core/firebase
 */

/**
 * Development environment Firebase configuration
 * @type {Object}
 */
const devConfig = {
  apiKey: "DEV_API_KEY",
  authDomain: "atlas25-dev.firebaseapp.com",
  projectId: "atlas25-dev",
  storageBucket: "atlas25-dev.appspot.com",
  messagingSenderId: "DEV_MESSAGING_SENDER_ID",
  appId: "DEV_APP_ID",
  measurementId: "DEV_MEASUREMENT_ID"
};

/**
 * Production environment Firebase configuration
 * @type {Object}
 */
const prodConfig = {
  apiKey: "PROD_API_KEY",
  authDomain: "atlas25-prod.firebaseapp.com",
  projectId: "atlas25-prod",
  storageBucket: "atlas25-prod.appspot.com",
  messagingSenderId: "PROD_MESSAGING_SENDER_ID",
  appId: "PROD_APP_ID",
  measurementId: "PROD_MEASUREMENT_ID"
};

/**
 * Determine if the current environment is production
 * @returns {boolean} - True if production environment
 */
function isProduction() {
  // Check for production environment
  // This can be customized based on your deployment strategy
  return window.location.hostname === 'atlas25.nba.com' ||
         window.location.hostname === 'www.atlas25.nba.com';
}

/**
 * Get the appropriate Firebase configuration based on environment
 * @returns {Object} - Firebase configuration object
 */
export function getFirebaseConfig() {
  return isProduction() ? prodConfig : devConfig;
}

/**
 * Firebase configuration object
 * @type {Object}
 */
export const firebaseConfig = getFirebaseConfig();

/**
 * Firebase app initialization status
 * @type {boolean}
 */
let initialized = false;

/**
 * Initialize Firebase if not already initialized
 * @returns {Object} - Firebase app instance
 */
export function initializeFirebase() {
  if (typeof firebase === 'undefined') {
    console.error('Firebase SDK not loaded');
    throw new Error('Firebase SDK not loaded');
  }
  
  try {
    // Check if Firebase is already initialized
    if (!initialized) {
      firebase.initializeApp(firebaseConfig);
      
      // Enable offline persistence if supported
      // This is important for offline data capabilities
      if (firebase.firestore && typeof firebase.firestore().enablePersistence === 'function') {
        firebase.firestore().enablePersistence({ synchronizeTabs: true })
          .catch(err => {
            if (err.code === 'failed-precondition') {
              // Multiple tabs open, persistence can only be enabled in one tab at a time
              console.warn('Firebase persistence failed: Multiple tabs open');
            } else if (err.code === 'unimplemented') {
              // The current browser does not support persistence
              console.warn('Firebase persistence not supported in this browser');
            }
          });
      }
      
      initialized = true;
      console.log('Firebase initialized successfully (without Auth)');
    }
    
    return firebase.app();
  } catch (error) {
    console.error('Error initializing Firebase:', error);
    throw error;
  }
}

/**
 * Get Firebase app instance
 * @returns {Object} - Firebase app instance
 */
export function getFirebaseApp() {
  if (!initialized) {
    return initializeFirebase();
  }
  return firebase.app();
}

/**
 * Get Firebase firestore instance
 * @returns {Object} - Firebase firestore instance
 */
export function getFirestore() {
  const app = getFirebaseApp();
  return firebase.firestore(app);
}

/**
 * Get Firebase storage instance
 * @returns {Object} - Firebase storage instance
 */
export function getStorage() {
  const app = getFirebaseApp();
  return firebase.storage(app);
}

/**
 * Get Firebase functions instance
 * @returns {Object} - Firebase functions instance
 */
export function getFunctions() {
  const app = getFirebaseApp();
  return firebase.functions(app);
}