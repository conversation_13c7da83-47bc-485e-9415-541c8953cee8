/**
 * @file firebase-service.js
 * @description Firebase service functionality for Atlas25 Web App
 * @module core/firebase
 */

import {
  getFirebaseApp,
  getFirestore,
  getStorage,
  getFunctions
} from './firebase-config.js';

// Import offline manager for synchronization
import offlineManager from '../offline/offline-manager.js';

/**
 * Firebase service class to handle database operations
 */
class FirebaseService {
  constructor() {
    this.db = null;
    this.storage = null;
    this.functions = null;
    this.initialized = false;
    this.syncQueue = [];
    this.retryConfig = {
      maxRetries: 5,
      retryDelay: 1000, // Initial delay in ms
      backoffFactor: 1.5 // Exponential backoff factor
    };
  }

  /**
   * Initialize Firebase services
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) return;
    
    try {
      getFirebaseApp();
      this.db = getFirestore();
      this.storage = getStorage();
      this.functions = getFunctions();
      this.initialized = true;
      console.log('Firebase services initialized (without Auth)');
    } catch (error) {
      console.error('Failed to initialize Firebase services:', error);
      throw error;
    }
  }

  /**
   * Get a document from Firestore
   * @param {string} collection - Collection name
   * @param {string} docId - Document ID
   * @returns {Promise<Object|null>} - Document data or null if not found
   */
  async getDocument(collection, docId) {
    if (!this.initialized) await this.initialize();
    
    try {
      const docRef = this.db.collection(collection).doc(docId);
      const doc = await docRef.get();
      
      if (doc.exists) {
        return { id: doc.id, ...doc.data() };
      } else {
        console.log(`No document found with ID: ${docId}`);
        return null;
      }
    } catch (error) {
      console.error('Error getting document:', error);
      throw error;
    }
  }

  /**
   * Get documents from a collection with optional query
   * @param {string} collection - Collection name
   * @param {Object} [query] - Query parameters
   * @returns {Promise<Array>} - Array of documents
   */
  async getCollection(collection, query = null) {
    if (!this.initialized) await this.initialize();
    
    try {
      let collectionRef = this.db.collection(collection);
      
      // Apply query if provided
      if (query) {
        if (query.where) {
          const { field, operator, value } = query.where;
          collectionRef = collectionRef.where(field, operator, value);
        }
        
        if (query.orderBy) {
          const { field, direction = 'asc' } = query.orderBy;
          collectionRef = collectionRef.orderBy(field, direction);
        }
        
        if (query.limit) {
          collectionRef = collectionRef.limit(query.limit);
        }
      }
      
      const snapshot = await collectionRef.get();
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting collection:', error);
      throw error;
    }
  }

  /**
   * Add a document to a collection
   * @param {string} collection - Collection name
   * @param {Object} data - Document data
   * @returns {Promise<string>} - New document ID
   */
  async addDocument(collection, data) {
    if (!this.initialized) await this.initialize();
    
    try {
      const timestamp = new Date();
      const docRef = await this.db.collection(collection).add({
        ...data,
        createdAt: timestamp,
        updatedAt: timestamp
      });
      
      console.log(`Document added with ID: ${docRef.id}`);
      return docRef.id;
    } catch (error) {
      console.error('Error adding document:', error);
      throw error;
    }
  }

  /**
   * Update a document in a collection
   * @param {string} collection - Collection name
   * @param {string} docId - Document ID
   * @param {Object} data - Document data to update
   * @returns {Promise<void>}
   */
  async updateDocument(collection, docId, data) {
    if (!this.initialized) await this.initialize();
    
    try {
      const docRef = this.db.collection(collection).doc(docId);
      await docRef.update({
        ...data,
        updatedAt: new Date()
      });
      
      console.log(`Document ${docId} updated successfully`);
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  }

  /**
   * Delete a document from a collection
   * @param {string} collection - Collection name
   * @param {string} docId - Document ID
   * @returns {Promise<void>}
   */
  async deleteDocument(collection, docId) {
    if (!this.initialized) await this.initialize();
    
    try {
      await this.db.collection(collection).doc(docId).delete();
      console.log(`Document ${docId} deleted successfully`);
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }

  /**
   * Upload a file to Firebase Storage
   * @param {string} path - Storage path
   * @param {File|Blob} file - File to upload
   * @param {Object} [metadata] - File metadata
   * @returns {Promise<string>} - Download URL
   */
  async uploadFile(path, file, metadata = {}) {
    if (!this.initialized) await this.initialize();
    
    try {
      const storageRef = this.storage.ref(path);
      const uploadTask = await storageRef.put(file, metadata);
      const downloadURL = await uploadTask.ref.getDownloadURL();
      
      console.log('File uploaded successfully');
      return downloadURL;
    } catch (error) {
      console.error('Error uploading file:', error);
      
      // If offline, queue for later upload
      if (!navigator.onLine) {
        await this.queueOperation('uploadFile', { path, file, metadata });
        return `pending-upload-${Date.now()}`;
      }
      
      throw error;
    }
  }

  /**
   * Call a Firebase Cloud Function
   * @param {string} functionName - Function name
   * @param {Object} [data] - Function parameters
   * @returns {Promise<any>} - Function result
   */
  async callFunction(functionName, data = {}) {
    if (!this.initialized) await this.initialize();
    
    try {
      const func = this.functions.httpsCallable(functionName);
      const result = await func(data);
      return result.data;
    } catch (error) {
      console.error(`Error calling function ${functionName}:`, error);
      
      // If offline, queue for later execution
      if (!navigator.onLine) {
        await this.queueOperation('callFunction', { functionName, data });
        return { pending: true, operationId: `pending-function-${Date.now()}` };
      }
      
      throw error;
    }
  }
  
  /**
   * Get user data from Firestore
   * @param {string} memberstackId - Memberstack User ID
   * @returns {Promise<Object|null>} - User data or null if not found
   */
  async getUserData(memberstackId) {
    return this.getDocument('users', memberstackId);
  }
  
  /**
   * Create or update user data in Firestore
   * @param {string} memberstackId - Memberstack User ID
   * @param {Object} userData - User data to save
   * @returns {Promise<void>}
   */
  async saveUserData(memberstackId, userData) {
    try {
      // Check if user document exists
      const existingUser = await this.getDocument('users', memberstackId);
      
      if (existingUser) {
        await this.updateDocument('users', memberstackId, userData);
      } else {
        await this.db.collection('users').doc(memberstackId).set({
          ...userData,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      
      // Store in local storage for offline access
      this.saveUserToLocalStorage(memberstackId, userData);
      
      console.log(`User data saved for ${memberstackId}`);
    } catch (error) {
      console.error('Error saving user data:', error);
      
      // If offline, save locally and queue for sync
      if (!navigator.onLine) {
        this.saveUserToLocalStorage(memberstackId, userData);
        await this.queueOperation('saveUserData', { userId: memberstackId, userData });
        return;
      }
      
      throw error;
    }
  }
  
  /**
   * Save user data to local storage
   * @param {string} memberstackId - Memberstack User ID
   * @param {Object} userData - User data to save
   * @private
   */
  saveUserToLocalStorage(memberstackId, userData) {
    try {
      const storageKey = `atlas25_user_${memberstackId}`;
      const dataToStore = {
        ...userData,
        lastUpdated: new Date().toISOString()
      };
      localStorage.setItem(storageKey, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Error saving user to local storage:', error);
    }
  }
  
  /**
   * Get user data from local storage
   * @param {string} memberstackId - Memberstack User ID
   * @returns {Object|null} - User data or null if not found
   */
  getUserFromLocalStorage(memberstackId) {
    try {
      const storageKey = `atlas25_user_${memberstackId}`;
      const userData = localStorage.getItem(storageKey);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting user from local storage:', error);
      return null;
    }
  }
  
  /**
   * Queue an operation for later execution when online
   * @param {string} operationType - Type of operation
   * @param {Object} operationData - Operation data
   * @returns {Promise<void>}
   * @private
   */
  async queueOperation(operationType, operationData) {
    const operation = {
      id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: operationType,
      data: operationData,
      timestamp: new Date().toISOString(),
      retryCount: 0
    };
    
    // Add to sync queue
    this.syncQueue.push(operation);
    
    // Notify offline manager
    await offlineManager.addToSyncQueue(operation);
    
    console.log(`Operation queued for later execution: ${operationType}`);
  }
  
  /**
   * Process pending operations in the sync queue
   * @returns {Promise<{success: number, failed: number}>} - Results of sync attempt
   */
  async processSyncQueue() {
    if (!navigator.onLine) {
      console.log('Still offline, sync deferred');
      return { success: 0, failed: 0 };
    }
    
    if (!this.initialized) await this.initialize();
    
    let successCount = 0;
    let failedCount = 0;
    
    // Get operations from offline manager
    const pendingOperations = await offlineManager.getSyncQueue();
    this.syncQueue = pendingOperations;
    
    // Process each operation with exponential backoff retry
    for (const operation of this.syncQueue) {
      try {
        await this.executeOperation(operation);
        await offlineManager.removeFromSyncQueue(operation.id);
        successCount++;
      } catch (error) {
        console.error(`Failed to process operation ${operation.id}:`, error);
        
        // Implement retry with exponential backoff
        if (operation.retryCount < this.retryConfig.maxRetries) {
          operation.retryCount++;
          const delay = this.retryConfig.retryDelay *
                        Math.pow(this.retryConfig.backoffFactor, operation.retryCount - 1);
          
          console.log(`Will retry operation ${operation.id} in ${delay}ms (attempt ${operation.retryCount})`);
          await offlineManager.updateInSyncQueue(operation);
          
          // Schedule retry
          setTimeout(() => this.processSyncQueue(), delay);
        } else {
          console.error(`Operation ${operation.id} failed after ${this.retryConfig.maxRetries} attempts`);
          failedCount++;
          await offlineManager.removeFromSyncQueue(operation.id);
        }
      }
    }
    
    // Update sync queue
    this.syncQueue = await offlineManager.getSyncQueue();
    
    return { success: successCount, failed: failedCount };
  }
  
  /**
   * Execute a queued operation
   * @param {Object} operation - Operation to execute
   * @returns {Promise<any>} - Operation result
   * @private
   */
  async executeOperation(operation) {
    switch (operation.type) {
      case 'saveUserData':
        const { userId, userData } = operation.data;
        return this.saveUserData(userId, userData); // userId is now memberstackId
        
      case 'uploadFile':
        const { path, file, metadata } = operation.data;
        return this.uploadFile(path, file, metadata);
        
      case 'callFunction':
        const { functionName, data } = operation.data;
        return this.callFunction(functionName, data);
        
      case 'addDocument':
        const { collection, docData } = operation.data;
        return this.addDocument(collection, docData);
        
      case 'updateDocument':
        const { collection: coll, docId, docData: updateData } = operation.data;
        return this.updateDocument(coll, docId, updateData);
        
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }
  
  /**
   * Set up online/offline listeners to trigger sync
   */
  setupNetworkListeners() {
    window.addEventListener('online', async () => {
      console.log('Back online, processing sync queue');
      await this.processSyncQueue();
    });
    
    window.addEventListener('offline', () => {
      console.log('Went offline, operations will be queued');
    });
  }
}

// Create and export a singleton instance
const firebaseService = new FirebaseService();

// Set up network listeners when the service is imported
firebaseService.setupNetworkListeners();

export default firebaseService;