/**
 * @file sync-queue.js
 * @description Manages offline data synchronization queue for Atlas25 Web App
 * @module core/offline
 */

import firebaseService from '../firebase/firebase-service.js';

/**
 * SyncQueue class for managing offline data synchronization
 */
class SyncQueue {
  constructor() {
    this.dbName = 'atlas25-offline-db';
    this.storeName = 'sync-queue';
    this.fileStoreName = 'offline-files';
    this.db = null;
    this.initialized = false;
    this.syncInProgress = false;
    this.retryConfig = {
      maxRetries: 5,
      baseDelay: 1000, // 1 second
      maxDelay: 60000, // 1 minute
      backoffFactor: 1.5
    };
  }

  /**
   * Initialize the IndexedDB database for the sync queue
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) return;
    
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, 1);
      
      request.onerror = (event) => {
        console.error('Failed to open IndexedDB:', event.target.error);
        reject(event.target.error);
      };
      
      request.onsuccess = (event) => {
        this.db = event.target.result;
        this.initialized = true;
        console.log('SyncQueue IndexedDB initialized');
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create object store for sync queue if it doesn't exist
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'id', autoIncrement: true });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          store.createIndex('endpoint', 'endpoint', { unique: false });
          store.createIndex('status', 'status', { unique: false });
          console.log('Created sync queue object store');
        }
        
        // Create object store for offline files if it doesn't exist
        if (!db.objectStoreNames.contains(this.fileStoreName)) {
          const fileStore = db.createObjectStore(this.fileStoreName, { keyPath: 'id', autoIncrement: true });
          fileStore.createIndex('filename', 'filename', { unique: false });
          fileStore.createIndex('timestamp', 'timestamp', { unique: false });
          fileStore.createIndex('type', 'type', { unique: false });
          console.log('Created offline files object store');
        }
      };
    });
  }

  /**
   * Add an item to the sync queue
   * @param {Object} item - The item to add to the queue
   * @param {string} item.endpoint - API endpoint
   * @param {string} item.method - HTTP method (GET, POST, PUT, DELETE)
   * @param {Object} item.data - Data to be sent
   * @returns {Promise<number>} - ID of the added item
   */
  async addToQueue(item) {
    if (!this.initialized) await this.initialize();
    
    const queueItem = {
      ...item,
      timestamp: new Date().getTime(),
      attempts: 0,
      status: 'pending',
      lastAttempt: null,
      conflictResolution: item.conflictResolution || 'server-wins',
      priority: item.priority || 'normal',
      deviceId: this.getDeviceId()
    };
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.add(queueItem);
      
      request.onsuccess = (event) => {
        console.log('Item added to sync queue:', event.target.result);
        
        // Register for sync if supported
        if ('serviceWorker' in navigator && 'SyncManager' in window) {
          navigator.serviceWorker.ready
            .then(registration => {
              registration.sync.register('sync-new-data')
                .then(() => console.log('Sync registered'))
                .catch(err => console.error('Sync registration failed:', err));
            });
        } else {
          console.log('Background Sync not supported');
          // Try to sync immediately
          this.processQueue();
        }
        
        resolve(event.target.result);
      };
      
      request.onerror = (event) => {
        console.error('Error adding item to sync queue:', event.target.error);
        reject(event.target.error);
      };
    });
  }

  /**
   * Get all items in the sync queue
   * @returns {Promise<Array>} - Array of queue items
   */
  async getQueue() {
    if (!this.initialized) await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.getAll();
      
      request.onsuccess = (event) => {
        resolve(event.target.result);
      };
      
      request.onerror = (event) => {
        console.error('Error getting sync queue:', event.target.error);
        reject(event.target.error);
      };
    });
  }

  /**
   * Remove an item from the sync queue
   * @param {number} id - ID of the item to remove
   * @returns {Promise<void>}
   */
  async removeFromQueue(id) {
    if (!this.initialized) await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(id);
      
      request.onsuccess = () => {
        console.log('Item removed from sync queue:', id);
        resolve();
      };
      
      request.onerror = (event) => {
        console.error('Error removing item from sync queue:', event.target.error);
        reject(event.target.error);
      };
    });
  }

  /**
   * Update an item in the sync queue
   * @param {Object} item - The item to update
   * @returns {Promise<void>}
   */
  async updateQueueItem(item) {
    if (!this.initialized) await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.put(item);
      
      request.onsuccess = () => {
        console.log('Item updated in sync queue:', item.id);
        resolve();
      };
      
      request.onerror = (event) => {
        console.error('Error updating item in sync queue:', event.target.error);
        reject(event.target.error);
      };
    });
  }

  /**
   * Process the sync queue
   * @returns {Promise<void>}
   */
  async processQueue() {
    if (!navigator.onLine) {
      console.log('Offline, cannot process sync queue');
      return { processed: 0, failed: 0, remaining: 0 };
    }
    
    if (this.syncInProgress) {
      console.log('Sync already in progress');
      return { processed: 0, failed: 0, remaining: 0 };
    }
    
    if (!this.initialized) await this.initialize();
    
    this.syncInProgress = true;
    let processed = 0;
    let failed = 0;
    
    try {
      // Get all pending items, sorted by priority and timestamp
      const queue = await this.getQueueByStatus('pending');
      const sortedQueue = this.sortQueueByPriority(queue);
      
      console.log(`Processing sync queue: ${sortedQueue.length} items`);
      
      for (const item of sortedQueue) {
        try {
          // Check if we should process this item now based on backoff
          if (this.shouldProcessItem(item)) {
            console.log(`Processing queue item ${item.id}: ${item.endpoint}`);
            
            // Mark as in-progress
            item.status = 'in-progress';
            item.lastAttempt = new Date().getTime();
            await this.updateQueueItem(item);
            
            // Try to sync
            await this.syncItem(item);
            
            // If successful, remove from queue
            await this.removeFromQueue(item.id);
            processed++;
            
            // Dispatch event for successful sync
            this.dispatchSyncEvent('sync-success', { item });
          }
        } catch (error) {
          console.error(`Error syncing item ${item.id}:`, error);
          
          // Update attempt count and status with exponential backoff
          item.attempts += 1;
          item.lastAttempt = new Date().getTime();
          item.lastError = error.message;
          
          if (item.attempts >= this.retryConfig.maxRetries) {
            item.status = 'failed';
            failed++;
            this.dispatchSyncEvent('sync-failed', { item, error });
          } else {
            item.status = 'pending';
            // Calculate next retry time with exponential backoff
            const backoffDelay = Math.min(
              this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, item.attempts),
              this.retryConfig.maxDelay
            );
            item.nextRetry = new Date().getTime() + backoffDelay;
          }
          
          await this.updateQueueItem(item);
        }
      }
      
      // Get remaining items count
      const remaining = await this.getQueueByStatus('pending');
      
      return { processed, failed, remaining: remaining.length };
    } catch (error) {
      console.error('Error processing sync queue:', error);
      return { processed, failed, remaining: 0, error: error.message };
    } finally {
      this.syncInProgress = false;
    }
  }
  
  /**
   * Sort queue items by priority and timestamp
   * @param {Array} queue - Queue items to sort
   * @returns {Array} - Sorted queue items
   * @private
   */
  sortQueueByPriority(queue) {
    const priorityValues = {
      'high': 3,
      'normal': 2,
      'low': 1
    };
    
    return [...queue].sort((a, b) => {
      // First sort by priority
      const priorityDiff = priorityValues[b.priority] - priorityValues[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      // Then sort by timestamp (older first)
      return a.timestamp - b.timestamp;
    });
  }
  
  /**
   * Check if an item should be processed now based on backoff
   * @param {Object} item - Queue item
   * @returns {boolean} - True if the item should be processed
   * @private
   */
  shouldProcessItem(item) {
    // If no next retry time or it's in the past, process it
    return !item.nextRetry || item.nextRetry <= new Date().getTime();
  }
  
  /**
   * Get queue items by status
   * @param {string} status - Status to filter by
   * @returns {Promise<Array>} - Array of queue items
   */
  async getQueueByStatus(status) {
    if (!this.initialized) await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const index = store.index('status');
      const request = index.getAll(status);
      
      request.onsuccess = (event) => {
        resolve(event.target.result);
      };
      
      request.onerror = (event) => {
        console.error('Error getting sync queue by status:', event.target.error);
        reject(event.target.error);
      };
    });
  }

  /**
   * Sync a single item
   * @param {Object} item - The item to sync
   * @returns {Promise<void>}
   */
  async syncItem(item) {
    console.log('Syncing item:', item);
    
    // Check for conflicts before syncing
    const hasConflict = await this.checkForConflicts(item);
    
    if (hasConflict) {
      console.log(`Conflict detected for item ${item.id}`);
      await this.resolveConflict(item);
    }
    
    // Handle different types of sync operations
    switch (item.endpoint) {
      case 'firestore':
        return this.syncFirestoreItem(item);
      
      case 'storage':
        return this.syncStorageItem(item);
      
      case 'api':
        return this.syncApiItem(item);
        
      case 'indexeddb':
        return this.syncIndexedDBItem(item);
      
      default:
        // Default to REST API call
        return this.syncApiItem(item);
    }
  }
  
  /**
   * Check for conflicts before syncing
   * @param {Object} item - The item to check for conflicts
   * @returns {Promise<boolean>} - True if there is a conflict
   * @private
   */
  async checkForConflicts(item) {
    // Skip conflict check for certain operations
    if (item.method === 'GET' || item.endpoint === 'storage') {
      return false;
    }
    
    try {
      // For Firestore operations, check if the document has been modified
      if (item.endpoint === 'firestore') {
        const { collection, docId } = item.data;
        
        // Skip if no docId (e.g., for add operations)
        if (!docId) return false;
        
        // Get the current version from Firestore
        const currentDoc = await firebaseService.getDocument(collection, docId);
        
        // If document doesn't exist anymore, it's a conflict
        if (!currentDoc && item.method !== 'ADD') {
          return true;
        }
        
        // If document has been updated since we queued the operation
        if (currentDoc && item.data.lastUpdated &&
            new Date(currentDoc.updatedAt) > new Date(item.data.lastUpdated)) {
          return true;
        }
      }
      
      return false;
    } catch (error) {
      console.error('Error checking for conflicts:', error);
      return false; // Assume no conflict if check fails
    }
  }
  
  /**
   * Resolve a conflict
   * @param {Object} item - The item with a conflict
   * @returns {Promise<void>}
   * @private
   */
  async resolveConflict(item) {
    console.log(`Resolving conflict for item ${item.id} using strategy: ${item.conflictResolution}`);
    
    switch (item.conflictResolution) {
      case 'server-wins':
        // Skip the operation
        throw new Error('Operation skipped due to conflict (server-wins)');
        
      case 'client-wins':
        // Continue with the operation
        console.log('Continuing with operation despite conflict (client-wins)');
        return;
        
      case 'merge':
        // For Firestore operations, merge the data
        if (item.endpoint === 'firestore') {
          const { collection, docId, data } = item.data;
          
          // Get the current version from Firestore
          const currentDoc = await firebaseService.getDocument(collection, docId);
          
          if (currentDoc) {
            // Merge the data
            item.data.data = {
              ...currentDoc,
              ...data,
              updatedAt: new Date() // Use current time
            };
            
            console.log('Merged data for conflict resolution:', item.data.data);
          }
        }
        return;
        
      default:
        // Default to server-wins
        throw new Error('Operation skipped due to conflict (default: server-wins)');
    }
  }

  /**
   * Sync a Firestore item
   * @param {Object} item - The item to sync
   * @returns {Promise<void>}
   */
  async syncFirestoreItem(item) {
    const { collection, docId, method, data } = item.data;
    
    switch (method) {
      case 'ADD':
        await firebaseService.addDocument(collection, data);
        break;
      
      case 'UPDATE':
        await firebaseService.updateDocument(collection, docId, data);
        break;
      
      case 'DELETE':
        await firebaseService.deleteDocument(collection, docId);
        break;
      
      default:
        throw new Error(`Unknown Firestore method: ${method}`);
    }
  }

  /**
   * Sync a Storage item
   * @param {Object} item - The item to sync
   * @returns {Promise<void>}
   */
  async syncStorageItem(item) {
    const { path, fileId, metadata } = item.data;
    
    // For storage items, we need to retrieve the file from IndexedDB
    const fileData = await this.getFileFromStorage(fileId);
    
    if (!fileData) {
      throw new Error('File not found in storage');
    }
    
    // Upload the file
    const downloadURL = await firebaseService.uploadFile(path, fileData.blob, metadata);
    
    // Update the file record with the download URL
    await this.updateFileRecord(fileId, { downloadURL, synced: true });
    
    return downloadURL;
  }

  /**
   * Sync an API item
   * @param {Object} item - The item to sync
   * @returns {Promise<void>}
   */
  async syncApiItem(item) {
    const { endpoint, method, data, headers = {} } = item;
    
    const response = await fetch(endpoint, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: method !== 'GET' ? JSON.stringify(data) : undefined
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * Get a file from IndexedDB storage
   * @param {string} fileId - ID of the file to retrieve
   * @returns {Promise<Object|null>} - The file record with blob, or null if not found
   */
  async getFileFromStorage(fileId) {
    if (!this.initialized) await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.fileStoreName], 'readonly');
      const store = transaction.objectStore(this.fileStoreName);
      const request = store.get(parseInt(fileId, 10));
      
      request.onsuccess = (event) => {
        const file = event.target.result;
        if (file) {
          console.log(`File found in storage: ${file.filename}`);
          resolve(file);
        } else {
          console.log(`File not found in storage: ${fileId}`);
          resolve(null);
        }
      };
      
      request.onerror = (event) => {
        console.error('Error getting file from storage:', event.target.error);
        reject(event.target.error);
      };
    });
  }
  
  /**
   * Store a file in IndexedDB for offline use
   * @param {File|Blob} file - The file to store
   * @param {Object} metadata - File metadata
   * @returns {Promise<number>} - ID of the stored file
   */
  async storeFile(file, metadata = {}) {
    if (!this.initialized) await this.initialize();
    
    const fileRecord = {
      blob: file,
      filename: metadata.filename || 'unknown',
      type: file.type,
      size: file.size,
      timestamp: new Date().getTime(),
      synced: false,
      metadata
    };
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.fileStoreName], 'readwrite');
      const store = transaction.objectStore(this.fileStoreName);
      const request = store.add(fileRecord);
      
      request.onsuccess = (event) => {
        const fileId = event.target.result;
        console.log(`File stored in IndexedDB with ID: ${fileId}`);
        resolve(fileId);
      };
      
      request.onerror = (event) => {
        console.error('Error storing file in IndexedDB:', event.target.error);
        reject(event.target.error);
      };
    });
  }
  
  /**
   * Update a file record in IndexedDB
   * @param {number} fileId - ID of the file to update
   * @param {Object} updates - Updates to apply
   * @returns {Promise<void>}
   */
  async updateFileRecord(fileId, updates) {
    if (!this.initialized) await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.fileStoreName], 'readwrite');
      const store = transaction.objectStore(this.fileStoreName);
      const request = store.get(parseInt(fileId, 10));
      
      request.onsuccess = (event) => {
        const file = event.target.result;
        if (file) {
          const updatedFile = { ...file, ...updates };
          const updateRequest = store.put(updatedFile);
          
          updateRequest.onsuccess = () => {
            console.log(`File record updated: ${fileId}`);
            resolve();
          };
          
          updateRequest.onerror = (event) => {
            console.error('Error updating file record:', event.target.error);
            reject(event.target.error);
          };
        } else {
          reject(new Error(`File not found: ${fileId}`));
        }
      };
      
      request.onerror = (event) => {
        console.error('Error getting file for update:', event.target.error);
        reject(event.target.error);
      };
    });
  }
  
  /**
   * Sync an IndexedDB item
   * @param {Object} item - The item to sync
   * @returns {Promise<void>}
   */
  async syncIndexedDBItem(item) {
    const { operation, data } = item.data;
    
    console.log(`Syncing IndexedDB operation: ${operation}`);
    
    // Implement specific IndexedDB sync operations here
    // This is a placeholder for custom IndexedDB sync logic
    console.log('IndexedDB sync operation completed');
    
    return Promise.resolve();
  }
  
  /**
   * Get a unique device identifier
   * @returns {string} - Device ID
   * @private
   */
  getDeviceId() {
    let deviceId = localStorage.getItem('atlas25_device_id');
    
    if (!deviceId) {
      deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      localStorage.setItem('atlas25_device_id', deviceId);
    }
    
    return deviceId;
  }
  
  /**
   * Dispatch a sync event
   * @param {string} eventName - Event name
   * @param {Object} data - Event data
   * @private
   */
  dispatchSyncEvent(eventName, data) {
    const event = new CustomEvent(eventName, { detail: data });
    window.dispatchEvent(event);
  }
}

// Create and export a singleton instance
const syncQueue = new SyncQueue();
export default syncQueue;

// Listen for online/offline events
window.addEventListener('online', () => {
  console.log('App is online');
  syncQueue.processQueue().then(result => {
    console.log('Sync queue processed:', result);
  });
});

window.addEventListener('offline', () => {
  console.log('App is offline');
});

// Listen for sync events from service worker
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'sync-requested') {
      console.log('Sync requested by service worker');
      syncQueue.processQueue();
    }
  });
}