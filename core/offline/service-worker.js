/**
 * @file service-worker.js
 * @description Service Worker for Atlas25 Web App to enable offline functionality
 * 
 * This service worker handles:
 * 1. Caching of static assets
 * 2. Offline fallback pages
 * 3. Background sync for offline data submission
 */

// Cache names with versioning
const CACHE_VERSION = 'v2';
const STATIC_CACHE_NAME = `atlas25-static-${CACHE_VERSION}`;
const DYNAMIC_CACHE_NAME = `atlas25-dynamic-${CACHE_VERSION}`;
const OFFLINE_CACHE_NAME = `atlas25-offline-${CACHE_VERSION}`;
const WEBFLOW_CACHE_NAME = `atlas25-webflow-${CACHE_VERSION}`;

// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/offline.html',
  '/assets/css/main.css',
  '/assets/js/main.js',
  '/assets/images/logo.png',
  '/assets/images/offline.svg',
  '/assets/fonts/nba-atlas25.woff2',
  '/assets/icons/favicon.ico',
  '/assets/icons/app-icon-192.png',
  '/assets/icons/app-icon-512.png'
];

// Webflow pages to cache
const WEBFLOW_PAGES = [
  '/home',
  '/leaderboard',
  '/quiz',
  '/ar-experience',
  '/onboarding'
];

// Install event - cache static assets
self.addEventListener('install', event => {
  console.log('[Service Worker] Installing Service Worker...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE_NAME)
        .then(cache => {
          console.log('[Service Worker] Precaching static assets');
          return cache.addAll(STATIC_ASSETS);
        }),
      
      // Cache offline page
      caches.open(OFFLINE_CACHE_NAME)
        .then(cache => {
          console.log('[Service Worker] Precaching offline page');
          return cache.add('/offline.html');
        }),
        
      // Cache Webflow pages
      caches.open(WEBFLOW_CACHE_NAME)
        .then(cache => {
          console.log('[Service Worker] Precaching Webflow pages');
          return Promise.all(
            WEBFLOW_PAGES.map(page => {
              return fetch(page)
                .then(response => {
                  if (response.ok) {
                    return cache.put(page, response);
                  }
                })
                .catch(err => console.warn(`Failed to cache ${page}:`, err));
            })
          );
        })
    ])
    .then(() => {
      console.log('[Service Worker] Skip waiting on install');
      return self.skipWaiting();
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('[Service Worker] Activating Service Worker...');
  
  event.waitUntil(
    caches.keys()
      .then(keyList => {
        return Promise.all(keyList.map(key => {
          // Keep current version caches, delete old ones
          if (!key.includes(CACHE_VERSION)) {
            console.log('[Service Worker] Removing old cache', key);
            return caches.delete(key);
          }
        }));
      })
      .then(() => {
        console.log('[Service Worker] Claiming clients');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
  const url = new URL(event.request.url);
  
  // Skip for browser extensions and Chrome-specific URLs
  if (
    !url.protocol.startsWith('http') || 
    url.hostname === 'chrome-extension' ||
    url.pathname.startsWith('/extension')
  ) {
    return;
  }
  
  // Skip for non-GET requests
  if (event.request.method !== 'GET') {
    // For API requests that fail when offline, add to sync queue
    if ((event.request.url.includes('/api/') || event.request.url.includes('firestore.googleapis.com')) && !navigator.onLine) {
      // Queue the request for later sync
      return queueOfflineRequest(event.request);
    }
    return;
  }
  
  // Handle navigation requests - network first with offline fallback
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // Cache a copy of the response
          const responseClone = response.clone();
          caches.open(WEBFLOW_CACHE_NAME)
            .then(cache => {
              cache.put(event.request, responseClone);
            });
          return response;
        })
        .catch(() => {
          // Try to get from cache first
          return caches.match(event.request)
            .then(cachedResponse => {
              if (cachedResponse) {
                return cachedResponse;
              }
              // If not in cache, return offline page
              return caches.match('/offline.html');
            });
        })
    );
    return;
  }
  
  // For static assets - cache first, then network
  if (
    STATIC_ASSETS.includes(url.pathname) ||
    url.pathname.startsWith('/assets/') ||
    url.pathname.endsWith('.css') ||
    url.pathname.endsWith('.js') ||
    url.pathname.endsWith('.woff2') ||
    url.pathname.endsWith('.png') ||
    url.pathname.endsWith('.jpg') ||
    url.pathname.endsWith('.svg') ||
    url.pathname.endsWith('.ico')
  ) {
    event.respondWith(
      caches.match(event.request)
        .then(response => {
          if (response) {
            return response;
          }
          
          return fetch(event.request)
            .then(fetchResponse => {
              return caches.open(STATIC_CACHE_NAME)
                .then(cache => {
                  cache.put(event.request.url, fetchResponse.clone());
                  return fetchResponse;
                });
            })
            .catch(error => {
              console.error('[Service Worker] Fetch failed:', error);
              // Return offline fallback for images
              if (event.request.url.match(/\.(jpg|jpeg|png|gif|svg)$/)) {
                return caches.match('/assets/images/offline.svg');
              }
              return caches.match('/offline.html');
            });
        })
    );
    return;
  }
  
  // For Webflow pages - network first, then cache
  if (WEBFLOW_PAGES.includes(url.pathname)) {
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // Cache a copy of the response
          const responseClone = response.clone();
          caches.open(WEBFLOW_CACHE_NAME)
            .then(cache => {
              cache.put(event.request, responseClone);
            });
          return response;
        })
        .catch(() => {
          return caches.match(event.request)
            .then(cachedResponse => {
              if (cachedResponse) {
                return cachedResponse;
              }
              return caches.match('/offline.html');
            });
        })
    );
    return;
  }

  // For other requests - network first, then cache, then offline fallback
  event.respondWith(
    fetch(event.request)
      .then(response => {
        // Only cache successful responses
        if (!response || response.status !== 200 || response.type !== 'basic') {
          return response;
        }
        
        // Cache a copy of the response
        const responseClone = response.clone();
        caches.open(DYNAMIC_CACHE_NAME)
          .then(cache => {
            cache.put(event.request, responseClone);
          });
        return response;
      })
      .catch(() => {
        return caches.match(event.request)
          .then(cachedResponse => {
            if (cachedResponse) {
              return cachedResponse;
            }
            
            // Return offline fallback for images
            if (event.request.url.match(/\.(jpg|jpeg|png|gif|svg)$/)) {
              return caches.match('/assets/images/offline.svg');
            }
            
            // For API requests, return empty JSON with offline flag
            if (event.request.url.includes('/api/') || event.request.url.includes('firestore.googleapis.com')) {
              return new Response(JSON.stringify({
                offline: true,
                message: 'You are currently offline. This data will be updated when you reconnect.'
              }), {
                headers: { 'Content-Type': 'application/json' }
              });
            }
            
            // Default offline fallback
            return caches.match('/offline.html');
          });
      })
  );
});

// Background sync event
self.addEventListener('sync', event => {
  console.log('[Service Worker] Background Sync', event.tag);
  
  if (event.tag === 'sync-new-data') {
    event.waitUntil(
      syncQueuedData()
        .then(() => {
          console.log('[Service Worker] Sync completed');
          // Notify clients that sync is complete
          self.clients.matchAll().then(clients => {
            clients.forEach(client => {
              client.postMessage({
                type: 'sync-complete',
                timestamp: new Date().toISOString()
              });
            });
          });
        })
        .catch(error => {
          console.error('[Service Worker] Sync failed:', error);
        })
    );
  }
});

/**
 * Queue an offline request for later processing
 * @param {Request} request - The request to queue
 * @returns {Response} - A response indicating the request was queued
 */
async function queueOfflineRequest(request) {
  try {
    // Clone the request to read its body
    const requestClone = request.clone();
    const body = await requestClone.text();
    
    // Create a record of the request
    const requestData = {
      url: request.url,
      method: request.method,
      headers: Array.from(request.headers.entries()),
      body,
      timestamp: Date.now()
    };
    
    // Open the IndexedDB database
    const db = await openDatabase();
    
    // Add the request to the queue
    const tx = db.transaction('offline-requests', 'readwrite');
    await tx.objectStore('offline-requests').add(requestData);
    await tx.complete;
    
    console.log('[Service Worker] Request queued for later:', requestData.url);
    
    // Return a JSON response indicating the request was queued
    return new Response(JSON.stringify({
      offline: true,
      queued: true,
      message: 'Your request has been queued and will be processed when you are back online.'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('[Service Worker] Failed to queue request:', error);
    
    // Return a JSON response indicating the request failed
    return new Response(JSON.stringify({
      offline: true,
      queued: false,
      error: error.message,
      message: 'Failed to queue your request. Please try again when you are back online.'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Open the IndexedDB database
 * @returns {Promise<IDBDatabase>} - The database
 */
function openDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('atlas25-offline-db', 1);
    
    request.onerror = event => {
      reject(new Error('Failed to open IndexedDB'));
    };
    
    request.onsuccess = event => {
      resolve(event.target.result);
    };
    
    request.onupgradeneeded = event => {
      const db = event.target.result;
      
      // Create object store for offline requests if it doesn't exist
      if (!db.objectStoreNames.contains('offline-requests')) {
        db.createObjectStore('offline-requests', {
          keyPath: 'id',
          autoIncrement: true
        });
      }
      
      // Create object store for sync queue if it doesn't exist
      if (!db.objectStoreNames.contains('sync-queue')) {
        const store = db.createObjectStore('sync-queue', {
          keyPath: 'id',
          autoIncrement: true
        });
        store.createIndex('timestamp', 'timestamp', { unique: false });
        store.createIndex('endpoint', 'endpoint', { unique: false });
      }
    };
  });
}

// Push notification event
self.addEventListener('push', event => {
  console.log('[Service Worker] Push Notification received', event);
  
  let data = { title: 'Atlas25 Update', body: 'New content is available.' };
  
  if (event.data) {
    data = JSON.parse(event.data.text());
  }
  
  const options = {
    body: data.body,
    icon: '/assets/images/logo.png',
    badge: '/assets/images/badge.png',
    data: {
      url: data.url || '/'
    }
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Notification click event
self.addEventListener('notificationclick', event => {
  const notification = event.notification;
  const action = event.action;
  const url = notification.data.url;
  
  console.log('[Service Worker] Notification click', action);
  
  notification.close();
  
  event.waitUntil(
    clients.matchAll({ type: 'window' })
      .then(windowClients => {
        // Check if there is already a window open
        for (let client of windowClients) {
          if (client.url === url && 'focus' in client) {
            return client.focus();
          }
        }
        
        // If not, open a new window
        if (clients.openWindow) {
          return clients.openWindow(url);
        }
      })
  );
});

/**
 * Process the sync queue
 * @returns {Promise<void>}
 */
async function syncQueuedData() {
  try {
    // Open the database
    const db = await openDatabase();
    
    // Get all queued requests
    const tx = db.transaction('offline-requests', 'readonly');
    const requests = await tx.objectStore('offline-requests').getAll();
    
    if (requests.length === 0) {
      console.log('[Service Worker] No queued requests to sync');
      return;
    }
    
    console.log(`[Service Worker] Syncing ${requests.length} queued requests`);
    
    // Process each request
    for (const request of requests) {
      try {
        await processQueuedRequest(request, db);
      } catch (error) {
        console.error(`[Service Worker] Failed to process request ${request.id}:`, error);
      }
    }
  } catch (error) {
    console.error('[Service Worker] Failed to sync queued data:', error);
    throw error;
  }
}

/**
 * Process a queued request
 * @param {Object} requestData - The queued request data
 * @param {IDBDatabase} db - The IndexedDB database
 * @returns {Promise<void>}
 */
async function processQueuedRequest(requestData, db) {
  try {
    // Create a new request from the stored data
    const request = new Request(requestData.url, {
      method: requestData.method,
      headers: new Headers(requestData.headers),
      body: requestData.body ? requestData.body : undefined
    });
    
    // Send the request
    const response = await fetch(request);
    
    if (!response.ok) {
      throw new Error(`Request failed with status ${response.status}`);
    }
    
    console.log(`[Service Worker] Successfully processed queued request: ${requestData.url}`);
    
    // Remove the request from the queue
    const tx = db.transaction('offline-requests', 'readwrite');
    await tx.objectStore('offline-requests').delete(requestData.id);
    await tx.complete;
  } catch (error) {
    console.error(`[Service Worker] Failed to process queued request: ${requestData.url}`, error);
    
    // If the request has been retried too many times, remove it
    if (requestData.retryCount && requestData.retryCount >= 5) {
      const tx = db.transaction('offline-requests', 'readwrite');
      await tx.objectStore('offline-requests').delete(requestData.id);
      await tx.complete;
      
      console.log(`[Service Worker] Removed failed request after 5 retries: ${requestData.url}`);
    } else {
      // Otherwise, increment the retry count and update the request
      const tx = db.transaction('offline-requests', 'readwrite');
      requestData.retryCount = (requestData.retryCount || 0) + 1;
      requestData.lastRetry = Date.now();
      await tx.objectStore('offline-requests').put(requestData);
      await tx.complete;
      
      console.log(`[Service Worker] Updated retry count for request: ${requestData.url} (${requestData.retryCount}/5)`);
    }
    
    throw error;
  }
}

// Listen for messages from clients
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});