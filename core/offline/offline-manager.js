/**
 * @file offline-manager.js
 * @description Manages offline functionality for Atlas25 Web App
 * @module core/offline
 */

import syncQueue from './sync-queue.js';

/**
 * OfflineManager class for handling offline functionality
 */
class OfflineManager {
  constructor() {
    this.isOnline = navigator.onLine;
    this.serviceWorkerRegistration = null;
    this.offlineListeners = [];
    this.onlineListeners = [];
    this.syncListeners = [];
    this.initialized = false;
    this.offlineIndicators = [];
    this.networkStatus = {
      type: 'unknown',
      downlink: 0,
      rtt: 0,
      effectiveType: 'unknown',
      saveData: false
    };
    this.syncStatus = {
      lastSync: null,
      pendingItems: 0,
      inProgress: false
    };
  }

  /**
   * Initialize the offline manager
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) return;
    
    try {
      // Register service worker if supported
      if ('serviceWorker' in navigator) {
        this.serviceWorkerRegistration = await navigator.serviceWorker.register('/service-worker.js', {
          scope: '/',
          updateViaCache: 'none' // Don't use cached service worker
        });
        
        console.log('Service Worker registered with scope:', this.serviceWorkerRegistration.scope);
      } else {
        console.warn('Service Worker not supported in this browser');
      }
      
      // Initialize sync queue
      await syncQueue.initialize();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Get network information if available
      this.updateNetworkInformation();
      
      // Check for pending sync items
      await this.updateSyncStatus();
      
      // Create default offline indicator if none exists
      this.createDefaultOfflineIndicator();
      
      this.initialized = true;
      console.log('Offline Manager initialized');
      
      // Update UI based on current online status
      this.updateOnlineStatus();
    } catch (error) {
      console.error('Failed to initialize Offline Manager:', error);
      throw error;
    }
  }

  /**
   * Set up event listeners for online/offline events
   */
  setupEventListeners() {
    // Online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      console.log('App is online');
      this.updateNetworkInformation();
      this.updateOnlineStatus();
      
      // Process sync queue with a slight delay to ensure connection is stable
      setTimeout(() => {
        this.syncData();
      }, 1000);
      
      this.notifyOnlineListeners();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
      console.log('App is offline');
      this.updateNetworkInformation();
      this.updateOnlineStatus();
      this.notifyOfflineListeners();
    });
    
    // Network information events
    if ('connection' in navigator) {
      navigator.connection.addEventListener('change', () => {
        this.updateNetworkInformation();
      });
    }
    
    // Listen for sync events from service worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data) {
          switch (event.data.type) {
            case 'sync-complete':
              console.log('Sync completed:', event.data);
              this.updateSyncStatus();
              this.notifySyncListeners('complete', event.data);
              break;
              
            case 'sync-failed':
              console.log('Sync failed:', event.data);
              this.updateSyncStatus();
              this.notifySyncListeners('failed', event.data);
              break;
              
            case 'cache-updated':
              console.log('Cache updated:', event.data);
              break;
          }
        }
      });
    }
    
    // Listen for sync events from sync queue
    window.addEventListener('sync-success', (event) => {
      this.updateSyncStatus();
      this.notifySyncListeners('success', event.detail);
    });
    
    window.addEventListener('sync-failed', (event) => {
      this.updateSyncStatus();
      this.notifySyncListeners('failed', event.detail);
    });
  }

  /**
   * Update UI elements based on online status
   */
  /**
   * Update network information
   */
  updateNetworkInformation() {
    if ('connection' in navigator) {
      const connection = navigator.connection;
      
      this.networkStatus = {
        type: connection.type || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0,
        effectiveType: connection.effectiveType || 'unknown',
        saveData: connection.saveData || false
      };
      
      console.log('Network information updated:', this.networkStatus);
    }
  }
  
  /**
   * Update online status in UI
   */
  updateOnlineStatus() {
    // Update all registered offline indicators
    this.offlineIndicators.forEach(indicator => {
      if (typeof indicator === 'function') {
        // If it's a function, call it with the online status
        indicator(this.isOnline);
      } else if (indicator instanceof HTMLElement) {
        // If it's an HTML element, update its display
        indicator.style.display = this.isOnline ? 'none' : 'block';
        indicator.setAttribute('aria-hidden', this.isOnline ? 'true' : 'false');
      }
    });
    
    // Update default offline indicator if it exists
    const defaultIndicator = document.querySelector('.offline-indicator');
    if (defaultIndicator && !this.offlineIndicators.includes(defaultIndicator)) {
      defaultIndicator.style.display = this.isOnline ? 'none' : 'block';
      defaultIndicator.setAttribute('aria-hidden', this.isOnline ? 'true' : 'false');
    }
    
    // Update offline-sensitive elements
    const offlineElements = document.querySelectorAll('[data-offline-sensitive]');
    offlineElements.forEach(element => {
      if (this.isOnline) {
        element.classList.remove('offline');
        element.removeAttribute('aria-disabled');
      } else {
        element.classList.add('offline');
        if (element.tagName === 'BUTTON' || element.tagName === 'A' || element.tagName === 'INPUT') {
          element.setAttribute('aria-disabled', 'true');
        }
      }
    });
    
    // Update body class for CSS styling
    if (this.isOnline) {
      document.body.classList.remove('is-offline');
    } else {
      document.body.classList.add('is-offline');
    }
    
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('atlas25:connectionchange', {
      detail: {
        online: this.isOnline,
        networkStatus: this.networkStatus
      }
    }));
  }

  /**
   * Add a listener for offline events
   * @param {Function} listener - Function to call when app goes offline
   * @returns {Function} - Function to remove the listener
   */
  addOfflineListener(listener) {
    this.offlineListeners.push(listener);
    return () => {
      this.offlineListeners = this.offlineListeners.filter(l => l !== listener);
    };
  }

  /**
   * Add a listener for online events
   * @param {Function} listener - Function to call when app goes online
   * @returns {Function} - Function to remove the listener
   */
  addOnlineListener(listener) {
    this.onlineListeners.push(listener);
    return () => {
      this.onlineListeners = this.onlineListeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all offline listeners
   */
  notifyOfflineListeners() {
    this.offlineListeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('Error in offline listener:', error);
      }
    });
  }

  /**
   * Notify all online listeners
   */
  notifyOnlineListeners() {
    this.onlineListeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('Error in online listener:', error);
      }
    });
  }

  /**
   * Check if the app is currently online
   * @returns {boolean} - True if online
   */
  isAppOnline() {
    return this.isOnline;
  }

  /**
   * Queue an operation for background sync
   * @param {Object} operation - Operation to queue
   * @returns {Promise<number>} - ID of the queued operation
   */
  /**
   * Create default offline indicator if none exists
   */
  createDefaultOfflineIndicator() {
    // Check if there's already an offline indicator
    if (document.querySelector('.offline-indicator')) {
      return;
    }
    
    // Create a new offline indicator
    const indicator = document.createElement('div');
    indicator.className = 'offline-indicator';
    indicator.setAttribute('aria-live', 'polite');
    indicator.setAttribute('role', 'status');
    indicator.setAttribute('aria-hidden', this.isOnline ? 'true' : 'false');
    
    // Style the indicator
    Object.assign(indicator.style, {
      display: this.isOnline ? 'none' : 'block',
      position: 'fixed',
      top: '0',
      left: '0',
      right: '0',
      backgroundColor: '#f44336',
      color: 'white',
      textAlign: 'center',
      padding: '8px',
      zIndex: '9999',
      fontWeight: 'bold'
    });
    
    // Add content
    indicator.innerHTML = `
      <span>You are currently offline. Some features may be limited.</span>
    `;
    
    // Add to body
    document.body.appendChild(indicator);
    
    // Add to indicators list
    this.offlineIndicators.push(indicator);
  }
  
  /**
   * Register a custom offline indicator
   * @param {HTMLElement|Function} indicator - Element or function to update when online status changes
   * @returns {Function} - Function to unregister the indicator
   */
  registerOfflineIndicator(indicator) {
    this.offlineIndicators.push(indicator);
    
    // If we're already initialized, update the indicator immediately
    if (this.initialized) {
      if (typeof indicator === 'function') {
        indicator(this.isOnline);
      } else if (indicator instanceof HTMLElement) {
        indicator.style.display = this.isOnline ? 'none' : 'block';
        indicator.setAttribute('aria-hidden', this.isOnline ? 'true' : 'false');
      }
    }
    
    // Return function to unregister
    return () => {
      this.offlineIndicators = this.offlineIndicators.filter(i => i !== indicator);
    };
  }
  
  /**
   * Queue an operation for background sync
   * @param {Object} operation - Operation to queue
   * @param {Object} [options] - Queue options
   * @param {string} [options.priority='normal'] - Priority ('high', 'normal', 'low')
   * @param {string} [options.conflictResolution='server-wins'] - Conflict resolution strategy
   * @returns {Promise<number>} - ID of the queued operation
   */
  async queueOperation(operation, options = {}) {
    const queueItem = {
      ...operation,
      priority: options.priority || 'normal',
      conflictResolution: options.conflictResolution || 'server-wins'
    };
    
    const id = await syncQueue.addToQueue(queueItem);
    await this.updateSyncStatus();
    
    return id;
  }

  /**
   * Get all queued operations
   * @returns {Promise<Array>} - Array of queued operations
   */
  /**
   * Get all queued operations
   * @param {string} [status] - Filter by status ('pending', 'failed', etc.)
   * @returns {Promise<Array>} - Array of queued operations
   */
  async getQueuedOperations(status) {
    if (status) {
      return syncQueue.getQueueByStatus(status);
    }
    return syncQueue.getQueue();
  }

  /**
   * Process the sync queue manually
   * @returns {Promise<void>}
   */
  /**
   * Process the sync queue
   * @returns {Promise<Object>} - Results of sync attempt
   */
  async syncData() {
    if (!this.isOnline) {
      console.warn('Cannot sync data while offline');
      return { success: false, reason: 'offline' };
    }
    
    if (this.syncStatus.inProgress) {
      console.warn('Sync already in progress');
      return { success: false, reason: 'in-progress' };
    }
    
    try {
      this.syncStatus.inProgress = true;
      this.notifySyncListeners('start', { timestamp: new Date() });
      
      // Update service worker
      if (this.serviceWorkerRegistration) {
        try {
          await this.serviceWorkerRegistration.update();
        } catch (error) {
          console.warn('Failed to update service worker:', error);
        }
      }
      
      // Process sync queue
      const result = await syncQueue.processQueue();
      
      // Update sync status
      this.syncStatus.lastSync = new Date();
      await this.updateSyncStatus();
      
      this.notifySyncListeners('complete', { result, timestamp: new Date() });
      
      return { success: true, result };
    } catch (error) {
      console.error('Error syncing data:', error);
      this.notifySyncListeners('error', { error, timestamp: new Date() });
      return { success: false, error };
    } finally {
      this.syncStatus.inProgress = false;
    }
  }
  
  /**
   * Update sync status
   * @returns {Promise<void>}
   */
  async updateSyncStatus() {
    try {
      const pendingItems = await syncQueue.getQueueByStatus('pending');
      this.syncStatus.pendingItems = pendingItems.length;
      
      // Dispatch event
      window.dispatchEvent(new CustomEvent('atlas25:syncstatuschange', {
        detail: {
          ...this.syncStatus,
          pendingItems: this.syncStatus.pendingItems
        }
      }));
    } catch (error) {
      console.error('Error updating sync status:', error);
    }
  }

  /**
   * Request permission for push notifications
   * @returns {Promise<string>} - Permission status
   */
  async requestNotificationPermission() {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return 'not-supported';
    }
    
    try {
      const permission = await Notification.requestPermission();
      console.log('Notification permission:', permission);
      
      if (permission === 'granted') {
        this.subscribeToPushNotifications();
      }
      
      return permission;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      throw error;
    }
  }

  /**
   * Subscribe to push notifications
   * @returns {Promise<void>}
   */
  async subscribeToPushNotifications() {
    if (!this.serviceWorkerRegistration) {
      console.warn('Service Worker not registered');
      return;
    }
    
    try {
      // This would be implemented with your push notification service
      console.log('Subscribing to push notifications');
      
      // Example implementation with Firebase Cloud Messaging
      // const messaging = firebase.messaging();
      // const token = await messaging.getToken({
      //   vapidKey: 'YOUR_VAPID_KEY',
      //   serviceWorkerRegistration: this.serviceWorkerRegistration
      // });
      
      // console.log('FCM Token:', token);
      // Save the token to your server
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
      throw error;
    }
  }

  /**
   * Cache a resource for offline use
   * @param {string} url - URL of the resource to cache
   * @returns {Promise<void>}
   */
  /**
   * Cache a resource for offline use
   * @param {string} url - URL of the resource to cache
   * @param {Object} [options] - Cache options
   * @param {string} [options.cacheName] - Name of the cache to use
   * @returns {Promise<void>}
   */
  async cacheResource(url, options = {}) {
    if (!('caches' in window)) {
      console.warn('Cache API not supported');
      return;
    }
    
    try {
      const cacheName = options.cacheName || DYNAMIC_CACHE_NAME;
      const cache = await caches.open(cacheName);
      
      // Fetch the resource
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch resource: ${response.status} ${response.statusText}`);
      }
      
      // Cache the response
      await cache.put(url, response);
      console.log(`Resource cached in ${cacheName}:`, url);
    } catch (error) {
      console.error('Error caching resource:', error);
      throw error;
    }
  }

  /**
   * Check if a resource is cached
   * @param {string} url - URL of the resource to check
   * @returns {Promise<boolean>} - True if resource is cached
   */
  /**
   * Check if a resource is cached
   * @param {string} url - URL of the resource to check
   * @param {Object} [options] - Cache options
   * @param {string} [options.cacheName] - Name of the cache to check
   * @param {boolean} [options.checkAllCaches=false] - Whether to check all caches
   * @returns {Promise<boolean>} - True if resource is cached
   */
  async isResourceCached(url, options = {}) {
    if (!('caches' in window)) {
      return false;
    }
    
    try {
      if (options.checkAllCaches) {
        // Check all caches
        const cacheNames = await caches.keys();
        for (const cacheName of cacheNames) {
          const cache = await caches.open(cacheName);
          const response = await cache.match(url);
          if (response) {
            return true;
          }
        }
        return false;
      } else {
        // Check specific cache
        const cacheName = options.cacheName || DYNAMIC_CACHE_NAME;
        const cache = await caches.open(cacheName);
        const response = await cache.match(url);
        return !!response;
      }
    } catch (error) {
      console.error('Error checking cached resource:', error);
      return false;
    }
  }
  
  /**
   * Add a listener for sync events
   * @param {Function} listener - Function to call when sync events occur
   * @returns {Function} - Function to remove the listener
   */
  addSyncListener(listener) {
    this.syncListeners.push(listener);
    return () => {
      this.syncListeners = this.syncListeners.filter(l => l !== listener);
    };
  }
  
  /**
   * Notify all sync listeners
   * @param {string} status - Sync status ('start', 'complete', 'error', etc.)
   * @param {Object} data - Sync data
   */
  notifySyncListeners(status, data) {
    this.syncListeners.forEach(listener => {
      try {
        listener(status, data);
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }
  
  /**
   * Store data in IndexedDB for offline use
   * @param {string} storeName - Name of the store
   * @param {Object} data - Data to store
   * @param {string|number} [key] - Key to use (optional)
   * @returns {Promise<string|number>} - Key of the stored data
   */
  async storeOfflineData(storeName, data, key = null) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('atlas25-offline-db', 1);
      
      request.onerror = (event) => {
        reject(new Error('Failed to open IndexedDB'));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        try {
          const tx = db.transaction(storeName, 'readwrite');
          const store = tx.objectStore(storeName);
          
          let storeRequest;
          if (key !== null) {
            storeRequest = store.put(data, key);
          } else {
            storeRequest = store.add(data);
          }
          
          storeRequest.onsuccess = (event) => {
            resolve(event.target.result);
          };
          
          storeRequest.onerror = (event) => {
            reject(new Error(`Failed to store data: ${event.target.error}`));
          };
          
          tx.oncomplete = () => {
            db.close();
          };
        } catch (error) {
          db.close();
          reject(error);
        }
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create object store if it doesn't exist
        if (!db.objectStoreNames.contains(storeName)) {
          db.createObjectStore(storeName, { keyPath: key ? null : 'id', autoIncrement: !key });
        }
      };
    });
  }
  
  /**
   * Get data from IndexedDB
   * @param {string} storeName - Name of the store
   * @param {string|number} key - Key to retrieve
   * @returns {Promise<any>} - Retrieved data
   */
  async getOfflineData(storeName, key) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('atlas25-offline-db', 1);
      
      request.onerror = (event) => {
        reject(new Error('Failed to open IndexedDB'));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        try {
          const tx = db.transaction(storeName, 'readonly');
          const store = tx.objectStore(storeName);
          const getRequest = store.get(key);
          
          getRequest.onsuccess = (event) => {
            resolve(event.target.result);
          };
          
          getRequest.onerror = (event) => {
            reject(new Error(`Failed to get data: ${event.target.error}`));
          };
          
          tx.oncomplete = () => {
            db.close();
          };
        } catch (error) {
          db.close();
          reject(error);
        }
      };
    });
  }
  
  /**
   * Get all data from an IndexedDB store
   * @param {string} storeName - Name of the store
   * @returns {Promise<Array>} - All data in the store
   */
  async getAllOfflineData(storeName) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('atlas25-offline-db', 1);
      
      request.onerror = (event) => {
        reject(new Error('Failed to open IndexedDB'));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        try {
          const tx = db.transaction(storeName, 'readonly');
          const store = tx.objectStore(storeName);
          const getAllRequest = store.getAll();
          
          getAllRequest.onsuccess = (event) => {
            resolve(event.target.result);
          };
          
          getAllRequest.onerror = (event) => {
            reject(new Error(`Failed to get all data: ${event.target.error}`));
          };
          
          tx.oncomplete = () => {
            db.close();
          };
        } catch (error) {
          db.close();
          reject(error);
        }
      };
    });
  }
  
  /**
   * Delete data from IndexedDB
   * @param {string} storeName - Name of the store
   * @param {string|number} key - Key to delete
   * @returns {Promise<void>}
   */
  async deleteOfflineData(storeName, key) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('atlas25-offline-db', 1);
      
      request.onerror = (event) => {
        reject(new Error('Failed to open IndexedDB'));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        try {
          const tx = db.transaction(storeName, 'readwrite');
          const store = tx.objectStore(storeName);
          const deleteRequest = store.delete(key);
          
          deleteRequest.onsuccess = () => {
            resolve();
          };
          
          deleteRequest.onerror = (event) => {
            reject(new Error(`Failed to delete data: ${event.target.error}`));
          };
          
          tx.oncomplete = () => {
            db.close();
          };
        } catch (error) {
          db.close();
          reject(error);
        }
      };
    });
  }
}

// Cache names (should match service-worker.js)
const CACHE_VERSION = 'v2';
const STATIC_CACHE_NAME = `atlas25-static-${CACHE_VERSION}`;
const DYNAMIC_CACHE_NAME = `atlas25-dynamic-${CACHE_VERSION}`;
const OFFLINE_CACHE_NAME = `atlas25-offline-${CACHE_VERSION}`;
const WEBFLOW_CACHE_NAME = `atlas25-webflow-${CACHE_VERSION}`;

// Create and export a singleton instance
const offlineManager = new OfflineManager();
export default offlineManager;