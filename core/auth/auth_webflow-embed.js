/**
 * @file auth_webflow-embed.js
 * @description Webflow embed for authentication functionality
 *
 * HOW TO USE:
 * 1. Add this code to a Webflow embed element
 * 2. Ensure you have the following elements with these specific classes:
 *    - .auth-login-form: Form element for login
 *    - .auth-register-form: Form element for registration
 *    - .auth-email-input: Input for email
 *    - .auth-password-input: Input for password
 *    - .auth-name-input: Input for display name (registration only)
 *    - .auth-login-button: But<PERSON> to submit login
 *    - .auth-register-button: Button to submit registration
 *    - .auth-logout-button: Button to logout
 *    - .auth-reset-password-link: Link to reset password
 *    - .auth-error-message: Element to display error messages
 *    - .auth-user-profile: Container that shows when user is logged in
 *    - .auth-login-container: Container that shows when user is logged out
 *    - .auth-user-email: Element to display user email
 *    - .auth-user-name: Element to display user name
 *    - .auth-user-photo: Image element for user photo
 *    - .auth-offline-indicator: Element that shows when in offline mode
 */

// Load Memberstack SDK
(function() {
  const script = document.createElement('script');
  script.src = 'https://api.memberstack.io/static/memberstack.js';
  script.defer = true;
  document.head.appendChild(script);
})();

// Import auth module (in production, this would be loaded from a CDN)
import {
  initAuth,
  signInWithEmailPassword,
  signOut,
  isAuthenticated,
  onAuthStateChanged,
  registerUser,
  updateUserProfile,
  sendPasswordResetEmail,
  getCurrentUser
} from '../auth/auth.js';

/**
 * Initialize the auth functionality in Webflow
 */
function initAuthWebflow() {
  try {
    console.log('Initializing auth in Webflow');
    
    // Initialize auth module with Memberstack config
    initAuth({
      publicKey: 'YOUR_MEMBERSTACK_PUBLIC_KEY',
      devMode: window.location.hostname === 'localhost' ||
               window.location.hostname === '127.0.0.1'
    })
      .then(() => {
        setupEventListeners();
        setupAuthStateListener();
        setupOfflineIndicator();
      })
      .catch(error => {
        console.error('Failed to initialize auth:', error);
        showErrorMessage('Authentication system failed to initialize');
      });
  } catch (error) {
    console.error('Error in auth initialization:', error);
    showErrorMessage('Authentication system failed to initialize');
  }
}

/**
 * Set up event listeners for auth-related elements
 */
function setupEventListeners() {
  try {
    // Login form submission
    const loginForm = document.querySelector('.auth-login-form');
    if (loginForm) {
      loginForm.addEventListener('submit', handleLogin);
    }
    
    // Registration form submission
    const registerForm = document.querySelector('.auth-register-form');
    if (registerForm) {
      registerForm.addEventListener('submit', handleRegistration);
    }
    
    // Logout button
    const logoutButton = document.querySelector('.auth-logout-button');
    if (logoutButton) {
      logoutButton.addEventListener('click', handleLogout);
    }
    
    // Password reset link
    const resetPasswordLink = document.querySelector('.auth-reset-password-link');
    if (resetPasswordLink) {
      resetPasswordLink.addEventListener('click', handlePasswordReset);
    }
    
    // Profile update form
    const profileForm = document.querySelector('.auth-profile-form');
    if (profileForm) {
      profileForm.addEventListener('submit', handleProfileUpdate);
    }
  } catch (error) {
    console.error('Error setting up event listeners:', error);
  }
}

/**
 * Handle login form submission
 * @param {Event} event - Form submission event
 */
async function handleLogin(event) {
  event.preventDefault();
  
  try {
    const emailInput = document.querySelector('.auth-email-input');
    const passwordInput = document.querySelector('.auth-password-input');
    
    if (!emailInput || !passwordInput) {
      showErrorMessage('Login form is missing required fields');
      return;
    }
    
    const email = emailInput.value.trim();
    const password = passwordInput.value;
    
    if (!email || !password) {
      showErrorMessage('Please enter both email and password');
      return;
    }
    
    // Show loading state
    const loginButton = document.querySelector('.auth-login-button');
    if (loginButton) {
      loginButton.disabled = true;
      loginButton.textContent = 'Logging in...';
    }
    
    // Attempt login
    const user = await signInWithEmailPassword(email, password);
    
    // Reset form
    const loginForm = document.querySelector('.auth-login-form');
    if (loginForm) {
      loginForm.reset();
    }
    
    console.log('Login successful:', user.email);
    
  } catch (error) {
    console.error('Login failed:', error);
    showErrorMessage(error.message || 'Login failed. Please check your credentials and try again.');
  } finally {
    // Reset button state
    const loginButton = document.querySelector('.auth-login-button');
    if (loginButton) {
      loginButton.disabled = false;
      loginButton.textContent = 'Log In';
    }
  }
}

/**
 * Handle registration form submission
 * @param {Event} event - Form submission event
 */
async function handleRegistration(event) {
  event.preventDefault();
  
  try {
    const emailInput = document.querySelector('.auth-register-form .auth-email-input');
    const passwordInput = document.querySelector('.auth-register-form .auth-password-input');
    const nameInput = document.querySelector('.auth-register-form .auth-name-input');
    
    if (!emailInput || !passwordInput) {
      showErrorMessage('Registration form is missing required fields');
      return;
    }
    
    const email = emailInput.value.trim();
    const password = passwordInput.value;
    const displayName = nameInput ? nameInput.value.trim() : '';
    
    if (!email || !password) {
      showErrorMessage('Please enter both email and password');
      return;
    }
    
    // Show loading state
    const registerButton = document.querySelector('.auth-register-button');
    if (registerButton) {
      registerButton.disabled = true;
      registerButton.textContent = 'Creating Account...';
    }
    
    // Additional user data
    const userData = {
      displayName: displayName || email.split('@')[0],
      createdAt: new Date().toISOString()
    };
    
    // Attempt registration
    const user = await registerUser(email, password, userData);
    
    // Reset form
    const registerForm = document.querySelector('.auth-register-form');
    if (registerForm) {
      registerForm.reset();
    }
    
    console.log('Registration successful:', user.email);
    
  } catch (error) {
    console.error('Registration failed:', error);
    showErrorMessage(error.message || 'Registration failed. Please try again.');
  } finally {
    // Reset button state
    const registerButton = document.querySelector('.auth-register-button');
    if (registerButton) {
      registerButton.disabled = false;
      registerButton.textContent = 'Create Account';
    }
  }
}

/**
 * Handle profile update form submission
 * @param {Event} event - Form submission event
 */
async function handleProfileUpdate(event) {
  event.preventDefault();
  
  try {
    const nameInput = document.querySelector('.auth-profile-form .auth-name-input');
    const photoInput = document.querySelector('.auth-profile-form .auth-photo-input');
    
    if (!nameInput) {
      showErrorMessage('Profile form is missing required fields');
      return;
    }
    
    const displayName = nameInput.value.trim();
    
    if (!displayName) {
      showErrorMessage('Please enter your name');
      return;
    }
    
    // Show loading state
    const updateButton = document.querySelector('.auth-profile-update-button');
    if (updateButton) {
      updateButton.disabled = true;
      updateButton.textContent = 'Updating...';
    }
    
    // Prepare profile data
    const profileData = { displayName };
    
    // Handle photo upload if present
    if (photoInput && photoInput.files && photoInput.files[0]) {
      const photoFile = photoInput.files[0];
      
      // Upload to Firebase Storage
      const photoURL = await uploadProfilePhoto(photoFile);
      if (photoURL) {
        profileData.photoURL = photoURL;
      }
    }
    
    // Update profile
    const updatedUser = await updateUserProfile(profileData);
    
    console.log('Profile updated successfully');
    
  } catch (error) {
    console.error('Profile update failed:', error);
    showErrorMessage(error.message || 'Profile update failed. Please try again.');
  } finally {
    // Reset button state
    const updateButton = document.querySelector('.auth-profile-update-button');
    if (updateButton) {
      updateButton.disabled = false;
      updateButton.textContent = 'Update Profile';
    }
  }
}

/**
 * Upload profile photo to Firebase Storage
 * @param {File} photoFile - Photo file to upload
 * @returns {Promise<string|null>} - Download URL or null if failed
 */
async function uploadProfilePhoto(photoFile) {
  try {
    const user = getCurrentUser();
    if (!user) throw new Error('No authenticated user');
    
    const path = `profile_photos/${user.uid}/${Date.now()}_${photoFile.name}`;
    
    // Import Firebase service dynamically
    const firebaseService = (await import('../firebase/firebase-service.js')).default;
    
    // Upload file
    const downloadURL = await firebaseService.uploadFile(path, photoFile, {
      contentType: photoFile.type
    });
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading profile photo:', error);
    return null;
  }
}

/**
 * Handle password reset request
 * @param {Event} event - Click event
 */
async function handlePasswordReset(event) {
  event.preventDefault();
  
  try {
    // Get email from input or prompt
    let email = '';
    const emailInput = document.querySelector('.auth-email-input');
    
    if (emailInput && emailInput.value.trim()) {
      email = emailInput.value.trim();
    } else {
      email = prompt('Please enter your email address to reset your password:');
    }
    
    if (!email) {
      return;
    }
    
    // Send password reset email
    await sendPasswordResetEmail(email);
    
    alert(`Password reset email sent to ${email}. Please check your inbox.`);
    
  } catch (error) {
    console.error('Password reset failed:', error);
    showErrorMessage(error.message || 'Failed to send password reset email. Please try again.');
  }
}

/**
 * Handle logout button click
 */
async function handleLogout() {
  try {
    await signOut();
  } catch (error) {
    console.error('Logout failed:', error);
    showErrorMessage('Failed to log out');
  }
}

/**
 * Set up auth state listener to update UI based on authentication state
 */
function setupAuthStateListener() {
  onAuthStateChanged(user => {
    updateUIForAuthState(user);
  });
}

/**
 * Set up offline indicator
 */
function setupOfflineIndicator() {
  const updateOfflineStatus = () => {
    const offlineIndicator = document.querySelector('.auth-offline-indicator');
    if (offlineIndicator) {
      if (navigator.onLine) {
        offlineIndicator.style.display = 'none';
      } else {
        offlineIndicator.style.display = 'block';
      }
    }
  };
  
  // Initial status
  updateOfflineStatus();
  
  // Listen for online/offline events
  window.addEventListener('online', updateOfflineStatus);
  window.addEventListener('offline', updateOfflineStatus);
}

/**
 * Update UI based on authentication state
 * @param {Object|null} user - Current user or null if not authenticated
 */
function updateUIForAuthState(user) {
  const userProfileContainer = document.querySelector('.auth-user-profile');
  const loginContainer = document.querySelector('.auth-login-container');
  const registerContainer = document.querySelector('.auth-register-container');
  
  if (user) {
    // User is signed in
    if (userProfileContainer) userProfileContainer.style.display = 'block';
    if (loginContainer) loginContainer.style.display = 'none';
    if (registerContainer) registerContainer.style.display = 'none';
    
    // Update user profile information
    const userEmailElement = document.querySelector('.auth-user-email');
    if (userEmailElement && user.email) {
      userEmailElement.textContent = user.email;
    }
    
    const userNameElement = document.querySelector('.auth-user-name');
    if (userNameElement && user.displayName) {
      userNameElement.textContent = user.displayName;
    }
    
    const userPhotoElement = document.querySelector('.auth-user-photo');
    if (userPhotoElement && user.photoURL) {
      userPhotoElement.src = user.photoURL;
      userPhotoElement.style.display = 'block';
    } else if (userPhotoElement) {
      userPhotoElement.style.display = 'none';
    }
    
    // Update any profile form fields
    const profileNameInput = document.querySelector('.auth-profile-form .auth-name-input');
    if (profileNameInput) {
      profileNameInput.value = user.displayName || '';
    }
    
  } else {
    // User is signed out
    if (userProfileContainer) userProfileContainer.style.display = 'none';
    if (loginContainer) loginContainer.style.display = 'block';
    if (registerContainer) registerContainer.style.display = 'block';
  }
  
  // Update offline indicator
  const offlineIndicator = document.querySelector('.auth-offline-indicator');
  if (offlineIndicator) {
    if (navigator.onLine) {
      offlineIndicator.style.display = 'none';
    } else {
      offlineIndicator.style.display = 'block';
    }
  }
}

/**
 * Show error message in the designated error element
 * @param {string} message - Error message to display
 */
function showErrorMessage(message) {
  const errorElement = document.querySelector('.auth-error-message');
  if (errorElement) {
    errorElement.textContent = message;
    errorElement.style.display = 'block';
    
    // Hide error after 5 seconds
    setTimeout(() => {
      errorElement.style.display = 'none';
    }, 5000);
  }
}

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initAuthWebflow);

// Fallback for Webflow's preview mode where DOMContentLoaded might have already fired
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initAuthWebflow, 1);
}