/**
 * @file auth.js
 * @description Core authentication functionality for Atlas25 Web App
 * @module core/auth
 */

import firebaseService from '../firebase/firebase-service.js';

/**
 * Memberstack configuration
 * @type {Object}
 */
const memberstackConfig = {
  publicKey: 'YOUR_MEMBERSTACK_PUBLIC_KEY',
  devMode: window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1'
};

/**
 * User authentication state
 * @type {Object|null}
 */
let currentUser = null;

/**
 * Auth state change listeners
 * @type {Array<Function>}
 */
const authListeners = [];

/**
 * Initialize the authentication module
 * @param {Object} options - Configuration options
 * @returns {Promise<void>}
 */
export async function initAuth(options = {}) {
  try {
    console.log('Initializing auth module');
    
    // Merge default options with provided options
    const config = {
      ...memberstackConfig,
      ...options
    };
    
    // Initialize Memberstack if available
    if (window.Memberstack) {
      window.MemberStack = window.Memberstack.init({
        publicKey: config.publicKey,
        dev: config.devMode
      });
      console.log('Memberstack initialized');
    } else {
      console.warn('Memberstack not available, falling back to Firebase auth only');
    }
    
    // Initialize Firebase services (without Auth)
    await firebaseService.initialize();
    
    // Check for Memberstack user
    await checkMemberstackUser();
    
    // Check for cached user in local storage if offline
    if (!navigator.onLine && !currentUser) {
      await checkCachedUser();
    }
    
  } catch (error) {
    console.error('Failed to initialize auth module:', error);
    throw error;
  }
}

/**
 * Check for cached user in local storage
 * @returns {Promise<void>}
 * @private
 */
async function checkCachedUser() {
  try {
    const cachedUser = localStorage.getItem('atlas25_auth_user');
    
    if (cachedUser) {
      const userData = JSON.parse(cachedUser);
      const now = new Date();
      const expiryTime = new Date(userData.expiryTime);
      
      // Check if cached user is still valid
      if (expiryTime > now) {
        currentUser = userData.user;
        notifyAuthStateChange(currentUser);
        console.log('User restored from cache:', currentUser.email);
      } else {
        // Clear expired cache
        localStorage.removeItem('atlas25_auth_user');
      }
    }
  } catch (error) {
    console.error('Error checking cached user:', error);
    localStorage.removeItem('atlas25_auth_user');
  }
}

/**
 * Handle authentication state changes
 * @param {Object|null} user - Firebase user object
 * @private
 */
/**
 * Check for Memberstack user and update current user state
 * @returns {Promise<void>}
 * @private
 */
async function checkMemberstackUser() {
  try {
    if (!window.MemberStack) {
      console.warn('Memberstack not available');
      return;
    }
    
    // Get current member from Memberstack
    const member = await window.MemberStack.getCurrentMember();
    
    if (member) {
      // User is signed in with Memberstack
      const { id, email, metaData } = member;
      
      // Get additional user data from Firestore using Memberstack ID
      let userData = await firebaseService.getUserData(id);
      
      if (!userData) {
        // Create new user record if it doesn't exist
        userData = {
          email,
          displayName: metaData?.displayName || email.split('@')[0],
          photoURL: metaData?.photoURL || null,
          createdAt: new Date().toISOString()
        };
        
        await firebaseService.saveUserData(id, userData);
      }
      
      // Set current user with Memberstack ID and Firestore data
      currentUser = {
        uid: id, // Use Memberstack ID as uid for compatibility
        memberstackId: id,
        email,
        displayName: userData.displayName || metaData?.displayName || email.split('@')[0],
        photoURL: userData.photoURL || metaData?.photoURL,
        ...userData
      };
      
      // Cache user for offline access
      cacheUserLocally(currentUser);
      
      // Notify listeners of auth state change
      notifyAuthStateChange(currentUser);
      console.log('User authenticated with Memberstack:', email);
    } else {
      // No Memberstack user
      currentUser = null;
      localStorage.removeItem('atlas25_auth_user');
      notifyAuthStateChange(null);
    }
  } catch (error) {
    console.error('Error checking Memberstack user:', error);
    
    // If offline, try to use cached user
    if (!navigator.onLine) {
      await checkCachedUser();
    }
  }
}

/**
 * Cache user data locally for offline access
 * @param {Object} user - User object to cache
 * @private
 */
function cacheUserLocally(user) {
  if (!user) return;
  
  try {
    // Set expiry time to 24 hours from now
    const expiryTime = new Date();
    expiryTime.setHours(expiryTime.getHours() + 24);
    
    const userData = {
      user,
      expiryTime: expiryTime.toISOString()
    };
    
    localStorage.setItem('atlas25_auth_user', JSON.stringify(userData));
  } catch (error) {
    console.error('Error caching user locally:', error);
  }
}

/**
 * Notify all listeners of authentication state change
 * @param {Object|null} user - Current user or null if not authenticated
 * @private
 */
function notifyAuthStateChange(user) {
  authListeners.forEach(callback => {
    try {
      callback(user);
    } catch (error) {
      console.error('Error in auth state change listener:', error);
    }
  });
}

/**
 * Sign in a user with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} - User object
 */
export async function signInWithEmailPassword(email, password) {
  try {
    console.log('Signing in user with email:', email);
    
    // Check if Memberstack is available
    if (!window.MemberStack) {
      throw new Error('Memberstack is not available');
    }
    
    // Authenticate with Memberstack
    const memberStackResult = await window.MemberStack.login({
      email,
      password
    });
    
    if (memberStackResult.success) {
      console.log('Memberstack login successful');
      
      // Get the current member after login
      const member = await window.MemberStack.getCurrentMember();
      
      if (!member) {
        throw new Error('Failed to get member after login');
      }
      
      // Get or create user data in Firestore
      let userData = await firebaseService.getUserData(member.id);
      
      if (!userData) {
        // Create new user record if it doesn't exist
        userData = {
          email,
          displayName: member.metaData?.displayName || email.split('@')[0],
          photoURL: member.metaData?.photoURL || null,
          createdAt: new Date().toISOString()
        };
        
        await firebaseService.saveUserData(member.id, userData);
      }
      
      // Set current user
      currentUser = {
        uid: member.id, // Use Memberstack ID as uid for compatibility
        memberstackId: member.id,
        email,
        displayName: userData.displayName || member.metaData?.displayName || email.split('@')[0],
        photoURL: userData.photoURL || member.metaData?.photoURL,
        ...userData
      };
      
      // Cache user for offline access
      cacheUserLocally(currentUser);
      
      // Notify listeners
      notifyAuthStateChange(currentUser);
      
      return currentUser;
    } else {
      throw new Error('Memberstack login failed');
    }
  } catch (error) {
    console.error('Sign in failed:', error);
    
    // Check if offline and try to use cached credentials
    if (!navigator.onLine) {
      return await signInOffline(email, password);
    }
    
    throw error;
  }
}

/**
 * Attempt to sign in when offline using cached credentials
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} - User object
 * @private
 */
async function signInOffline(email, password) {
  try {
    console.log('Attempting offline sign in');
    
    // This is a simplified offline auth - in a real app, you'd want to use
    // a secure hashing mechanism to verify credentials
    
    const cachedUser = localStorage.getItem('atlas25_auth_user');
    if (!cachedUser) {
      throw new Error('No cached user available for offline login');
    }
    
    const userData = JSON.parse(cachedUser);
    
    // Check if the cached user matches the login attempt
    if (userData.user.email === email) {
      // In a real implementation, you would verify the password hash
      // For now, we're just checking if the email matches
      
      currentUser = userData.user;
      notifyAuthStateChange(currentUser);
      
      console.log('Offline login successful');
      return currentUser;
    } else {
      throw new Error('Offline login failed: email does not match cached user');
    }
  } catch (error) {
    console.error('Offline sign in failed:', error);
    throw new Error('Offline authentication failed. Please connect to the internet and try again.');
  }
}

/**
 * Sign out the current user
 * @returns {Promise<void>}
 */
export async function signOut() {
  try {
    console.log('Signing out user');
    
    // Sign out from Memberstack
    if (window.MemberStack) {
      try {
        await window.MemberStack.logout();
        console.log('Memberstack logout successful');
      } catch (memberStackError) {
        console.error('Memberstack logout failed:', memberStackError);
      }
    }
    
    // Clear cached user
    localStorage.removeItem('atlas25_auth_user');
    
    // Update user state
    currentUser = null;
    notifyAuthStateChange(null);
    
  } catch (error) {
    console.error('Sign out failed:', error);
    throw error;
  }
}

/**
 * Get the current authenticated user
 * @returns {Object|null} - Current user or null if not authenticated
 */
export function getCurrentUser() {
  return currentUser;
}

/**
 * Check if user is authenticated
 * @returns {boolean} - True if user is authenticated
 */
export function isAuthenticated() {
  return currentUser !== null;
}

/**
 * Register auth state change listener
 * @param {Function} callback - Function to call when auth state changes
 * @returns {Function} - Unsubscribe function
 */
export function onAuthStateChanged(callback) {
  console.log('Registering auth state change listener');
  
  // Add callback to listeners array
  authListeners.push(callback);
  
  // Immediately call with current state
  if (callback && typeof callback === 'function') {
    setTimeout(() => callback(currentUser), 0);
  }
  
  // Return unsubscribe function
  return () => {
    console.log('Unregistering auth state change listener');
    const index = authListeners.indexOf(callback);
    if (index !== -1) {
      authListeners.splice(index, 1);
    }
  };
}

/**
 * Update user profile information
 * @param {Object} profileData - Profile data to update
 * @returns {Promise<Object>} - Updated user object
 */
export async function updateUserProfile(profileData) {
  try {
    if (!currentUser) {
      throw new Error('No authenticated user');
    }
    
    const { displayName, photoURL, ...otherData } = profileData;
    
    // Update user data in Firestore using Memberstack ID
    await firebaseService.saveUserData(currentUser.memberstackId, {
      ...currentUser,
      ...profileData,
      updatedAt: new Date().toISOString()
    });
    
    // Update Memberstack metadata if available
    if (window.MemberStack) {
      try {
        const member = await window.MemberStack.getCurrentMember();
        if (member) {
          // Update Memberstack metadata
          await member.updateMetaData({
            ...member.metaData,
            displayName: displayName || currentUser.displayName,
            photoURL: photoURL || currentUser.photoURL,
            ...otherData
          });
        }
      } catch (memberStackError) {
        console.error('Error updating Memberstack metadata:', memberStackError);
      }
    }
    
    // Update local user object
    currentUser = {
      ...currentUser,
      ...profileData
    };
    
    // Update cached user
    cacheUserLocally(currentUser);
    
    // Notify listeners
    notifyAuthStateChange(currentUser);
    
    return currentUser;
  } catch (error) {
    console.error('Error updating user profile:', error);
    
    // If offline, update locally and queue for sync
    if (!navigator.onLine) {
      currentUser = {
        ...currentUser,
        ...profileData,
        updatedAt: new Date().toISOString()
      };
      
      cacheUserLocally(currentUser);
      notifyAuthStateChange(currentUser);
      
      // Queue for sync when back online
      await firebaseService.queueOperation('saveUserData', {
        userId: currentUser.memberstackId,
        userData: currentUser
      });
      
      return currentUser;
    }
    
    throw error;
  }
}

/**
 * Register a new user
 * @param {string} email - User email
 * @param {string} password - User password
 * @param {Object} [userData] - Additional user data
 * @returns {Promise<Object>} - New user object
 */
export async function registerUser(email, password, userData = {}) {
  try {
    console.log('Registering new user with email:', email);
    
    // Check if Memberstack is available
    if (!window.MemberStack) {
      throw new Error('Memberstack is not available');
    }
    
    // Register with Memberstack
    const memberStackResult = await window.MemberStack.signup({
      email,
      password
    });
    
    if (!memberStackResult.success) {
      throw new Error('Memberstack registration failed');
    }
    
    console.log('Memberstack registration successful');
    
    // Get the current member after signup
    const member = await window.MemberStack.getCurrentMember();
    
    if (!member) {
      throw new Error('Failed to get member after signup');
    }
    
    // Create user profile
    const userProfile = {
      email,
      displayName: userData.displayName || email.split('@')[0],
      photoURL: userData.photoURL || null,
      createdAt: new Date().toISOString(),
      ...userData
    };
    
    // Save user data to Firestore using Memberstack ID
    await firebaseService.saveUserData(member.id, userProfile);
    
    // Set current user
    currentUser = {
      uid: member.id, // Use Memberstack ID as uid for compatibility
      memberstackId: member.id,
      email,
      displayName: userProfile.displayName,
      photoURL: userProfile.photoURL,
      ...userProfile
    };
    
    // Cache user for offline access
    cacheUserLocally(currentUser);
    
    // Notify listeners
    notifyAuthStateChange(currentUser);
    
    return currentUser;
    
  } catch (error) {
    console.error('Registration failed:', error);
    throw error;
  }
}

/**
 * Send password reset email
 * @param {string} email - User email
 * @returns {Promise<void>}
 */
export async function sendPasswordResetEmail(email) {
  try {
    console.log('Sending password reset email to:', email);
    
    // Check if Memberstack is available
    if (!window.MemberStack) {
      throw new Error('Memberstack is not available');
    }
    
    // Use Memberstack's password reset functionality
    await window.MemberStack.forgotPassword({
      email
    });
    
    console.log('Password reset email sent via Memberstack');
  } catch (error) {
    console.error('Failed to send password reset email:', error);
    throw error;
  }
}

/**
 * Verify if user has required permissions
 * @param {string|Array<string>} permissions - Required permission(s)
 * @returns {boolean} - True if user has all required permissions
 */
export function hasPermission(permissions) {
  if (!currentUser || !currentUser.permissions) {
    return false;
  }
  
  const userPermissions = currentUser.permissions || [];
  
  if (Array.isArray(permissions)) {
    return permissions.every(permission => userPermissions.includes(permission));
  }
  
  return userPermissions.includes(permissions);
}