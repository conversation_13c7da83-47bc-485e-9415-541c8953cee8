/**
 * @file leaderboard.js
 * @description Leaderboard component for Atlas25 Web App
 * @module components/leaderboard
 */

import firebaseService from '../../core/firebase/firebase-service.js';
import stateManager from '../../core/state/state-manager.js';
import offlineManager from '../../core/offline/offline-manager.js';

/**
 * Point categories for leaderboard
 * @enum {string}
 */
export const POINT_CATEGORIES = {
  PARTICIPATION: 'participation',
  QUIZ: 'quiz',
  BONUS: 'bonus',
  TOTAL: 'total'
};

/**
 * Booth types for different experiences
 * @enum {string}
 */
export const BOOTH_TYPES = {
  REFEREE: 'referee',
  MEDIA: 'media',
  COACHING: 'coaching',
  BUSINESS: 'business',
  COMMUNITY: 'community'
};

/**
 * View modes for leaderboard display
 * @enum {string}
 */
export const VIEW_MODES = {
  PERSONAL: 'personal',
  TOP: 'top',
  ALL: 'all'
};

/**
 * Leaderboard class for displaying and managing leaderboards
 */
class Leaderboard {
  /**
   * Create a leaderboard
   * @param {Object} options - Configuration options
   * @param {string} options.boardId - ID of the leaderboard
   * @param {HTMLElement|string} [options.container] - Container element or selector
   * @param {string} [options.title] - Leaderboard title
   * @param {string} [options.sortBy='points.total'] - Field to sort by
   * @param {string} [options.sortDirection='desc'] - Sort direction ('asc' or 'desc')
   * @param {number} [options.limit=10] - Maximum number of entries to display
   * @param {string} [options.viewMode='top'] - View mode (personal, top, all)
   * @param {boolean} [options.showInitialsOnly=false] - Whether to show only initials for privacy
   * @param {Function} [options.renderItem] - Custom function to render an item
   * @param {Function} [options.onUpdate] - Callback function when leaderboard is updated
   * @param {Function} [options.onPointsEarned] - Callback function when points are earned
   */
  constructor(options) {
    this.options = {
      sortBy: 'points.total',
      sortDirection: 'desc',
      limit: 10,
      viewMode: VIEW_MODES.TOP,
      showInitialsOnly: false,
      ...options
    };
    
    this.boardId = options.boardId;
    this.container = typeof options.container === 'string' 
      ? document.querySelector(options.container) 
      : options.container;
      
    this.entries = [];
    this.loading = false;
    this.error = null;
    this.lastUpdated = null;
    this.userEntry = null;
    this.offlineMode = !navigator.onLine;
    this.pendingPoints = [];
    
    // Bind methods
    this.renderItem = this.renderItem.bind(this);
    this.render = this.render.bind(this);
    this.handlePointsEarned = this.handlePointsEarned.bind(this);
    
    // Set up network listeners
    this.setupNetworkListeners();
  }
  
  /**
   * Set up network listeners for online/offline events
   * @private
   */
  setupNetworkListeners() {
    window.addEventListener('online', () => {
      this.offlineMode = false;
      console.log('Leaderboard is now online');
      
      // Sync any pending points
      this.syncPendingPoints();
    });
    
    window.addEventListener('offline', () => {
      this.offlineMode = true;
      console.log('Leaderboard is now offline');
    });
  }

  /**
   * Load leaderboard data
   * @param {boolean} [forceRefresh=false] - Whether to force a refresh from the server
   * @returns {Promise<Array>} - Leaderboard entries
   */
  async loadData(forceRefresh = false) {
    try {
      this.loading = true;
      this.error = null;
      
      if (this.container) {
        this.container.classList.add('loading');
      }
      
      // Check if we have cached data in state
      const cachedData = stateManager.get(`content.leaderboards.${this.boardId}`);
      
      if (!forceRefresh && cachedData && cachedData.entries && cachedData.timestamp) {
        // Check if cache is still valid (less than 5 minutes old)
        const cacheAge = Date.now() - new Date(cachedData.timestamp).getTime();
        if (cacheAge < 5 * 60 * 1000) {
          this.entries = cachedData.entries;
          this.lastUpdated = cachedData.timestamp;
          
          // Get current user's entry
          await this.loadUserEntry();
          
          this.render();
          
          this.loading = false;
          if (this.container) {
            this.container.classList.remove('loading');
          }
          
          return this.entries;
        }
      }
      
      // If offline, use cached data regardless of age
      if (this.offlineMode) {
        if (cachedData && cachedData.entries) {
          this.entries = cachedData.entries;
          this.lastUpdated = cachedData.timestamp;
          
          // Get current user's entry
          await this.loadUserEntry();
          
          this.render();
          
          this.loading = false;
          if (this.container) {
            this.container.classList.remove('loading');
          }
          
          return this.entries;
        } else {
          // Try to load from offline storage
          const offlineData = await this.getOfflineLeaderboard();
          if (offlineData) {
            this.entries = offlineData.entries || [];
            this.lastUpdated = offlineData.timestamp;
            
            // Get current user's entry
            await this.loadUserEntry();
            
            this.render();
            
            this.loading = false;
            if (this.container) {
              this.container.classList.remove('loading');
            }
            
            return this.entries;
          } else {
            throw new Error('No leaderboard data available offline');
          }
        }
      }
      
      // Load data from Firebase
      const query = {
        where: {
          field: 'boardId',
          operator: '==',
          value: this.boardId
        },
        orderBy: {
          field: this.options.sortBy,
          direction: this.options.sortDirection
        },
        limit: this.options.limit
      };
      
      const entries = await firebaseService.getCollection('leaderboard_entries', query);
      
      // Process entries
      this.entries = entries.map((entry, index) => ({
        ...entry,
        rank: index + 1
      }));
      
      // Get current user's entry
      await this.loadUserEntry();
      
      // Cache in state
      this.lastUpdated = new Date().toISOString();
      stateManager.set(`content.leaderboards.${this.boardId}`, {
        entries: this.entries,
        timestamp: this.lastUpdated
      });
      
      // Store for offline use
      await this.storeLeaderboardOffline();
      
      // Render
      this.render();
      
      // Call onUpdate callback
      if (this.options.onUpdate) {
        this.options.onUpdate(this.entries);
      }
      
      return this.entries;
    } catch (error) {
      console.error('Failed to load leaderboard data:', error);
      this.error = error.message || 'Failed to load leaderboard data';
      this.render();
      throw error;
    } finally {
      this.loading = false;
      if (this.container) {
        this.container.classList.remove('loading');
      }
    }
  }
  
  /**
   * Store leaderboard data for offline use
   * @private
   */
  async storeLeaderboardOffline() {
    try {
      await offlineManager.storeOfflineData('leaderboards', {
        entries: this.entries,
        timestamp: this.lastUpdated,
        boardId: this.boardId
      }, this.boardId);
      
      console.log('Stored leaderboard offline:', this.boardId);
    } catch (error) {
      console.error('Failed to store leaderboard offline:', error);
    }
  }
  
  /**
   * Get leaderboard from offline storage
   * @returns {Promise<Object|null>} - Leaderboard data or null if not found
   * @private
   */
  async getOfflineLeaderboard() {
    try {
      return await offlineManager.getOfflineData('leaderboards', this.boardId);
    } catch (error) {
      console.error('Failed to get offline leaderboard:', error);
      return null;
    }
  }
  
  /**
   * Load the current user's entry
   * @private
   */
  async loadUserEntry() {
    try {
      const auth = firebase.auth();
      const userId = auth.currentUser?.uid;
      
      if (userId) {
        this.userEntry = await this.getUserRank(userId);
      } else {
        this.userEntry = null;
      }
    } catch (error) {
      console.error('Failed to load user entry:', error);
      this.userEntry = null;
    }
  }

  /**
   * Add an entry to the leaderboard
   * @param {Object} entry - Leaderboard entry
   * @param {string} entry.userId - User ID
   * @param {string} entry.displayName - Display name
   * @param {Object} entry.points - Points object with categories
   * @param {Object} [entry.metadata] - Additional metadata
   * @returns {Promise<string>} - ID of the new entry
   */
  async addEntry(entry) {
    try {
      // Validate entry
      if (!entry.userId || !entry.displayName) {
        throw new Error('Invalid leaderboard entry');
      }
      
      // Ensure points structure
      const points = {
        participation: 0,
        quiz: 0,
        bonus: 0,
        total: 0,
        ...(entry.points || {})
      };
      
      // Calculate total if not provided
      if (!points.total) {
        points.total = Object.values(points).reduce((sum, val) => {
          return typeof val === 'number' && val !== points.total ? sum + val : sum;
        }, 0);
      }
      
      // Add boardId and timestamp
      const fullEntry = {
        ...entry,
        points,
        boardId: this.boardId,
        timestamp: new Date().toISOString(),
        booths: entry.booths || []
      };
      
      // If offline, queue for later
      if (this.offlineMode) {
        await this.queueEntryForSync(fullEntry);
        
        // Store locally for immediate feedback
        await this.storeEntryOffline(fullEntry);
        
        // Refresh from local data
        await this.loadData(true);
        
        return `pending-${Date.now()}`;
      }
      
      // Add to Firebase
      const entryId = await firebaseService.addDocument('leaderboard_entries', fullEntry);
      
      // Refresh leaderboard
      await this.loadData(true);
      
      return entryId;
    } catch (error) {
      console.error('Failed to add leaderboard entry:', error);
      throw error;
    }
  }
  
  /**
   * Queue an entry for synchronization when online
   * @param {Object} entry - Entry to queue
   * @private
   */
  async queueEntryForSync(entry) {
    try {
      await offlineManager.queueOperation({
        endpoint: 'firestore',
        method: 'ADD',
        data: {
          collection: 'leaderboard_entries',
          docData: entry
        }
      });
      
      console.log('Queued leaderboard entry for sync:', entry);
    } catch (error) {
      console.error('Failed to queue entry for sync:', error);
    }
  }
  
  /**
   * Store an entry offline
   * @param {Object} entry - Entry to store
   * @private
   */
  async storeEntryOffline(entry) {
    try {
      // Store in offline data
      await offlineManager.storeOfflineData('leaderboard_entries', entry, `${this.boardId}_${entry.userId}`);
      
      console.log('Stored leaderboard entry offline:', entry);
    } catch (error) {
      console.error('Failed to store entry offline:', error);
    }
  }

  /**
   * Update an entry in the leaderboard
   * @param {string} entryId - ID of the entry to update
   * @param {Object} data - Data to update
   * @returns {Promise<void>}
   */
  async updateEntry(entryId, data) {
    try {
      // If updating points, ensure total is calculated
      if (data.points) {
        const points = {
          ...(data.points || {}),
        };
        
        // Calculate total if not provided
        if (!points.total) {
          points.total = Object.values(points).reduce((sum, val) => {
            return typeof val === 'number' && val !== points.total ? sum + val : sum;
          }, 0);
        }
        
        data.points = points;
      }
      
      // If offline, queue for later
      if (this.offlineMode) {
        await this.queueUpdateForSync(entryId, data);
        
        // Update locally for immediate feedback
        await this.updateEntryOffline(entryId, data);
        
        // Refresh from local data
        await this.loadData(true);
        
        return;
      }
      
      // Update in Firebase
      await firebaseService.updateDocument('leaderboard_entries', entryId, data);
      
      // Refresh leaderboard
      await this.loadData(true);
    } catch (error) {
      console.error('Failed to update leaderboard entry:', error);
      throw error;
    }
  }
  
  /**
   * Queue an update for synchronization when online
   * @param {string} entryId - ID of the entry to update
   * @param {Object} data - Data to update
   * @private
   */
  async queueUpdateForSync(entryId, data) {
    try {
      await offlineManager.queueOperation({
        endpoint: 'firestore',
        method: 'UPDATE',
        data: {
          collection: 'leaderboard_entries',
          docId: entryId,
          docData: data
        }
      });
      
      console.log('Queued leaderboard update for sync:', entryId, data);
    } catch (error) {
      console.error('Failed to queue update for sync:', error);
    }
  }
  
  /**
   * Update an entry offline
   * @param {string} entryId - ID of the entry to update
   * @param {Object} data - Data to update
   * @private
   */
  async updateEntryOffline(entryId, data) {
    try {
      // Get current entry from cache
      const cachedData = stateManager.get(`content.leaderboards.${this.boardId}`);
      
      if (cachedData && cachedData.entries) {
        const entries = [...cachedData.entries];
        const entryIndex = entries.findIndex(entry => entry.id === entryId);
        
        if (entryIndex !== -1) {
          // Update the entry
          entries[entryIndex] = {
            ...entries[entryIndex],
            ...data,
            updatedAt: new Date().toISOString()
          };
          
          // Update cache
          stateManager.set(`content.leaderboards.${this.boardId}`, {
            entries,
            timestamp: new Date().toISOString()
          });
          
          // Store for offline use
          await this.storeLeaderboardOffline();
        }
      }
    } catch (error) {
      console.error('Failed to update entry offline:', error);
    }
  }

  /**
   * Delete an entry from the leaderboard
   * @param {string} entryId - ID of the entry to delete
   * @returns {Promise<void>}
   */
  async deleteEntry(entryId) {
    try {
      // If offline, queue for later
      if (this.offlineMode) {
        await this.queueDeleteForSync(entryId);
        
        // Delete locally for immediate feedback
        await this.deleteEntryOffline(entryId);
        
        // Refresh from local data
        await this.loadData(true);
        
        return;
      }
      
      // Delete from Firebase
      await firebaseService.deleteDocument('leaderboard_entries', entryId);
      
      // Refresh leaderboard
      await this.loadData(true);
    } catch (error) {
      console.error('Failed to delete leaderboard entry:', error);
      throw error;
    }
  }
  
  /**
   * Queue a delete operation for synchronization when online
   * @param {string} entryId - ID of the entry to delete
   * @private
   */
  async queueDeleteForSync(entryId) {
    try {
      await offlineManager.queueOperation({
        endpoint: 'firestore',
        method: 'DELETE',
        data: {
          collection: 'leaderboard_entries',
          docId: entryId
        }
      });
      
      console.log('Queued leaderboard delete for sync:', entryId);
    } catch (error) {
      console.error('Failed to queue delete for sync:', error);
    }
  }
  
  /**
   * Delete an entry offline
   * @param {string} entryId - ID of the entry to delete
   * @private
   */
  async deleteEntryOffline(entryId) {
    try {
      // Get current entries from cache
      const cachedData = stateManager.get(`content.leaderboards.${this.boardId}`);
      
      if (cachedData && cachedData.entries) {
        const entries = cachedData.entries.filter(entry => entry.id !== entryId);
        
        // Update cache
        stateManager.set(`content.leaderboards.${this.boardId}`, {
          entries,
          timestamp: new Date().toISOString()
        });
        
        // Store for offline use
        await this.storeLeaderboardOffline();
      }
    } catch (error) {
      console.error('Failed to delete entry offline:', error);
    }
  }

  /**
   * Get user's rank in the leaderboard
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} - User's entry with rank, or null if not found
   */
  async getUserRank(userId) {
    try {
      // If we already have the user entry cached, return it
      if (this.userEntry && this.userEntry.userId === userId) {
        return this.userEntry;
      }
      
      // If offline, check cached data first
      if (this.offlineMode) {
        const cachedData = stateManager.get(`content.leaderboards.${this.boardId}`);
        
        if (cachedData && cachedData.entries) {
          const userEntry = cachedData.entries.find(entry => entry.userId === userId);
          
          if (userEntry) {
            // Calculate rank based on cached entries
            const sortField = this.options.sortBy.split('.');
            const sortValue = sortField.reduce((obj, key) => obj && obj[key] !== undefined ? obj[key] : null, userEntry);
            
            const higherRankedEntries = cachedData.entries.filter(entry => {
              const entryValue = sortField.reduce((obj, key) => obj && obj[key] !== undefined ? obj[key] : null, entry);
              return this.options.sortDirection === 'desc' ? entryValue > sortValue : entryValue < sortValue;
            });
            
            this.userEntry = {
              ...userEntry,
              rank: higherRankedEntries.length + 1
            };
            
            return this.userEntry;
          }
        }
        
        // Try to get from offline storage
        const offlineEntry = await offlineManager.getOfflineData('leaderboard_entries', `${this.boardId}_${userId}`);
        
        if (offlineEntry) {
          this.userEntry = {
            ...offlineEntry,
            rank: -1 // Cannot determine accurate rank offline
          };
          
          return this.userEntry;
        }
        
        return null;
      }
      
      // Load all entries for this board (no limit)
      const query = {
        where: {
          field: 'boardId',
          operator: '==',
          value: this.boardId
        },
        orderBy: {
          field: this.options.sortBy,
          direction: this.options.sortDirection
        }
      };
      
      const entries = await firebaseService.getCollection('leaderboard_entries', query);
      
      // Find user's entry and rank
      const userIndex = entries.findIndex(entry => entry.userId === userId);
      
      if (userIndex === -1) {
        this.userEntry = null;
        return null;
      }
      
      this.userEntry = {
        ...entries[userIndex],
        rank: userIndex + 1
      };
      
      return this.userEntry;
    } catch (error) {
      console.error('Failed to get user rank:', error);
      throw error;
    }
  }

  /**
   * Render the leaderboard
   */
  render() {
    if (!this.container) return;
    
    // Clear container
    this.container.innerHTML = '';
    
    // Add title if provided
    if (this.options.title) {
      const titleElement = document.createElement('h2');
      titleElement.className = 'leaderboard-title';
      titleElement.textContent = this.options.title;
      this.container.appendChild(titleElement);
    }
    
    // Show loading state
    if (this.loading) {
      const loadingElement = document.createElement('div');
      loadingElement.className = 'leaderboard-loading';
      loadingElement.textContent = 'Loading...';
      this.container.appendChild(loadingElement);
      return;
    }
    
    // Show error if any
    if (this.error) {
      const errorElement = document.createElement('div');
      errorElement.className = 'leaderboard-error';
      errorElement.textContent = this.error;
      this.container.appendChild(errorElement);
      return;
    }
    
    // Create view mode selector
    const viewModeSelector = document.createElement('div');
    viewModeSelector.className = 'leaderboard-view-selector';
    
    const personalButton = document.createElement('button');
    personalButton.className = `view-mode-button ${this.options.viewMode === VIEW_MODES.PERSONAL ? 'active' : ''}`;
    personalButton.textContent = 'My Progress';
    personalButton.addEventListener('click', () => this.setViewMode(VIEW_MODES.PERSONAL));
    
    const topButton = document.createElement('button');
    topButton.className = `view-mode-button ${this.options.viewMode === VIEW_MODES.TOP ? 'active' : ''}`;
    topButton.textContent = 'Top 10';
    topButton.addEventListener('click', () => this.setViewMode(VIEW_MODES.TOP));
    
    const allButton = document.createElement('button');
    allButton.className = `view-mode-button ${this.options.viewMode === VIEW_MODES.ALL ? 'active' : ''}`;
    allButton.textContent = 'All Participants';
    allButton.addEventListener('click', () => this.setViewMode(VIEW_MODES.ALL));
    
    viewModeSelector.appendChild(personalButton);
    viewModeSelector.appendChild(topButton);
    viewModeSelector.appendChild(allButton);
    
    this.container.appendChild(viewModeSelector);
    
    // Show personal progress if in personal mode
    if (this.options.viewMode === VIEW_MODES.PERSONAL) {
      this.renderPersonalProgress();
      return;
    }
    
    // Create leaderboard table
    const table = document.createElement('table');
    table.className = 'leaderboard-table';
    
    // Create header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    const rankHeader = document.createElement('th');
    rankHeader.textContent = 'Rank';
    headerRow.appendChild(rankHeader);
    
    const nameHeader = document.createElement('th');
    nameHeader.textContent = 'Name';
    headerRow.appendChild(nameHeader);
    
    const pointsHeader = document.createElement('th');
    pointsHeader.textContent = 'Points';
    headerRow.appendChild(pointsHeader);
    
    const boothsHeader = document.createElement('th');
    boothsHeader.textContent = 'Booths';
    headerRow.appendChild(boothsHeader);
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Create body
    const tbody = document.createElement('tbody');
    
    if (this.entries.length === 0) {
      const emptyRow = document.createElement('tr');
      const emptyCell = document.createElement('td');
      emptyCell.colSpan = 4;
      emptyCell.textContent = 'No entries yet';
      emptyCell.className = 'leaderboard-empty';
      emptyRow.appendChild(emptyCell);
      tbody.appendChild(emptyRow);
    } else {
      // Filter entries based on view mode
      let entriesToShow = [...this.entries];
      if (this.options.viewMode === VIEW_MODES.TOP) {
        entriesToShow = entriesToShow.slice(0, 10);
      }
      
      entriesToShow.forEach(entry => {
        const row = this.renderItem(entry);
        tbody.appendChild(row);
      });
    }
    
    table.appendChild(tbody);
    this.container.appendChild(table);
    
    // Add last updated timestamp
    if (this.lastUpdated) {
      const timestampElement = document.createElement('div');
      timestampElement.className = 'leaderboard-timestamp';
      
      const date = new Date(this.lastUpdated);
      timestampElement.textContent = `Last updated: ${date.toLocaleString()}`;
      
      this.container.appendChild(timestampElement);
    }
    
    // Add refresh button
    const refreshButton = document.createElement('button');
    refreshButton.className = 'leaderboard-refresh';
    refreshButton.textContent = 'Refresh';
    refreshButton.addEventListener('click', () => {
      this.loadData(true);
    });
    
    this.container.appendChild(refreshButton);
  }

  /**
   * Render a leaderboard item
   * @param {Object} entry - Leaderboard entry
   * @returns {HTMLElement} - Rendered item
   */
  renderItem(entry) {
    // Use custom render function if provided
    if (this.options.renderItem) {
      return this.options.renderItem(entry);
    }
    
    // Default rendering
    const row = document.createElement('tr');
    row.className = 'leaderboard-item';
    row.dataset.entryId = entry.id;
    
    // Highlight current user
    const currentUser = stateManager.get('user.profile');
    if (currentUser && entry.userId === currentUser.uid) {
      row.classList.add('current-user');
    }
    
    // Rank cell
    const rankCell = document.createElement('td');
    rankCell.className = 'leaderboard-rank';
    rankCell.textContent = entry.rank;
    row.appendChild(rankCell);
    
    // Name cell
    const nameCell = document.createElement('td');
    nameCell.className = 'leaderboard-name';
    
    // Handle privacy settings
    if (this.options.showInitialsOnly && entry.userId !== currentUser?.uid) {
      // Show only initials for privacy
      const nameParts = entry.displayName.split(' ');
      const initials = nameParts.map(part => part.charAt(0)).join('.');
      nameCell.textContent = `${initials}.`;
    } else {
      nameCell.textContent = entry.displayName;
    }
    
    row.appendChild(nameCell);
    
    // Points cell
    const pointsCell = document.createElement('td');
    pointsCell.className = 'leaderboard-points';
    pointsCell.textContent = entry.points?.total || 0;
    row.appendChild(pointsCell);
    
    // Booths cell
    const boothsCell = document.createElement('td');
    boothsCell.className = 'leaderboard-booths';
    
    const booths = entry.booths || [];
    boothsCell.textContent = booths.length;
    
    row.appendChild(boothsCell);
    
    return row;
  }
  
  /**
   * Render personal progress view
   * @private
   */
  renderPersonalProgress() {
    const progressContainer = document.createElement('div');
    progressContainer.className = 'personal-progress-container';
    
    if (!this.userEntry) {
      const noDataMessage = document.createElement('div');
      noDataMessage.className = 'no-progress-data';
      noDataMessage.textContent = 'No progress data available. Visit booths and complete quizzes to earn points!';
      progressContainer.appendChild(noDataMessage);
      this.container.appendChild(progressContainer);
      return;
    }
    
    // Create user info section
    const userInfo = document.createElement('div');
    userInfo.className = 'user-info';
    
    const userName = document.createElement('h3');
    userName.textContent = this.userEntry.displayName;
    userInfo.appendChild(userName);
    
    const userRank = document.createElement('div');
    userRank.className = 'user-rank';
    userRank.innerHTML = `<span>Rank:</span> ${this.userEntry.rank}`;
    userInfo.appendChild(userRank);
    
    progressContainer.appendChild(userInfo);
    
    // Create points breakdown
    const pointsBreakdown = document.createElement('div');
    pointsBreakdown.className = 'points-breakdown';
    
    const pointsTitle = document.createElement('h4');
    pointsTitle.textContent = 'Points Breakdown';
    pointsBreakdown.appendChild(pointsTitle);
    
    const points = this.userEntry.points || { participation: 0, quiz: 0, bonus: 0, total: 0 };
    
    const pointsList = document.createElement('ul');
    
    const participationItem = document.createElement('li');
    participationItem.innerHTML = `<span>Participation:</span> ${points.participation} points`;
    pointsList.appendChild(participationItem);
    
    const quizItem = document.createElement('li');
    quizItem.innerHTML = `<span>Quiz:</span> ${points.quiz} points`;
    pointsList.appendChild(quizItem);
    
    const bonusItem = document.createElement('li');
    bonusItem.innerHTML = `<span>Bonus:</span> ${points.bonus} points`;
    pointsList.appendChild(bonusItem);
    
    const totalItem = document.createElement('li');
    totalItem.className = 'total-points';
    totalItem.innerHTML = `<span>Total:</span> ${points.total} points`;
    pointsList.appendChild(totalItem);
    
    pointsBreakdown.appendChild(pointsList);
    progressContainer.appendChild(pointsBreakdown);
    
    // Create booth progress
    const boothProgress = document.createElement('div');
    boothProgress.className = 'booth-progress';
    
    const boothTitle = document.createElement('h4');
    boothTitle.textContent = 'Booth Progress';
    boothProgress.appendChild(boothTitle);
    
    const booths = this.userEntry.booths || [];
    const boothList = document.createElement('ul');
    
    if (booths.length === 0) {
      const noBoothsItem = document.createElement('li');
      noBoothsItem.className = 'no-booths';
      noBoothsItem.textContent = 'No booths visited yet';
      boothList.appendChild(noBoothsItem);
    } else {
      booths.forEach(booth => {
        const boothItem = document.createElement('li');
        boothItem.className = 'booth-item';
        boothItem.innerHTML = `
          <span class="booth-name">${booth.name}</span>
          <span class="booth-points">${booth.points} points</span>
          <span class="booth-timestamp">${new Date(booth.timestamp).toLocaleDateString()}</span>
        `;
        boothList.appendChild(boothItem);
      });
    }
    
    boothProgress.appendChild(boothList);
    progressContainer.appendChild(boothProgress);
    
    this.container.appendChild(progressContainer);
  }
  
  /**
   * Set the view mode
   * @param {string} mode - View mode
   */
  setViewMode(mode) {
    if (Object.values(VIEW_MODES).includes(mode)) {
      this.options.viewMode = mode;
      this.render();
    }
  }
  
  /**
   * Handle points earned from other components
   * @param {Object} event - Custom event with point details
   */
  handlePointsEarned(event) {
    const { userId, points, category, boothId, boothName } = event.detail;
    
    if (!userId || !points || !category) {
      console.warn('Invalid points earned event:', event.detail);
      return;
    }
    
    // Add to pending points
    this.pendingPoints.push({ userId, points, category, boothId, boothName });
    
    // Process points
    this.processPendingPoints();
  }
  
  /**
   * Process pending points
   * @private
   */
  async processPendingPoints() {
    for (const pointData of this.pendingPoints) {
      const { userId, points, category, boothId, boothName } = pointData;
      
      try {
        let userEntry = await this.getUserRank(userId);
        
        if (!userEntry) {
          // Create new entry if user is not on the leaderboard
          const userProfile = stateManager.get('user.profile');
          
          if (!userProfile) {
            console.warn('Cannot add points for anonymous user');
            continue;
          }
          
          userEntry = {
            userId,
            displayName: userProfile.displayName || 'Anonymous',
            points: { participation: 0, quiz: 0, bonus: 0, total: 0 },
            booths: []
          };
        }
        
        // Update points
        userEntry.points[category] = (userEntry.points[category] || 0) + points;
        userEntry.points.total = (userEntry.points.total || 0) + points;
        
        // Update booths
        if (boothId && !userEntry.booths.some(b => b.id === boothId)) {
          userEntry.booths.push({
            id: boothId,
            name: boothName,
            points,
            timestamp: new Date().toISOString()
          });
        }
        
        // Save entry
        if (userEntry.id) {
          await this.updateEntry(userEntry.id, {
            points: userEntry.points,
            booths: userEntry.booths
          });
        } else {
          await this.addEntry(userEntry);
        }
        
        // Call onPointsEarned callback
        if (this.options.onPointsEarned) {
          this.options.onPointsEarned({ userId, points, category });
        }
        
      } catch (error) {
        console.error('Failed to process points:', error);
      }
    }
    
    // Clear pending points
    this.pendingPoints = [];
  }
  
  /**
   * Sync pending points when coming back online
   * @private
   */
  async syncPendingPoints() {
    if (this.pendingPoints.length > 0) {
      console.log('Syncing pending points...');
      await this.processPendingPoints();
    }
  }
}

export default Leaderboard;