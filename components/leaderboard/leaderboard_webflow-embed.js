/**
 * @file leaderboard_webflow-embed.js
 * @description Webflow embed for leaderboard component
 * 
 * HOW TO USE:
 * 1. Add this code to a Webflow embed element
 * 2. Ensure you have the following elements with these specific classes:
 *    - .leaderboard-container: Container for the leaderboard
 *    - .leaderboard-title: (Optional) Title element
 *    - .leaderboard-loading: (Optional) Loading indicator
 *    - .leaderboard-error: (Optional) Error message container
 * 3. Add data attributes to the container to configure the leaderboard:
 *    - data-board-id: ID of the leaderboard (required)
 *    - data-sort-by: Field to sort by (default: "points.total")
 *    - data-sort-direction: Sort direction (default: "desc")
 *    - data-limit: Maximum number of entries to display (default: 10)
 *    - data-auto-load: Whether to load data automatically (default: "true")
 *    - data-view-mode: Initial view mode (default: "top")
 *    - data-show-initials-only: Whether to show only initials (default: "false")
 */

import Leaderboard, { VIEW_MODES } from './leaderboard.js';

/**
 * Initialize the leaderboard in Webflow
 */
function initLeaderboardWebflow() {
  try {
    console.log('Initializing leaderboard in Webflow');
    
    // Find all leaderboard containers
    const containers = document.querySelectorAll('.leaderboard-container');
    
    if (containers.length === 0) {
      console.warn('No leaderboard containers found');
      return;
    }
    
    // Initialize each leaderboard
    containers.forEach(container => {
      // Get configuration from data attributes
      const boardId = container.getAttribute('data-board-id');
      const sortBy = container.getAttribute('data-sort-by') || 'points.total';
      const sortDirection = container.getAttribute('data-sort-direction') || 'desc';
      const limit = parseInt(container.getAttribute('data-limit') || '10', 10);
      const autoLoad = container.getAttribute('data-auto-load') !== 'false';
      const title = container.getAttribute('data-title') || '';
      const viewMode = container.getAttribute('data-view-mode') || VIEW_MODES.TOP;
      const showInitialsOnly = container.getAttribute('data-show-initials-only') === 'true';
      
      // Check if board ID is provided
      if (!boardId) {
        console.error('Leaderboard container missing data-board-id attribute');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'leaderboard-error';
        errorElement.textContent = 'Leaderboard ID not specified';
        container.appendChild(errorElement);
        
        return;
      }
      
      // Create leaderboard instance
      const leaderboard = new Leaderboard({
        boardId,
        container,
        title,
        sortBy,
        sortDirection,
        limit,
        viewMode,
        showInitialsOnly,
        onUpdate: handleUpdate,
        onPointsEarned: handlePointsEarned
      });
      
      // Store leaderboard instance on container for later access
      container.leaderboard = leaderboard;
      
      // Load data if auto-load is enabled
      if (autoLoad) {
        leaderboard.loadData();
      }
      
      // Handle leaderboard update
      function handleUpdate(entries) {
        // Dispatch custom event
        const event = new CustomEvent('leaderboard-updated', { 
          detail: { boardId, entries },
          bubbles: true 
        });
        container.dispatchEvent(event);
      }
      
      // Handle points earned
      function handlePointsEarned(data) {
        // Dispatch custom event
        const event = new CustomEvent('leaderboard-points-earned', {
          detail: { boardId, ...data },
          bubbles: true
        });
        container.dispatchEvent(event);
        
        // Show visual feedback
        showPointsNotification(data.points, data.category);
      }
    });
    
    console.log('Leaderboards initialized');
  } catch (error) {
    console.error('Failed to initialize leaderboard:', error);
  }
}

/**
 * Show a notification when points are earned
 * @param {number} points - Points earned
 * @param {string} category - Point category
 */
function showPointsNotification(points, category) {
  const notification = document.createElement('div');
  notification.className = 'points-notification';
  notification.innerHTML = `
    <div class="points-notification-content">
      <span class="points-value">+${points}</span>
      <span class="points-category">${category}</span>
    </div>
  `;
  
  // Add animation classes
  notification.style.animation = 'fadeInOut 3s ease-in-out forwards';
  
  // Add to body
  document.body.appendChild(notification);
  
  // Remove after animation ends
  setTimeout(() => {
    notification.remove();
  }, 3000);
}

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initLeaderboardWebflow);

// Fallback for Webflow's preview mode where DOMContentLoaded might have already fired
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initLeaderboardWebflow, 1);
}

// Expose API for external access
window.Atlas25Leaderboard = {
  refresh: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.leaderboard-container');
      
    if (container && container.leaderboard) {
      container.leaderboard.loadData(true);
    }
  },
  
  addEntry: function(boardId, entry) {
    const container = document.querySelector(`.leaderboard-container[data-board-id="${boardId}"]`);
    
    if (container && container.leaderboard) {
      container.leaderboard.addEntry(entry);
    } else {
      console.error(`Leaderboard with board ID "${boardId}" not found`);
    }
  },
  
  updateEntry: function(boardId, entryId, data) {
    const container = document.querySelector(`.leaderboard-container[data-board-id="${boardId}"]`);
    
    if (container && container.leaderboard) {
      container.leaderboard.updateEntry(entryId, data);
    } else {
      console.error(`Leaderboard with board ID "${boardId}" not found`);
    }
  },
  
  setViewMode: function(boardId, mode) {
    const container = document.querySelector(`.leaderboard-container[data-board-id="${boardId}"]`);
    
    if (container && container.leaderboard) {
      container.leaderboard.setViewMode(mode);
    } else {
      console.error(`Leaderboard with board ID "${boardId}" not found`);
    }
  }
};