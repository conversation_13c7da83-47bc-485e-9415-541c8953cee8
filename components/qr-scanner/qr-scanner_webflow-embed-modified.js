/**
 * @file qr-scanner_webflow-embed-modified.js
 * @description Webflow embed for QR scanner component (Firebase-independent version)
 * 
 * HOW TO USE:
 * 1. Add this code to a Webflow embed element
 * 2. Include the html5-qrcode library:
 *    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
 * 3. Include the CSS styles:
 *    <link rel="stylesheet" href="path/to/qr-scanner-styles.css">
 * 4. Ensure you have the following elements with these specific classes:
 *    - .qr-scanner-container: Container for the scanner
 *    - .qr-scanner-video: Video element for camera feed
 *    - .qr-scanner-canvas: (Optional) Canvas element for processing
 *    - .qr-scanner-start-button: Button to start scanning
 *    - .qr-scanner-stop-button: Button to stop scanning
 *    - .qr-scanner-switch-camera: Button to switch cameras
 *    - .qr-scanner-toggle-flash: Button to toggle flashlight
 *    - .qr-scanner-result: Element to display scan result
 *    - .qr-scanner-error: Element to display error messages
 *    - .qr-scanner-status: Element to display scanner status
 *    - .qr-scanner-offline-indicator: Element to display offline status
 * 5. Optional data attributes for configuration:
 *    - data-auto-start="true": Start scanning automatically
 *    - data-scan-mode="single": Set scan mode (single or continuous)
 *    - data-booth-type="referee": Set booth type (referee, media, coaching)
 */

/**
 * QR code format types
 * @enum {string}
 */
const QR_CODE_TYPES = {
  SCORE: 'score',
  QUIZ: 'quiz',
  AR: 'ar',
  UNKNOWN: 'unknown'
};

/**
 * Scan modes for QR scanner
 * @enum {string}
 */
const SCAN_MODES = {
  SINGLE: 'single',
  CONTINUOUS: 'continuous'
};

/**
 * Booth types for different experiences
 * @enum {string}
 */
const BOOTH_TYPES = {
  REFEREE: 'referee',
  MEDIA: 'media',
  COACHING: 'coaching'
};

/**
 * Local storage keys
 * @enum {string}
 */
const STORAGE_KEYS = {
  SCAN_HISTORY: 'atlas25_scan_history',
  OFFLINE_QUEUE: 'atlas25_offline_queue'
};

/**
 * Initialize the QR scanner in Webflow
 */
function initQRScannerWebflow() {
  try {
    console.log('Initializing QR scanner in Webflow');
    
    // Find elements
    const container = document.querySelector('.qr-scanner-container');
    const video = document.querySelector('.qr-scanner-video');
    const canvas = document.querySelector('.qr-scanner-canvas');
    const startButton = document.querySelector('.qr-scanner-start-button');
    const stopButton = document.querySelector('.qr-scanner-stop-button');
    const switchCameraButton = document.querySelector('.qr-scanner-switch-camera');
    const toggleFlashButton = document.querySelector('.qr-scanner-toggle-flash');
    const resultElement = document.querySelector('.qr-scanner-result');
    const errorElement = document.querySelector('.qr-scanner-error');
    const statusElement = document.querySelector('.qr-scanner-status');
    const offlineIndicator = document.querySelector('.qr-scanner-offline-indicator');
    
    // Check if required elements exist
    if (!container || !video) {
      showError('Required QR scanner elements not found');
      return;
    }
    
    // Set unique ID for video element if not present
    if (!video.id) {
      video.id = `qr-scanner-video-${Date.now()}`;
    }
    
    // Get configuration from data attributes
    const autoStart = container.getAttribute('data-auto-start') === 'true';
    const scanMode = container.getAttribute('data-scan-mode') || SCAN_MODES.CONTINUOUS;
    const boothType = container.getAttribute('data-booth-type') || BOOTH_TYPES.REFEREE;
    
    // Create HTML5 QR Scanner instance
    let html5QrCode = null;
    let scanning = false;
    let currentCamera = 'environment';
    
    // Initialize QR code library
    try {
      // Check if html5-qrcode is available
      if (typeof Html5Qrcode === 'undefined') {
        showError('Html5Qrcode library not found. Make sure to include the script in your HTML.');
        return;
      }
      
      // Create scanner instance
      html5QrCode = new Html5Qrcode(
        video.id,
        { formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE] }
      );
      
      console.log('QR code library initialized');
    } catch (error) {
      console.error('Failed to initialize QR code library:', error);
      showError('Failed to initialize QR scanner');
      return;
    }
    
    // Set up event listeners
    if (startButton) {
      startButton.addEventListener('click', () => {
        startScanning();
      });
    }
    
    if (stopButton) {
      stopButton.addEventListener('click', () => {
        stopScanning();
      });
    }
    
    if (switchCameraButton) {
      switchCameraButton.addEventListener('click', () => {
        switchCamera();
      });
    }
    
    if (toggleFlashButton) {
      toggleFlashButton.addEventListener('click', () => {
        toggleFlash();
      });
    }
    
    // Set up offline indicator
    updateOfflineIndicator();
    window.addEventListener('online', () => updateOfflineIndicator());
    window.addEventListener('offline', () => updateOfflineIndicator());
    
    // Handle scan result based on booth type
    function handleScan(decodedText, decodedResult) {
      // Parse the QR code data
      const qrData = parseQRData(decodedText);
      
      console.log('QR code scanned:', qrData);
      
      if (resultElement) {
        // Format result display based on type
        let displayText = '';
        
        if (qrData.type === QR_CODE_TYPES.SCORE) {
          displayText = `Score: ${qrData.data.score || 'N/A'}`;
        } else if (qrData.type === QR_CODE_TYPES.QUIZ) {
          displayText = `Quiz: ${qrData.data.title || 'Quiz'}`;
        } else if (qrData.type === QR_CODE_TYPES.AR) {
          displayText = `AR Experience: ${qrData.data.name || 'AR Experience'}`;
        } else {
          displayText = `QR Code: ${typeof qrData.data === 'object' ? JSON.stringify(qrData.data) : qrData.data}`;
        }
        
        resultElement.textContent = displayText;
        resultElement.style.display = 'block';
        
        // Add success animation
        resultElement.classList.add('scan-success');
        setTimeout(() => {
          resultElement.classList.remove('scan-success');
        }, 2000);
      }
      
      // Process scan based on booth type
      processScanByBoothType(qrData, boothType);
      
      // Dispatch custom event
      const event = new CustomEvent('qr-code-scanned', { 
        detail: { result: qrData, boothType },
        bubbles: true 
      });
      container.dispatchEvent(event);
      
      // If in single scan mode, stop scanning after successful scan
      if (scanMode === SCAN_MODES.SINGLE) {
        stopScanning();
      }
    }
    
    // Process scan based on booth type
    function processScanByBoothType(result, boothType) {
      try {
        switch (boothType) {
          case BOOTH_TYPES.REFEREE:
            processRefereeBoothScan(result);
            break;
          
          case BOOTH_TYPES.MEDIA:
            processMediaBoothScan(result);
            break;
          
          case BOOTH_TYPES.COACHING:
            processCoachingBoothScan(result);
            break;
          
          default:
            console.warn(`Unknown booth type: ${boothType}`);
        }
      } catch (error) {
        console.error(`Error processing scan for ${boothType} booth:`, error);
        showError(`Failed to process scan: ${error.message}`);
      }
    }
    
    // Process scan for referee booth
    function processRefereeBoothScan(result) {
      if (result.type !== QR_CODE_TYPES.SCORE) {
        showError('Invalid QR code for referee booth. Expected score data.');
        return;
      }
      
      // Store score data locally
      const scoreData = {
        score: result.data.score,
        gameId: result.data.gameId,
        timestamp: new Date().toISOString(),
        boothId: result.data.boothId || 'unknown'
      };
      
      // Save score to local storage
      saveToLocalStorage('scores', scoreData);
      showStatus('Score recorded locally!', 'success');
    }
    
    // Process scan for media booth
    function processMediaBoothScan(result) {
      if (result.type !== QR_CODE_TYPES.QUIZ) {
        showError('Invalid QR code for media booth. Expected quiz data.');
        return;
      }
      
      // Launch quiz
      try {
        const quizId = result.data.quizId || result.id;
        
        if (!quizId) {
          showError('Invalid quiz QR code. Missing quiz ID.');
          return;
        }
        
        // Dispatch event to launch quiz
        const launchEvent = new CustomEvent('launch-quiz', {
          detail: { quizId, quizData: result.data },
          bubbles: true
        });
        
        document.dispatchEvent(launchEvent);
        showStatus('Launching quiz...', 'success');
      } catch (error) {
        console.error('Error launching quiz:', error);
        showError('Failed to launch quiz. Please try again.');
      }
    }
    
    // Process scan for coaching booth
    function processCoachingBoothScan(result) {
      if (result.type !== QR_CODE_TYPES.AR) {
        showError('Invalid QR code for coaching booth. Expected AR data.');
        return;
      }
      
      // Launch AR experience
      try {
        const arId = result.data.arId || result.id;
        
        if (!arId) {
          showError('Invalid AR QR code. Missing AR ID.');
          return;
        }
        
        // Dispatch event to launch AR experience
        const launchEvent = new CustomEvent('launch-ar', {
          detail: { arId, arData: result.data },
          bubbles: true
        });
        
        document.dispatchEvent(launchEvent);
        showStatus('Launching AR experience...', 'success');
      } catch (error) {
        console.error('Error launching AR experience:', error);
        showError('Failed to launch AR experience. Please try again.');
      }
    }
    
    // Parse QR code data into a structured format
    function parseQRData(qrData) {
      try {
        // Try to parse as JSON first
        if (qrData.startsWith('{') && qrData.endsWith('}')) {
          const parsed = JSON.parse(qrData);
          return {
            type: parsed.type || QR_CODE_TYPES.UNKNOWN,
            data: parsed,
            timestamp: new Date().toISOString()
          };
        }
        
        // Check for URL format with parameters
        if (qrData.includes('://')) {
          const url = new URL(qrData);
          const type = url.searchParams.get('type');
          const id = url.searchParams.get('id');
          const data = url.searchParams.get('data');
          
          return {
            type: type || QR_CODE_TYPES.UNKNOWN,
            id: id || null,
            url: qrData,
            data: data ? JSON.parse(decodeURIComponent(data)) : null,
            timestamp: new Date().toISOString()
          };
        }
        
        // Check for custom format (type:id:data)
        if (qrData.includes(':')) {
          const [type, id, ...dataParts] = qrData.split(':');
          const dataStr = dataParts.join(':');
          
          return {
            type: type || QR_CODE_TYPES.UNKNOWN,
            id: id || null,
            data: dataStr ? JSON.parse(dataStr) : null,
            timestamp: new Date().toISOString()
          };
        }
        
        // Default format
        return {
          type: QR_CODE_TYPES.UNKNOWN,
          data: qrData,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        console.error('Error parsing QR data:', error);
        
        // Return basic format if parsing fails
        return {
          type: QR_CODE_TYPES.UNKNOWN,
          data: qrData,
          rawData: qrData,
          timestamp: new Date().toISOString(),
          error: 'Parse error'
        };
      }
    }
    
    // Handle errors
    function handleError(error) {
      console.error('QR scanner error:', error);
      showError(error.message || 'Failed to access camera');
    }
    
    // Show error message
    function showError(message) {
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        
        // Hide error after 5 seconds
        setTimeout(() => {
          errorElement.style.display = 'none';
        }, 5000);
      }
    }
    
    // Show status message
    function showStatus(message, type = 'info') {
      if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `qr-scanner-status status-${type}`;
        statusElement.style.display = 'block';
        
        // Hide status after 3 seconds
        setTimeout(() => {
          statusElement.style.display = 'none';
        }, 3000);
      }
    }
    
    // Update offline indicator
    function updateOfflineIndicator() {
      if (offlineIndicator) {
        if (navigator.onLine) {
          offlineIndicator.style.display = 'none';
        } else {
          offlineIndicator.style.display = 'block';
          offlineIndicator.textContent = 'Offline Mode';
        }
      }
    }
    
    // Save data to local storage
    function saveToLocalStorage(collection, data) {
      try {
        const key = `atlas25_${collection}`;
        let items = JSON.parse(localStorage.getItem(key) || '[]');
        
        // Add unique ID and timestamp
        const newItem = {
          ...data,
          id: `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date().toISOString()
        };
        
        items.push(newItem);
        
        // Limit storage size
        if (items.length > 100) {
          items = items.slice(-100);
        }
        
        localStorage.setItem(key, JSON.stringify(items));
        return newItem.id;
      } catch (error) {
        console.error(`Error saving to local storage (${collection}):`, error);
        return null;
      }
    }
    
    // Start scanning
    function startScanning() {
      if (scanning) return;
      
      if (resultElement) {
        resultElement.textContent = '';
        resultElement.style.display = 'none';
      }
      
      if (errorElement) {
        errorElement.style.display = 'none';
      }
      
      // Configure camera
      const cameraConfig = { 
        facingMode: currentCamera
      };
      
      // Calculate QR box size
      const minDimension = Math.min(
        video.offsetWidth, 
        video.offsetHeight
      );
      const qrboxSize = Math.floor(minDimension * 0.7);
      
      // Start scanning
      html5QrCode.start(
        { facingMode: currentCamera },
        {
          fps: 10,
          qrbox: { width: qrboxSize, height: qrboxSize },
          aspectRatio: 1.0,
          disableFlip: false
        },
        handleScan,
        (error) => {
          // Only log errors that aren't just "QR code not found"
          if (error && !error.message.includes('QR code not found')) {
            handleError(error);
          }
        }
      ).then(() => {
        scanning = true;
        
        // Update UI
        if (container) container.classList.add('scanning');
        if (startButton) startButton.style.display = 'none';
        if (stopButton) stopButton.style.display = 'block';
        if (statusElement) {
          statusElement.textContent = 'Scanner active';
          statusElement.className = 'qr-scanner-status status-info';
          statusElement.style.display = 'block';
        }
        
        console.log('QR scanner started');
      }).catch(handleError);
    }
    
    // Stop scanning
    function stopScanning() {
      if (!scanning) return;
      
      html5QrCode.stop().then(() => {
        scanning = false;
        
        // Update UI
        if (container) container.classList.remove('scanning');
        if (startButton) startButton.style.display = 'block';
        if (stopButton) stopButton.style.display = 'none';
        if (statusElement) {
          statusElement.textContent = 'Scanner stopped';
          statusElement.style.display = 'block';
          
          // Hide status after 2 seconds
          setTimeout(() => {
            statusElement.style.display = 'none';
          }, 2000);
        }
        
        console.log('QR scanner stopped');
      }).catch(error => {
        console.error('Error stopping QR scanner:', error);
      });
    }
    
    // Switch camera
    function switchCamera() {
      if (statusElement) {
        statusElement.textContent = 'Switching camera...';
        statusElement.style.display = 'block';
      }
      
      // Stop current scanner
      const wasScanning = scanning;
      if (scanning) {
        html5QrCode.stop().then(() => {
          // Toggle camera
          currentCamera = currentCamera === 'environment' ? 'user' : 'environment';
          
          // Restart scanner if it was active
          if (wasScanning) {
            startScanning();
          }
          
          if (statusElement) {
            statusElement.textContent = `Using ${currentCamera === 'environment' ? 'back' : 'front'} camera`;
            
            // Hide status after 2 seconds
            setTimeout(() => {
              statusElement.style.display = 'none';
            }, 2000);
          }
        }).catch(handleError);
      } else {
        // Just toggle camera if not scanning
        currentCamera = currentCamera === 'environment' ? 'user' : 'environment';
        
        if (statusElement) {
          statusElement.textContent = `Camera switched to ${currentCamera === 'environment' ? 'back' : 'front'}`;
          
          // Hide status after 2 seconds
          setTimeout(() => {
            statusElement.style.display = 'none';
          }, 2000);
        }
      }
    }
    
    // Toggle flash
    function toggleFlash() {
      if (!scanning) {
        showStatus('Start scanning first', 'warning');
        return;
      }
      
      html5QrCode.getState().then(state => {
        // Check if torch is currently on
        const isTorchOn = state.torch;
        
        // Toggle torch
        html5QrCode.applyVideoConstraints({
          advanced: [{ torch: !isTorchOn }]
        }).then(() => {
          if (toggleFlashButton) {
            toggleFlashButton.classList.toggle('flash-on', !isTorchOn);
          }
          
          if (statusElement) {
            statusElement.textContent = !isTorchOn ? 'Flash: ON' : 'Flash: OFF';
            statusElement.style.display = 'block';
            
            // Hide status after 2 seconds
            setTimeout(() => {
              statusElement.style.display = 'none';
            }, 2000);
          }
        }).catch(error => {
          console.error('Error toggling flashlight:', error);
          
          // Only show error if it's not a "not supported" error
          if (!error.message.includes('not supported')) {
            handleError(error);
          } else {
            // Show a more user-friendly message
            if (statusElement) {
              statusElement.textContent = 'Flash not supported on this device';
              statusElement.style.display = 'block';
              
              // Hide status after 2 seconds
              setTimeout(() => {
                statusElement.style.display = 'none';
              }, 2000);
            }
          }
        });
      }).catch(error => {
        console.error('Error getting scanner state:', error);
        handleError(error);
      });
    }
    
    // Auto-start if specified
    if (autoStart) {
      startScanning();
    }
    
    // Create overlay for scan region
    const overlay = document.createElement('div');
    overlay.className = 'qr-scanner-overlay';
    container.appendChild(overlay);
    
    console.log('QR scanner initialized');
    
    // Expose API for external access
    container.qrScanner = {
      start: startScanning,
      stop: stopScanning,
      switchCamera: switchCamera,
      toggleFlash: toggleFlash
    };
  } catch (error) {
    console.error('Failed to initialize QR scanner:', error);
  }
}

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initQRScannerWebflow);

// Fallback for Webflow's preview mode where DOMContentLoaded might have already fired
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initQRScannerWebflow, 1);
}

// Expose API for external access
window.Atlas25QRScanner = {
  start: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.start();
    }
  },
  
  stop: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.stop();
    }
  },
  
  switchCamera: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.switchCamera();
    }
  },
  
  toggleFlash: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.toggleFlash();
    }
  }
};