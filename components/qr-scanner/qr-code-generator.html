<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QR Code Generator for Testing</title>
  
  <!-- Include QR Code library -->
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js"></script>
  
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
    }
    
    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    select, input, textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 5px;
      font-size: 16px;
    }
    
    button {
      padding: 10px 15px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
    }
    
    button:hover {
      background-color: #0069d9;
    }
    
    .qr-codes {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      margin-top: 30px;
    }
    
    .qr-code {
      margin: 10px;
      padding: 15px;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      text-align: center;
    }
    
    .qr-code h3 {
      margin-top: 0;
      margin-bottom: 10px;
    }
    
    .qr-code canvas {
      margin: 0 auto;
    }
    
    .qr-code-data {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
      word-break: break-all;
    }
    
    .booth-type {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: bold;
      margin-left: 5px;
    }
    
    .booth-type.referee {
      background-color: #d4edda;
      color: #155724;
    }
    
    .booth-type.media {
      background-color: #cce5ff;
      color: #004085;
    }
    
    .booth-type.coaching {
      background-color: #fff3cd;
      color: #856404;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QR Code Generator for Testing</h1>
    
    <div class="form-group">
      <label for="qr-type">QR Code Type:</label>
      <select id="qr-type">
        <option value="score">Score (Referee Booth)</option>
        <option value="quiz">Quiz (Media Booth)</option>
        <option value="ar">AR Experience (Coaching Booth)</option>
      </select>
    </div>
    
    <!-- Score Fields -->
    <div id="score-fields">
      <div class="form-group">
        <label for="score-value">Score Value:</label>
        <input type="number" id="score-value" value="100">
      </div>
      
      <div class="form-group">
        <label for="game-id">Game ID:</label>
        <input type="text" id="game-id" value="game123">
      </div>
      
      <div class="form-group">
        <label for="booth-id">Booth ID:</label>
        <input type="text" id="booth-id" value="booth1">
      </div>
    </div>
    
    <!-- Quiz Fields -->
    <div id="quiz-fields" style="display: none;">
      <div class="form-group">
        <label for="quiz-id">Quiz ID:</label>
        <input type="text" id="quiz-id" value="quiz123">
      </div>
      
      <div class="form-group">
        <label for="quiz-title">Quiz Title:</label>
        <input type="text" id="quiz-title" value="Basketball Quiz">
      </div>
    </div>
    
    <!-- AR Fields -->
    <div id="ar-fields" style="display: none;">
      <div class="form-group">
        <label for="ar-id">AR Experience ID:</label>
        <input type="text" id="ar-id" value="ar123">
      </div>
      
      <div class="form-group">
        <label for="ar-name">AR Experience Name:</label>
        <input type="text" id="ar-name" value="Shooting Technique">
      </div>
    </div>
    
    <div class="form-group">
      <label for="format-type">QR Code Format:</label>
      <select id="format-type">
        <option value="json">JSON</option>
        <option value="url">URL</option>
        <option value="custom">Custom (type:id:data)</option>
      </select>
    </div>
    
    <button id="generate-btn">Generate QR Code</button>
    
    <div class="qr-codes" id="qr-codes-container">
      <!-- QR codes will be added here -->
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const qrTypeSelect = document.getElementById('qr-type');
      const scoreFields = document.getElementById('score-fields');
      const quizFields = document.getElementById('quiz-fields');
      const arFields = document.getElementById('ar-fields');
      const generateBtn = document.getElementById('generate-btn');
      const qrCodesContainer = document.getElementById('qr-codes-container');
      const formatTypeSelect = document.getElementById('format-type');
      
      // Show/hide fields based on QR type
      qrTypeSelect.addEventListener('change', function() {
        scoreFields.style.display = 'none';
        quizFields.style.display = 'none';
        arFields.style.display = 'none';
        
        switch (this.value) {
          case 'score':
            scoreFields.style.display = 'block';
            break;
          case 'quiz':
            quizFields.style.display = 'block';
            break;
          case 'ar':
            arFields.style.display = 'block';
            break;
        }
      });
      
      // Generate QR code
      generateBtn.addEventListener('click', function() {
        const qrType = qrTypeSelect.value;
        const formatType = formatTypeSelect.value;
        let qrData = '';
        let title = '';
        let boothType = '';
        
        // Prepare data based on QR type
        switch (qrType) {
          case 'score':
            const scoreValue = document.getElementById('score-value').value;
            const gameId = document.getElementById('game-id').value;
            const boothId = document.getElementById('booth-id').value;
            
            const scoreData = {
              type: 'score',
              score: parseInt(scoreValue),
              gameId: gameId,
              boothId: boothId
            };
            
            title = `Score: ${scoreValue}`;
            boothType = 'referee';
            
            if (formatType === 'json') {
              qrData = JSON.stringify(scoreData);
            } else if (formatType === 'url') {
              qrData = `https://atlas25.nba.com/scan?type=score&id=${gameId}&data=${encodeURIComponent(JSON.stringify({ score: parseInt(scoreValue) }))}`;
            } else if (formatType === 'custom') {
              qrData = `score:${gameId}:${JSON.stringify({ score: parseInt(scoreValue), boothId: boothId })}`;
            }
            break;
            
          case 'quiz':
            const quizId = document.getElementById('quiz-id').value;
            const quizTitle = document.getElementById('quiz-title').value;
            
            const quizData = {
              type: 'quiz',
              quizId: quizId,
              title: quizTitle
            };
            
            title = `Quiz: ${quizTitle}`;
            boothType = 'media';
            
            if (formatType === 'json') {
              qrData = JSON.stringify(quizData);
            } else if (formatType === 'url') {
              qrData = `https://atlas25.nba.com/scan?type=quiz&id=${quizId}&data=${encodeURIComponent(JSON.stringify({ title: quizTitle }))}`;
            } else if (formatType === 'custom') {
              qrData = `quiz:${quizId}:${JSON.stringify({ title: quizTitle })}`;
            }
            break;
            
          case 'ar':
            const arId = document.getElementById('ar-id').value;
            const arName = document.getElementById('ar-name').value;
            
            const arData = {
              type: 'ar',
              arId: arId,
              name: arName
            };
            
            title = `AR: ${arName}`;
            boothType = 'coaching';
            
            if (formatType === 'json') {
              qrData = JSON.stringify(arData);
            } else if (formatType === 'url') {
              qrData = `https://atlas25.nba.com/scan?type=ar&id=${arId}&data=${encodeURIComponent(JSON.stringify({ name: arName }))}`;
            } else if (formatType === 'custom') {
              qrData = `ar:${arId}:${JSON.stringify({ name: arName })}`;
            }
            break;
        }
        
        // Create QR code container
        const qrCodeDiv = document.createElement('div');
        qrCodeDiv.className = 'qr-code';
        
        // Add title
        const titleEl = document.createElement('h3');
        titleEl.textContent = title;
        
        // Add booth type badge
        const boothTypeSpan = document.createElement('span');
        boothTypeSpan.className = `booth-type ${boothType}`;
        boothTypeSpan.textContent = boothType;
        titleEl.appendChild(boothTypeSpan);
        
        qrCodeDiv.appendChild(titleEl);
        
        // Add format type
        const formatEl = document.createElement('p');
        formatEl.textContent = `Format: ${formatType}`;
        qrCodeDiv.appendChild(formatEl);
        
        // Create canvas for QR code
        const canvas = document.createElement('canvas');
        qrCodeDiv.appendChild(canvas);
        
        // Add QR data
        const dataEl = document.createElement('div');
        dataEl.className = 'qr-code-data';
        dataEl.textContent = qrData;
        qrCodeDiv.appendChild(dataEl);
        
        // Add to container
        qrCodesContainer.appendChild(qrCodeDiv);
        
        // Generate QR code
        QRCode.toCanvas(canvas, qrData, { width: 200 }, function(error) {
          if (error) console.error(error);
          console.log('QR code generated!');
        });
      });
    });
  </script>
</body>
</html>