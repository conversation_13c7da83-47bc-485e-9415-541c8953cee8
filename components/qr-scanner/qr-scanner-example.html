<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QR Scanner Example</title>
  
  <!-- Include the HTML5 QR Code library -->
  <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
  
  <!-- Include the QR Scanner styles -->
  <link rel="stylesheet" href="qr-scanner-styles.css">
  
  <style>
    /* Additional page styles */
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
    }
    
    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
    }
    
    .booth-selector {
      margin-bottom: 20px;
      text-align: center;
    }
    
    .booth-selector label {
      margin-right: 10px;
      font-weight: bold;
    }
    
    .booth-selector select {
      padding: 8px;
      border-radius: 5px;
      border: 1px solid #ccc;
    }
    
    .qr-scanner-buttons {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      margin-top: 15px;
    }
    
    .scan-history {
      margin-top: 30px;
      padding: 15px;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .scan-history h2 {
      margin-top: 0;
      color: #333;
      font-size: 18px;
    }
    
    .scan-history-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .scan-history-list li {
      padding: 10px;
      border-bottom: 1px solid #eee;
    }
    
    .scan-history-list li:last-child {
      border-bottom: none;
    }
    
    .clear-history {
      margin-top: 10px;
      padding: 8px 15px;
      background-color: #dc3545;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    
    .clear-history:hover {
      background-color: #c82333;
    }
  </style>
</head>
<body>
  <h1>QR Scanner Example</h1>
  
  <!-- Booth Type Selector -->
  <div class="booth-selector">
    <label for="booth-type">Booth Type:</label>
    <select id="booth-type">
      <option value="referee">Referee Booth</option>
      <option value="media">Media Booth</option>
      <option value="coaching">Coaching Booth</option>
    </select>
  </div>
  
  <!-- QR Scanner Container -->
  <div class="qr-scanner-container" data-scan-mode="continuous">
    <!-- Video element for camera feed -->
    <div class="qr-scanner-video" id="qr-video"></div>
    
    <!-- Buttons -->
    <div class="qr-scanner-buttons">
      <button class="qr-scanner-start-button">Start Scanning</button>
      <button class="qr-scanner-stop-button">Stop Scanning</button>
      <button class="qr-scanner-switch-camera">Switch Camera</button>
      <button class="qr-scanner-toggle-flash">Toggle Flash</button>
    </div>
    
    <!-- Result display -->
    <div class="qr-scanner-result"></div>
    
    <!-- Error display -->
    <div class="qr-scanner-error"></div>
    
    <!-- Status display -->
    <div class="qr-scanner-status"></div>
    
    <!-- Offline indicator -->
    <div class="qr-scanner-offline-indicator">Offline Mode</div>
  </div>
  
  <!-- Scan History -->
  <div class="scan-history">
    <h2>Scan History</h2>
    <ul class="scan-history-list" id="scan-history-list">
      <!-- Scan history items will be added here -->
      <li>No scans yet</li>
    </ul>
    <button class="clear-history" id="clear-history">Clear History</button>
  </div>
  
  <!-- Include the QR Scanner script -->
  <script src="qr-scanner_webflow-embed-modified.js"></script>
  
  <script>
    // Additional functionality for the example page
    document.addEventListener('DOMContentLoaded', function() {
      const boothTypeSelect = document.getElementById('booth-type');
      const qrScannerContainer = document.querySelector('.qr-scanner-container');
      const scanHistoryList = document.getElementById('scan-history-list');
      const clearHistoryButton = document.getElementById('clear-history');
      
      // Update booth type when selection changes
      boothTypeSelect.addEventListener('change', function() {
        qrScannerContainer.setAttribute('data-booth-type', this.value);
        console.log(`Booth type changed to: ${this.value}`);
      });
      
      // Listen for QR code scans
      qrScannerContainer.addEventListener('qr-code-scanned', function(event) {
        const { result, boothType } = event.detail;
        console.log('QR code scanned event:', result, boothType);
        
        // Update scan history
        updateScanHistory(result, boothType);
      });
      
      // Clear scan history
      clearHistoryButton.addEventListener('click', function() {
        clearScanHistory();
      });
      
      // Update scan history display
      function updateScanHistory(result, boothType) {
        // Get existing history from local storage
        const key = 'atlas25_scan_history';
        let history = JSON.parse(localStorage.getItem(key) || '[]');
        
        // Update UI
        if (history.length > 0) {
          scanHistoryList.innerHTML = '';
          
          // Display most recent 10 scans
          const recentScans = history.slice(-10).reverse();
          
          recentScans.forEach(scan => {
            const li = document.createElement('li');
            const timestamp = new Date(scan.createdAt).toLocaleTimeString();
            
            let scanInfo = '';
            if (scan.score) {
              scanInfo = `Score: ${scan.score}`;
            } else if (scan.type === 'quiz') {
              scanInfo = `Quiz: ${scan.data.title || 'Unknown Quiz'}`;
            } else if (scan.type === 'ar') {
              scanInfo = `AR: ${scan.data.name || 'Unknown AR Experience'}`;
            } else {
              scanInfo = `QR: ${typeof scan.data === 'object' ? JSON.stringify(scan.data) : scan.data}`;
            }
            
            li.textContent = `[${timestamp}] ${scanInfo}`;
            scanHistoryList.appendChild(li);
          });
        } else {
          scanHistoryList.innerHTML = '<li>No scans yet</li>';
        }
      }
      
      // Clear scan history
      function clearScanHistory() {
        localStorage.removeItem('atlas25_scan_history');
        localStorage.removeItem('atlas25_scores');
        scanHistoryList.innerHTML = '<li>No scans yet</li>';
        console.log('Scan history cleared');
      }
      
      // Initialize scan history on page load
      updateScanHistory();
    });
    
    // Handle quiz launch events
    document.addEventListener('launch-quiz', function(event) {
      const { quizId, quizData } = event.detail;
      alert(`Quiz launched: ${quizId}\nTitle: ${quizData.title || 'Unknown'}`);
      console.log('Quiz launched:', quizData);
    });
    
    // Handle AR experience launch events
    document.addEventListener('launch-ar', function(event) {
      const { arId, arData } = event.detail;
      alert(`AR Experience launched: ${arId}\nName: ${arData.name || 'Unknown'}`);
      console.log('AR Experience launched:', arData);
    });
  </script>
</body>
</html>