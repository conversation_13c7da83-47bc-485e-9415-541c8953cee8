/**
 * @file qr-scanner.js
 * @description QR code scanner component for Atlas25 Web App
 * @module components/qr-scanner
 */

import offlineManager from '../../core/offline/offline-manager.js';
import syncQueue from '../../core/offline/sync-queue.js';

/**
 * QR code format types
 * @enum {string}
 */
export const QR_CODE_TYPES = {
  SCORE: 'score',
  QUIZ: 'quiz',
  AR: 'ar',
  UNKNOWN: 'unknown'
};

/**
 * Scan modes for QR scanner
 * @enum {string}
 */
export const SCAN_MODES = {
  SINGLE: 'single',
  CONTINUOUS: 'continuous'
};

/**
 * QRScanner class for scanning QR codes
 */
class QRScanner {
  /**
   * Create a QR scanner
   * @param {Object} options - Configuration options
   * @param {HTMLElement|string} options.videoElement - Video element or selector
   * @param {HTMLElement|string} [options.canvasElement] - Canvas element or selector
   * @param {Function} [options.onScan] - Callback function when QR code is scanned
   * @param {Function} [options.onError] - Callback function when error occurs
   * @param {Function} [options.onStart] - Callback function when scanner starts
   * @param {Function} [options.onStop] - Callback function when scanner stops
   * @param {boolean} [options.startOnInit=false] - Whether to start scanning on initialization
   * @param {string} [options.scanMode='continuous'] - Scan mode (single or continuous)
   * @param {boolean} [options.highlightScanRegion=true] - Whether to highlight the scan region
   * @param {boolean} [options.highlightCodeOutline=true] - Whether to highlight the QR code outline
   */
  constructor(options) {
    this.options = {
      startOnInit: false,
      scanMode: SCAN_MODES.CONTINUOUS,
      highlightScanRegion: true,
      highlightCodeOutline: true,
      ...options
    };
    
    this.videoElement = typeof options.videoElement === 'string' 
      ? document.querySelector(options.videoElement) 
      : options.videoElement;
      
    this.canvasElement = options.canvasElement 
      ? (typeof options.canvasElement === 'string' 
        ? document.querySelector(options.canvasElement) 
        : options.canvasElement)
      : document.createElement('canvas');
      
    this.canvasContext = this.canvasElement.getContext('2d');
    this.scanning = false;
    this.lastResult = null;
    this.stream = null;
    this.html5QrCode = null;
    this.scanHistory = [];
    this.offlineQueue = [];
    this.isOnline = navigator.onLine;
    
    // Initialize QR code library
    this.initQRCodeLibrary();
    
    // Set up network listeners
    this.setupNetworkListeners();
    
    if (this.options.startOnInit) {
      this.start();
    }
  }

  /**
   * Initialize QR code library
   * @private
   */
  initQRCodeLibrary() {
    try {
      // Check if html5-qrcode is available
      if (typeof Html5Qrcode === 'undefined') {
        console.warn('Html5Qrcode library not found. Make sure to include the script in your HTML.');
        return;
      }
      
      // Create scanner instance
      this.html5QrCode = new Html5Qrcode(
        this.videoElement.id || 'qr-scanner-video',
        { formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE] }
      );
      
      console.log('QR code library initialized');
    } catch (error) {
      console.error('Failed to initialize QR code library:', error);
      if (this.options.onError) {
        this.options.onError(error);
      }
    }
  }
  
  /**
   * Set up network listeners for online/offline events
   * @private
   */
  setupNetworkListeners() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processPendingScans();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  /**
   * Start scanning for QR codes
   * @param {Object} [config] - Configuration options for scanning
   * @param {string} [config.facingMode='environment'] - Camera facing mode ('environment' or 'user')
   * @param {number} [config.fps=10] - Frames per second for scanning
   * @returns {Promise<void>}
   */
  async start(config = {}) {
    if (this.scanning) return;
    
    const scanConfig = {
      facingMode: config.facingMode || 'environment',
      fps: config.fps || 10
    };
    
    try {
      if (!this.html5QrCode) {
        this.initQRCodeLibrary();
        if (!this.html5QrCode) {
          throw new Error('QR scanner library not initialized');
        }
      }
      
      // Configure camera
      const cameraConfig = { 
        facingMode: scanConfig.facingMode,
        fps: scanConfig.fps
      };
      
      // Start scanning
      await this.html5QrCode.start(
        { facingMode: scanConfig.facingMode },
        {
          fps: scanConfig.fps,
          qrbox: this.calculateQrboxSize(),
          aspectRatio: 1.0,
          disableFlip: false,
          videoConstraints: cameraConfig
        },
        this.handleScanSuccess.bind(this),
        this.handleScanError.bind(this)
      );
      
      this.scanning = true;
      
      // Call onStart callback if provided
      if (this.options.onStart) {
        this.options.onStart();
      }
      
      console.log('QR scanner started');
    } catch (error) {
      console.error('Failed to start QR scanner:', error);
      
      if (this.options.onError) {
        this.options.onError(this.formatError(error));
      }
      
      throw error;
    }
  }
  
  /**
   * Calculate the optimal QR box size based on video dimensions
   * @private
   * @returns {Object} - QR box dimensions
   */
  calculateQrboxSize() {
    if (!this.videoElement) return { width: 250, height: 250 };
    
    const minDimension = Math.min(
      this.videoElement.offsetWidth, 
      this.videoElement.offsetHeight
    );
    
    const qrboxSize = Math.floor(minDimension * 0.7);
    return { width: qrboxSize, height: qrboxSize };
  }
  
  /**
   * Handle successful QR code scan
   * @private
   * @param {string} decodedText - Decoded QR code text
   * @param {Object} decodedResult - Full decoded result
   */
  handleScanSuccess(decodedText, decodedResult) {
    // Parse the QR code data
    const qrData = this.parseQRData(decodedText);
    
    // Process the scan result
    this.processScanResult(qrData);
    
    // If in single scan mode, stop scanning after successful scan
    if (this.options.scanMode === SCAN_MODES.SINGLE) {
      this.stop();
    }
  }
  
  /**
   * Handle QR code scan error
   * @private
   * @param {Error} error - Scan error
   */
  handleScanError(error) {
    // Only log errors that aren't just "QR code not found"
    if (error && !error.message.includes('QR code not found')) {
      console.error('QR scan error:', error);
    }
  }

  /**
   * Stop scanning for QR codes
   */
  stop() {
    if (!this.scanning) return;
    
    try {
      if (this.html5QrCode) {
        this.html5QrCode.stop();
      }
      
      this.scanning = false;
      
      // Call onStop callback if provided
      if (this.options.onStop) {
        this.options.onStop();
      }
      
      console.log('QR scanner stopped');
    } catch (error) {
      console.error('Error stopping QR scanner:', error);
    }
  }

  /**
   * Parse QR code data into a structured format
   * @param {string} qrData - Raw QR code data
   * @returns {Object} - Parsed QR code data
   * @private
   */
  parseQRData(qrData) {
    try {
      // Try to parse as JSON first
      if (qrData.startsWith('{') && qrData.endsWith('}')) {
        return JSON.parse(qrData);
      }
      
      // Check for URL format with parameters
      if (qrData.includes('://')) {
        const url = new URL(qrData);
        const type = url.searchParams.get('type');
        const id = url.searchParams.get('id');
        const data = url.searchParams.get('data');
        
        return {
          type: type || QR_CODE_TYPES.UNKNOWN,
          id: id || null,
          url: qrData,
          data: data ? JSON.parse(decodeURIComponent(data)) : null,
          timestamp: new Date().toISOString()
        };
      }
      
      // Check for custom format (type:id:data)
      if (qrData.includes(':')) {
        const [type, id, ...dataParts] = qrData.split(':');
        const dataStr = dataParts.join(':');
        
        return {
          type: type || QR_CODE_TYPES.UNKNOWN,
          id: id || null,
          data: dataStr ? JSON.parse(dataStr) : null,
          timestamp: new Date().toISOString()
        };
      }
      
      // Default format
      return {
        type: QR_CODE_TYPES.UNKNOWN,
        data: qrData,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error parsing QR data:', error);
      
      // Return basic format if parsing fails
      return {
        type: QR_CODE_TYPES.UNKNOWN,
        data: qrData,
        rawData: qrData,
        timestamp: new Date().toISOString(),
        error: 'Parse error'
      };
    }
  }

  /**
   * Process a scan result
   * @param {Object} qrData - Parsed QR code data
   * @private
   */
  processScanResult(qrData) {
    // Avoid duplicate scans (within 2 seconds)
    const isDuplicate = this.isDuplicateScan(qrData);
    if (isDuplicate) return;
    
    // Add to scan history
    this.addToScanHistory(qrData);
    
    // Store the last result
    this.lastResult = qrData;
    console.log('QR code scanned:', qrData);
    
    // Handle offline mode
    if (!this.isOnline) {
      this.storeOfflineScan(qrData);
    }
    
    // Call onScan callback
    if (this.options.onScan) {
      this.options.onScan(qrData);
    }
    
    // Emit scan event
    const event = new CustomEvent('qr-scan', { 
      detail: qrData,
      bubbles: true
    });
    this.videoElement.dispatchEvent(event);
  }
  
  /**
   * Check if a scan is a duplicate (within last 2 seconds)
   * @param {Object} qrData - Parsed QR code data
   * @returns {boolean} - True if duplicate
   * @private
   */
  isDuplicateScan(qrData) {
    if (!qrData) return true;
    
    // Check if we have a previous scan with the same data in the last 2 seconds
    const now = Date.now();
    const recentScans = this.scanHistory.filter(scan => {
      return (now - scan.timestamp) < 2000 && 
             JSON.stringify(scan.data) === JSON.stringify(qrData);
    });
    
    return recentScans.length > 0;
  }
  
  /**
   * Add a scan to the history
   * @param {Object} qrData - Parsed QR code data
   * @private
   */
  addToScanHistory(qrData) {
    // Add timestamp for tracking
    const scan = {
      ...qrData,
      timestamp: Date.now()
    };
    
    // Add to history
    this.scanHistory.push(scan);
    
    // Limit history size
    if (this.scanHistory.length > 50) {
      this.scanHistory.shift();
    }
  }
  
  /**
   * Store a scan for offline processing
   * @param {Object} qrData - Parsed QR code data
   * @private
   */
  storeOfflineScan(qrData) {
    // Add to offline queue
    const offlineScan = {
      ...qrData,
      offlineTimestamp: Date.now()
    };
    
    this.offlineQueue.push(offlineScan);
    
    // Store in IndexedDB via sync queue
    this.queueScanForSync(offlineScan);
    
    console.log('Stored scan for offline processing:', offlineScan);
  }
  
  /**
   * Queue a scan for synchronization when online
   * @param {Object} scanData - Scan data to queue
   * @private
   */
  async queueScanForSync(scanData) {
    try {
      // Use the sync queue to store the scan
      await syncQueue.addToQueue({
        endpoint: 'qrScans',
        method: 'POST',
        data: scanData
      });
    } catch (error) {
      console.error('Failed to queue scan for sync:', error);
    }
  }
  
  /**
   * Process any pending scans when coming back online
   * @private
   */
  async processPendingScans() {
    if (this.offlineQueue.length === 0) return;
    
    console.log(`Processing ${this.offlineQueue.length} pending scans`);
    
    // Process the queue
    await offlineManager.processSyncQueue();
    
    // Clear the offline queue
    this.offlineQueue = [];
  }

  /**
   * Scan a static image for QR codes
   * @param {string|File|Blob} image - Image to scan
   * @returns {Promise<Object>} - Parsed QR code data
   */
  async scanImage(image) {
    return new Promise((resolve, reject) => {
      try {
        if (!this.html5QrCode) {
          this.initQRCodeLibrary();
          if (!this.html5QrCode) {
            throw new Error('QR scanner library not initialized');
          }
        }
        
        const imageFile = image instanceof File || image instanceof Blob 
          ? image 
          : null;
        
        if (imageFile) {
          // Scan file directly
          this.html5QrCode.scanFile(imageFile, true)
            .then(decodedText => {
              const qrData = this.parseQRData(decodedText);
              this.processScanResult(qrData);
              resolve(qrData);
            })
            .catch(error => {
              reject(new Error('No QR code found in image'));
            });
        } else if (typeof image === 'string') {
          // For URL strings, load the image first
          const img = new Image();
          
          img.onload = () => {
            // Create canvas and draw image
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);
            
            // Convert to blob and scan
            canvas.toBlob(blob => {
              this.html5QrCode.scanFile(blob, true)
                .then(decodedText => {
                  const qrData = this.parseQRData(decodedText);
                  this.processScanResult(qrData);
                  resolve(qrData);
                })
                .catch(error => {
                  reject(new Error('No QR code found in image'));
                });
            });
          };
          
          img.onerror = (error) => {
            reject(new Error('Failed to load image'));
          };
          
          img.src = image;
        } else {
          reject(new Error('Invalid image format'));
        }
      } catch (error) {
        console.error('Error scanning image:', error);
        reject(error);
      }
    });
  }

  /**
   * Toggle flashlight
   * @returns {Promise<boolean>} - True if flashlight is on
   */
  async toggleFlashlight() {
    try {
      if (!this.html5QrCode) {
        throw new Error('Scanner not initialized');
      }
      
      // Get current torch state
      const torchState = await this.html5QrCode.getTorchState();
      
      // Toggle torch
      if (torchState) {
        await this.html5QrCode.turnOffFlash();
        return false;
      } else {
        await this.html5QrCode.turnOnFlash();
        return true;
      }
    } catch (error) {
      console.error('Error toggling flashlight:', error);
      
      // If html5-qrcode method fails, try the native approach
      if (this.stream) {
        try {
          const track = this.stream.getVideoTracks()[0];
          
          if (!track) {
            throw new Error('No video track available');
          }
          
          // Check if flashlight is supported
          const capabilities = track.getCapabilities();
          if (!capabilities.torch) {
            throw new Error('Flashlight not supported');
          }
          
          // Get current flashlight state
          const settings = track.getSettings();
          const currentState = settings.torch || false;
          
          // Toggle flashlight
          const newState = !currentState;
          await track.applyConstraints({ advanced: [{ torch: newState }] });
          
          return newState;
        } catch (nativeError) {
          console.error('Native flashlight toggle failed:', nativeError);
          throw nativeError;
        }
      }
      
      throw error;
    }
  }

  /**
   * Switch camera
   * @returns {Promise<void>}
   */
  async switchCamera() {
    try {
      // Stop current scanner
      this.stop();
      
      // Get current camera facing mode
      const currentFacingMode = this.options.facingMode || 'environment';
      
      // Toggle facing mode
      this.options.facingMode = currentFacingMode === 'environment' ? 'user' : 'environment';
      
      // Restart with new facing mode
      await this.start({ facingMode: this.options.facingMode });
      
      return this.options.facingMode;
    } catch (error) {
      console.error('Error switching camera:', error);
      throw error;
    }
  }
  
  /**
   * Format error message for user display
   * @param {Error} error - Error object
   * @returns {Object} - Formatted error
   * @private
   */
  formatError(error) {
    let errorMessage = 'An unknown error occurred';
    let errorType = 'unknown';
    
    if (error) {
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Camera access denied. Please grant permission to use the camera.';
        errorType = 'permission';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No camera found on this device.';
        errorType = 'no-camera';
      } else if (error.name === 'NotReadableError') {
        errorMessage = 'Camera is already in use by another application.';
        errorType = 'in-use';
      } else if (error.message) {
        errorMessage = error.message;
      }
    }
    
    return {
      message: errorMessage,
      type: errorType,
      originalError: error
    };
  }
  
  /**
   * Get scan history
   * @returns {Array} - Scan history
   */
  getScanHistory() {
    return [...this.scanHistory];
  }
  
  /**
   * Clear scan history
   */
  clearScanHistory() {
    this.scanHistory = [];
  }
  
  /**
   * Get offline queue
   * @returns {Array} - Offline queue
   */
  getOfflineQueue() {
    return [...this.offlineQueue];
  }
  
  /**
   * Check if the scanner is currently scanning
   * @returns {boolean} - True if scanning
   */
  isScanning() {
    return this.scanning;
  }
  
  /**
   * Get the current scan mode
   * @returns {string} - Scan mode
   */
  getScanMode() {
    return this.options.scanMode;
  }
  
  /**
   * Set the scan mode
   * @param {string} mode - Scan mode (SCAN_MODES.SINGLE or SCAN_MODES.CONTINUOUS)
   */
  setScanMode(mode) {
    if (Object.values(SCAN_MODES).includes(mode)) {
      this.options.scanMode = mode;
    } else {
      console.warn(`Invalid scan mode: ${mode}. Using default.`);
    }
  }
}

export default QRScanner;