/**
 * @file qr-scanner_webflow-embed.js
 * @description Webflow embed for QR scanner component
 * 
 * HOW TO USE:
 * 1. Add this code to a Webflow embed element
 * 2. Include the html5-qrcode library:
 *    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
 * 3. Ensure you have the following elements with these specific classes:
 *    - .qr-scanner-container: Container for the scanner
 *    - .qr-scanner-video: Video element for camera feed
 *    - .qr-scanner-canvas: (Optional) Canvas element for processing
 *    - .qr-scanner-start-button: Button to start scanning
 *    - .qr-scanner-stop-button: Button to stop scanning
 *    - .qr-scanner-switch-camera: But<PERSON> to switch cameras
 *    - .qr-scanner-toggle-flash: Button to toggle flashlight
 *    - .qr-scanner-result: Element to display scan result
 *    - .qr-scanner-error: Element to display error messages
 *    - .qr-scanner-status: Element to display scanner status
 *    - .qr-scanner-offline-indicator: Element to display offline status
 * 4. Optional data attributes for configuration:
 *    - data-auto-start="true": Start scanning automatically
 *    - data-scan-mode="single": Set scan mode (single or continuous)
 *    - data-booth-type="referee": Set booth type (referee, media, coaching)
 */

import QRScanner, { QR_CODE_TYPES, SCAN_MODES } from './qr-scanner.js';
import offlineManager from '../../core/offline/offline-manager.js';
import firebaseService from '../../core/firebase/firebase-service.js';
import { getAuth } from '../../core/firebase/firebase-config.js';

/**
 * Booth types for different experiences
 * @enum {string}
 */
const BOOTH_TYPES = {
  REFEREE: 'referee',
  MEDIA: 'media',
  COACHING: 'coaching'
};

/**
 * Initialize the QR scanner in Webflow
 */
function initQRScannerWebflow() {
  try {
    console.log('Initializing QR scanner in Webflow');
    
    // Find elements
    const container = document.querySelector('.qr-scanner-container');
    const video = document.querySelector('.qr-scanner-video');
    const canvas = document.querySelector('.qr-scanner-canvas');
    const startButton = document.querySelector('.qr-scanner-start-button');
    const stopButton = document.querySelector('.qr-scanner-stop-button');
    const switchCameraButton = document.querySelector('.qr-scanner-switch-camera');
    const toggleFlashButton = document.querySelector('.qr-scanner-toggle-flash');
    const resultElement = document.querySelector('.qr-scanner-result');
    const errorElement = document.querySelector('.qr-scanner-error');
    const statusElement = document.querySelector('.qr-scanner-status');
    const offlineIndicator = document.querySelector('.qr-scanner-offline-indicator');
    
    // Check if required elements exist
    if (!container || !video) {
      showError('Required QR scanner elements not found');
      return;
    }
    
    // Set unique ID for video element if not present
    if (!video.id) {
      video.id = `qr-scanner-video-${Date.now()}`;
    }
    
    // Get configuration from data attributes
    const autoStart = container.getAttribute('data-auto-start') === 'true';
    const scanMode = container.getAttribute('data-scan-mode') || SCAN_MODES.CONTINUOUS;
    const boothType = container.getAttribute('data-booth-type') || BOOTH_TYPES.REFEREE;
    
    // Create QR scanner instance
    const scanner = new QRScanner({
      videoElement: video,
      canvasElement: canvas,
      onScan: (result) => handleScan(result, boothType),
      onError: handleError,
      onStart: handleStart,
      onStop: handleStop,
      scanMode: scanMode
    });
    
    // Set up event listeners
    if (startButton) {
      startButton.addEventListener('click', () => {
        startScanning(scanner);
      });
    }
    
    if (stopButton) {
      stopButton.addEventListener('click', () => {
        stopScanning(scanner);
      });
    }
    
    if (switchCameraButton) {
      switchCameraButton.addEventListener('click', () => {
        switchCamera(scanner);
      });
    }
    
    if (toggleFlashButton) {
      toggleFlashButton.addEventListener('click', () => {
        toggleFlash(scanner);
      });
    }
    
    // Set up offline indicator
    updateOfflineIndicator(offlineIndicator);
    window.addEventListener('online', () => updateOfflineIndicator(offlineIndicator));
    window.addEventListener('offline', () => updateOfflineIndicator(offlineIndicator));
    
    // Store scanner instance on container for later access
    container.qrScanner = scanner;
    
    // Initialize offline manager
    offlineManager.initialize().catch(error => {
      console.error('Failed to initialize offline manager:', error);
    });
    
    // Handle scan result based on booth type
    function handleScan(result, boothType) {
      console.log('QR code scanned:', result);
      
      if (resultElement) {
        // Format result display based on type
        let displayText = '';
        
        if (result.type === QR_CODE_TYPES.SCORE) {
          displayText = `Score: ${result.data.score || 'N/A'}`;
        } else if (result.type === QR_CODE_TYPES.QUIZ) {
          displayText = `Quiz: ${result.data.title || 'Quiz'}`;
        } else if (result.type === QR_CODE_TYPES.AR) {
          displayText = `AR Experience: ${result.data.name || 'AR Experience'}`;
        } else {
          displayText = `QR Code: ${typeof result.data === 'object' ? JSON.stringify(result.data) : result.data}`;
        }
        
        resultElement.textContent = displayText;
        resultElement.style.display = 'block';
        
        // Add success animation
        resultElement.classList.add('scan-success');
        setTimeout(() => {
          resultElement.classList.remove('scan-success');
        }, 2000);
      }
      
      // Process scan based on booth type
      processScanByBoothType(result, boothType);
      
      // Dispatch custom event
      const event = new CustomEvent('qr-code-scanned', { 
        detail: { result, boothType },
        bubbles: true 
      });
      container.dispatchEvent(event);
    }
    
    // Process scan based on booth type
    async function processScanByBoothType(result, boothType) {
      try {
        switch (boothType) {
          case BOOTH_TYPES.REFEREE:
            await processRefereeBoothScan(result);
            break;
          
          case BOOTH_TYPES.MEDIA:
            await processMediaBoothScan(result);
            break;
          
          case BOOTH_TYPES.COACHING:
            await processCoachingBoothScan(result);
            break;
          
          default:
            console.warn(`Unknown booth type: ${boothType}`);
        }
      } catch (error) {
        console.error(`Error processing scan for ${boothType} booth:`, error);
        showError(`Failed to process scan: ${error.message}`);
      }
    }
    
    // Process scan for referee booth
    async function processRefereeBoothScan(result) {
      if (result.type !== QR_CODE_TYPES.SCORE) {
        showError('Invalid QR code for referee booth. Expected score data.');
        return;
      }
      
      // Get current user
      const auth = getAuth();
      const userId = auth.currentUser?.uid;
      
      if (!userId) {
        showError('User not authenticated. Please log in.');
        return;
      }
      
      // Prepare score data
      const scoreData = {
        userId,
        score: result.data.score,
        gameId: result.data.gameId,
        timestamp: new Date().toISOString(),
        boothId: result.data.boothId || 'unknown'
      };
      
      // Save score to Firebase or queue if offline
      if (navigator.onLine) {
        try {
          await firebaseService.addDocument('scores', scoreData);
          showStatus('Score recorded successfully!', 'success');
        } catch (error) {
          console.error('Error saving score:', error);
          showError('Failed to save score. Please try again.');
          
          // Queue for later if Firebase error
          await offlineManager.queueOperation({
            endpoint: 'firestore',
            method: 'ADD',
            data: {
              collection: 'scores',
              docData: scoreData
            }
          });
        }
      } else {
        // Queue for later if offline
        await offlineManager.queueOperation({
          endpoint: 'firestore',
          method: 'ADD',
          data: {
            collection: 'scores',
            docData: scoreData
          }
        });
        
        showStatus('Score saved offline. Will sync when online.', 'warning');
      }
    }
    
    // Process scan for media booth
    async function processMediaBoothScan(result) {
      if (result.type !== QR_CODE_TYPES.QUIZ) {
        showError('Invalid QR code for media booth. Expected quiz data.');
        return;
      }
      
      // Launch quiz
      try {
        const quizId = result.data.quizId || result.id;
        
        if (!quizId) {
          showError('Invalid quiz QR code. Missing quiz ID.');
          return;
        }
        
        // Dispatch event to launch quiz
        const launchEvent = new CustomEvent('launch-quiz', {
          detail: { quizId, quizData: result.data },
          bubbles: true
        });
        
        document.dispatchEvent(launchEvent);
        showStatus('Launching quiz...', 'success');
      } catch (error) {
        console.error('Error launching quiz:', error);
        showError('Failed to launch quiz. Please try again.');
      }
    }
    
    // Process scan for coaching booth
    async function processCoachingBoothScan(result) {
      if (result.type !== QR_CODE_TYPES.AR) {
        showError('Invalid QR code for coaching booth. Expected AR data.');
        return;
      }
      
      // Launch AR experience
      try {
        const arId = result.data.arId || result.id;
        
        if (!arId) {
          showError('Invalid AR QR code. Missing AR ID.');
          return;
        }
        
        // Dispatch event to launch AR experience
        const launchEvent = new CustomEvent('launch-ar', {
          detail: { arId, arData: result.data },
          bubbles: true
        });
        
        document.dispatchEvent(launchEvent);
        showStatus('Launching AR experience...', 'success');
      } catch (error) {
        console.error('Error launching AR experience:', error);
        showError('Failed to launch AR experience. Please try again.');
      }
    }
    
    // Handle errors
    function handleError(error) {
      console.error('QR scanner error:', error);
      showError(error.message || 'Failed to access camera');
    }
    
    // Show error message
    function showError(message) {
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        
        // Hide error after 5 seconds
        setTimeout(() => {
          errorElement.style.display = 'none';
        }, 5000);
      }
    }
    
    // Show status message
    function showStatus(message, type = 'info') {
      if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `qr-scanner-status status-${type}`;
        statusElement.style.display = 'block';
        
        // Hide status after 3 seconds
        setTimeout(() => {
          statusElement.style.display = 'none';
        }, 3000);
      }
    }
    
    // Update offline indicator
    function updateOfflineIndicator(indicator) {
      if (indicator) {
        if (navigator.onLine) {
          indicator.style.display = 'none';
        } else {
          indicator.style.display = 'block';
          indicator.textContent = 'Offline Mode';
        }
      }
    }
    
    // Handle scanner start
    function handleStart() {
      if (container) container.classList.add('scanning');
      if (startButton) startButton.style.display = 'none';
      if (stopButton) stopButton.style.display = 'block';
      if (statusElement) {
        statusElement.textContent = 'Scanner active';
        statusElement.className = 'qr-scanner-status status-info';
        statusElement.style.display = 'block';
      }
    }
    
    // Handle scanner stop
    function handleStop() {
      if (container) container.classList.remove('scanning');
      if (startButton) startButton.style.display = 'block';
      if (stopButton) stopButton.style.display = 'none';
      if (statusElement) {
        statusElement.textContent = 'Scanner stopped';
        statusElement.style.display = 'block';
        
        // Hide status after 2 seconds
        setTimeout(() => {
          statusElement.style.display = 'none';
        }, 2000);
      }
    }
    
    // Start scanning
    function startScanning(scanner) {
      if (resultElement) {
        resultElement.textContent = '';
        resultElement.style.display = 'none';
      }
      
      if (errorElement) {
        errorElement.style.display = 'none';
      }
      
      scanner.start().catch(handleError);
    }
    
    // Stop scanning
    function stopScanning(scanner) {
      scanner.stop();
    }
    
    // Switch camera
    function switchCamera(scanner) {
      if (statusElement) {
        statusElement.textContent = 'Switching camera...';
        statusElement.style.display = 'block';
      }
      
      scanner.switchCamera()
        .then(facingMode => {
          if (statusElement) {
            statusElement.textContent = `Using ${facingMode} camera`;
            
            // Hide status after 2 seconds
            setTimeout(() => {
              statusElement.style.display = 'none';
            }, 2000);
          }
        })
        .catch(handleError);
    }
    
    // Toggle flash
    function toggleFlash(scanner) {
      scanner.toggleFlashlight()
        .then(isOn => {
          if (toggleFlashButton) {
            toggleFlashButton.classList.toggle('flash-on', isOn);
          }
          
          if (statusElement) {
            statusElement.textContent = isOn ? 'Flash: ON' : 'Flash: OFF';
            statusElement.style.display = 'block';
            
            // Hide status after 2 seconds
            setTimeout(() => {
              statusElement.style.display = 'none';
            }, 2000);
          }
        })
        .catch(error => {
          console.error('Error toggling flashlight:', error);
          
          // Only show error if it's not a "not supported" error
          if (!error.message.includes('not supported')) {
            handleError(error);
          } else {
            // Show a more user-friendly message
            if (statusElement) {
              statusElement.textContent = 'Flash not supported on this device';
              statusElement.style.display = 'block';
              
              // Hide status after 2 seconds
              setTimeout(() => {
                statusElement.style.display = 'none';
              }, 2000);
            }
          }
        });
    }
    
    // Auto-start if specified
    if (autoStart) {
      startScanning(scanner);
    }
    
    console.log('QR scanner initialized');
  } catch (error) {
    console.error('Failed to initialize QR scanner:', error);
  }
}

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initQRScannerWebflow);

// Fallback for Webflow's preview mode where DOMContentLoaded might have already fired
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initQRScannerWebflow, 1);
}

// Expose API for external access
window.Atlas25QRScanner = {
  start: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.start();
    }
  },
  
  stop: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.stop();
    }
  },
  
  switchCamera: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.switchCamera();
    }
  },
  
  toggleFlash: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.toggleFlashlight();
    }
  },
  
  setScanMode: function(mode, containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.setScanMode(mode);
    }
  },
  
  getOfflineQueue: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      return container.qrScanner.getOfflineQueue();
    }
    return [];
  },
  
  getScanHistory: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      return container.qrScanner.getScanHistory();
    }
    return [];
  }
};