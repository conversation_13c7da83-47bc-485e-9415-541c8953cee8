/* QR Scanner Container */
.qr-scanner-container {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Video Element */
.qr-scanner-video {
  width: 100%;
  height: auto;
  min-height: 300px;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
}

/* Canvas Element */
.qr-scanner-canvas {
  display: none; /* Hidden by default */
  position: absolute;
  top: 0;
  left: 0;
}

/* Button Styling */
.qr-scanner-start-button,
.qr-scanner-stop-button,
.qr-scanner-switch-camera,
.qr-scanner-toggle-flash {
  display: inline-block;
  padding: 10px 15px;
  margin: 10px 5px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.qr-scanner-start-button:hover,
.qr-scanner-stop-button:hover,
.qr-scanner-switch-camera:hover,
.qr-scanner-toggle-flash:hover {
  background-color: #0069d9;
}

.qr-scanner-stop-button {
  background-color: #dc3545;
  display: none; /* Hidden by default */
}

.qr-scanner-stop-button:hover {
  background-color: #c82333;
}

/* Button Container */
.qr-scanner-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 15px;
}

/* Result Display */
.qr-scanner-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #d4edda;
  color: #155724;
  border-radius: 5px;
  display: none; /* Hidden by default */
  word-break: break-word;
}

/* Error Display */
.qr-scanner-error {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 5px;
  display: none; /* Hidden by default */
}

/* Status Display */
.qr-scanner-status {
  margin-top: 10px;
  padding: 10px;
  border-radius: 5px;
  text-align: center;
  display: none; /* Hidden by default */
}

.status-info {
  background-color: #cce5ff;
  color: #004085;
}

.status-success {
  background-color: #d4edda;
  color: #155724;
}

.status-warning {
  background-color: #fff3cd;
  color: #856404;
}

.status-error {
  background-color: #f8d7da;
  color: #721c24;
}

/* Offline Indicator */
.qr-scanner-offline-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 5px;
  font-size: 12px;
  display: none; /* Hidden by default */
}

/* Animation for successful scan */
@keyframes scanSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.scan-success {
  animation: scanSuccess 0.5s ease;
}

/* Scanning active state */
.qr-scanner-container.scanning .qr-scanner-video {
  border: 2px solid #28a745;
}

/* Scan region overlay */
.qr-scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.qr-scanner-overlay::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  height: 70%;
  transform: translate(-50%, -50%);
  border: 2px solid #28a745;
  border-radius: 10px;
  box-shadow: 0 0 0 100vmax rgba(0, 0, 0, 0.3);
  clip-path: inset(0 -100vmax -100vmax 0);
}

/* Flash on state */
.qr-scanner-toggle-flash.flash-on {
  background-color: #ffc107;
  color: #212529;
}

/* Responsive Design */
@media (max-width: 768px) {
  .qr-scanner-container {
    padding: 15px;
  }
  
  .qr-scanner-video {
    min-height: 250px;
  }
  
  .qr-scanner-buttons {
    flex-direction: column;
  }
  
  .qr-scanner-start-button,
  .qr-scanner-stop-button,
  .qr-scanner-switch-camera,
  .qr-scanner-toggle-flash {
    width: 100%;
    margin: 5px 0;
  }
}