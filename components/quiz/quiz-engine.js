/**
 * @file quiz-engine.js
 * @description Quiz engine component for Atlas25 Web App
 * @module components/quiz
 */

import firebaseService from '../../core/firebase/firebase-service.js';
import stateManager from '../../core/state/state-manager.js';
import offlineManager from '../../core/offline/offline-manager.js';

/**
 * QuizEngine class for managing quizzes
 */
class QuizEngine {
  /**
   * Create a quiz engine
   * @param {Object} options - Configuration options
   * @param {string} options.quizId - ID of the quiz
   * @param {string} [options.boothType] - Type of booth ('media' or 'coaching')
   * @param {HTMLElement|string} [options.container] - Container element or selector
   * @param {boolean} [options.shuffleQuestions=false] - Whether to shuffle questions
   * @param {boolean} [options.shuffleAnswers=true] - Whether to shuffle answers
   * @param {boolean} [options.showFeedback=true] - Whether to show feedback after each question
   * @param {boolean} [options.showResults=true] - Whether to show results at the end
   * @param {boolean} [options.autoSubmitToLeaderboard=true] - Whether to automatically submit score to leaderboard
   * @param {Function} [options.onComplete] - Callback function when quiz is completed
   * @param {Function} [options.onProgress] - Callback function when progress changes
   */
  constructor(options) {
    this.options = {
      shuffleQuestions: false,
      shuffleAnswers: true,
      showFeedback: true,
      showResults: true,
      autoSubmitToLeaderboard: true,
      boothType: 'media', // Default to media booth
      ...options
    };
    
    this.quizId = options.quizId;
    this.container = typeof options.container === 'string' 
      ? document.querySelector(options.container) 
      : options.container;
      
    this.quiz = null;
    this.questions = [];
    this.currentQuestionIndex = 0;
    this.answers = [];
    this.score = 0;
    this.maxScore = 0;
    this.loading = false;
    this.error = null;
    this.completed = false;
    this.startTime = null;
    this.endTime = null;
    this.offlineMode = !navigator.onLine;
    
    // Bind methods
    this.renderQuestion = this.renderQuestion.bind(this);
    this.handleAnswer = this.handleAnswer.bind(this);
    this.nextQuestion = this.nextQuestion.bind(this);
    
    // Set up online/offline listeners
    this.setupNetworkListeners();
  }

  /**
   * Set up network status listeners
   * @private
   */
  setupNetworkListeners() {
    window.addEventListener('online', () => {
      this.offlineMode = false;
      console.log('Quiz engine is now online');
      
      // Sync any stored quiz results
      this.syncOfflineResults();
    });
    
    window.addEventListener('offline', () => {
      this.offlineMode = true;
      console.log('Quiz engine is now offline');
    });
  }

  /**
   * Load quiz data
   * @param {boolean} [forceRefresh=false] - Whether to force refresh from server
   * @returns {Promise<Object>} - Quiz data
   */
  async loadQuiz(forceRefresh = false) {
    try {
      this.loading = true;
      this.error = null;
      
      if (this.container) {
        this.container.classList.add('loading');
      }
      
      // Check if we have cached data in state
      const cachedQuiz = stateManager.get(`content.quizzes.${this.quizId}`);
      
      if (!forceRefresh && cachedQuiz) {
        this.quiz = cachedQuiz;
      } else {
        // Try to load quiz from Firebase
        try {
          this.quiz = await firebaseService.getDocument('quizzes', this.quizId);
          
          if (!this.quiz) {
            throw new Error(`Quiz not found: ${this.quizId}`);
          }
          
          // Cache in state
          stateManager.set(`content.quizzes.${this.quizId}`, this.quiz);
        } catch (error) {
          // If offline, try to load from local storage
          if (!navigator.onLine) {
            const localQuiz = await this.getOfflineQuiz(this.quizId);
            if (localQuiz) {
              this.quiz = localQuiz;
              console.log('Loaded quiz from offline storage:', this.quizId);
            } else {
              throw new Error('Cannot load quiz while offline');
            }
          } else {
            throw error;
          }
        }
      }
      
      // Load questions
      if (this.quiz.questions) {
        // Questions are embedded in the quiz document
        this.questions = this.quiz.questions;
      } else if (this.quiz.questionIds) {
        // Questions are referenced by ID
        try {
          const questionPromises = this.quiz.questionIds.map(id => 
            firebaseService.getDocument('quiz_questions', id)
          );
          
          this.questions = await Promise.all(questionPromises);
        } catch (error) {
          // If offline, try to load from local storage
          if (!navigator.onLine) {
            const localQuestions = await this.getOfflineQuestions(this.quiz.questionIds);
            if (localQuestions && localQuestions.length > 0) {
              this.questions = localQuestions;
              console.log('Loaded questions from offline storage');
            } else {
              throw new Error('Cannot load quiz questions while offline');
            }
          } else {
            throw error;
          }
        }
      } else {
        // Load questions by quiz ID
        const query = {
          where: {
            field: 'quizId',
            operator: '==',
            value: this.quizId
          },
          orderBy: {
            field: 'order',
            direction: 'asc'
          }
        };
        
        try {
          this.questions = await firebaseService.getCollection('quiz_questions', query);
        } catch (error) {
          // If offline, try to load from local storage
          if (!navigator.onLine) {
            const localQuestions = await this.getOfflineQuestionsByQuizId(this.quizId);
            if (localQuestions && localQuestions.length > 0) {
              this.questions = localQuestions;
              console.log('Loaded questions from offline storage by quiz ID');
            } else {
              throw new Error('Cannot load quiz questions while offline');
            }
          } else {
            throw error;
          }
        }
      }
      
      // Store quiz and questions for offline use
      this.storeQuizOffline();
      
      // Shuffle questions if enabled
      if (this.options.shuffleQuestions) {
        this.questions = this.shuffleArray([...this.questions]);
      }
      
      // Initialize answers array
      this.answers = new Array(this.questions.length).fill(null);
      
      // Calculate max score
      this.maxScore = this.questions.reduce((total, question) => {
        return total + (question.points || 1);
      }, 0);
      
      return this.quiz;
    } catch (error) {
      console.error('Failed to load quiz:', error);
      this.error = error.message || 'Failed to load quiz';
      throw error;
    } finally {
      this.loading = false;
      
      if (this.container) {
        this.container.classList.remove('loading');
      }
    }
  }

  /**
   * Store quiz and questions for offline use
   * @private
   */
  async storeQuizOffline() {
    try {
      // Store quiz
      await offlineManager.storeOfflineData('quizzes', this.quiz, this.quizId);
      
      // Store questions
      for (const question of this.questions) {
        await offlineManager.storeOfflineData('quiz_questions', question, question.id);
      }
      
      // Store questions by quiz ID mapping
      await offlineManager.storeOfflineData('quiz_questions_map', {
        quizId: this.quizId,
        questionIds: this.questions.map(q => q.id)
      }, this.quizId);
      
      console.log('Stored quiz and questions offline:', this.quizId);
    } catch (error) {
      console.error('Failed to store quiz offline:', error);
    }
  }

  /**
   * Get quiz from offline storage
   * @param {string} quizId - Quiz ID
   * @returns {Promise<Object|null>} - Quiz data or null if not found
   * @private
   */
  async getOfflineQuiz(quizId) {
    try {
      return await offlineManager.getOfflineData('quizzes', quizId);
    } catch (error) {
      console.error('Failed to get offline quiz:', error);
      return null;
    }
  }

  /**
   * Get questions from offline storage by IDs
   * @param {Array<string>} questionIds - Question IDs
   * @returns {Promise<Array|null>} - Questions or null if not found
   * @private
   */
  async getOfflineQuestions(questionIds) {
    try {
      const questions = [];
      for (const id of questionIds) {
        const question = await offlineManager.getOfflineData('quiz_questions', id);
        if (question) {
          questions.push(question);
        }
      }
      return questions.length > 0 ? questions : null;
    } catch (error) {
      console.error('Failed to get offline questions:', error);
      return null;
    }
  }

  /**
   * Get questions from offline storage by quiz ID
   * @param {string} quizId - Quiz ID
   * @returns {Promise<Array|null>} - Questions or null if not found
   * @private
   */
  async getOfflineQuestionsByQuizId(quizId) {
    try {
      const mapping = await offlineManager.getOfflineData('quiz_questions_map', quizId);
      if (mapping && mapping.questionIds) {
        return this.getOfflineQuestions(mapping.questionIds);
      }
      return null;
    } catch (error) {
      console.error('Failed to get offline questions by quiz ID:', error);
      return null;
    }
  }

  /**
   * Start the quiz
   * @returns {Promise<void>}
   */
  async start() {
    if (!this.quiz) {
      await this.loadQuiz();
    }
    
    this.currentQuestionIndex = 0;
    this.answers = new Array(this.questions.length).fill(null);
    this.score = 0;
    this.completed = false;
    this.startTime = new Date();
    this.endTime = null;
    
    // Render first question
    this.renderQuestion();
    
    console.log('Quiz started:', this.quiz.title);
  }

  /**
   * Render the current question
   */
  renderQuestion() {
    if (!this.container) return;
    
    const question = this.questions[this.currentQuestionIndex];
    
    if (!question) {
      console.error('Question not found:', this.currentQuestionIndex);
      return;
    }
    
    // Clear container
    this.container.innerHTML = '';
    
    // Create question container
    const questionContainer = document.createElement('div');
    questionContainer.className = 'quiz-question';
    questionContainer.dataset.questionId = question.id;
    questionContainer.dataset.questionIndex = this.currentQuestionIndex;
    questionContainer.dataset.questionType = question.type || 'single';
    
    // Add progress indicator
    const progressContainer = document.createElement('div');
    progressContainer.className = 'quiz-progress';
    progressContainer.innerHTML = `Question ${this.currentQuestionIndex + 1} of ${this.questions.length}`;
    questionContainer.appendChild(progressContainer);
    
    // Add question text
    const questionText = document.createElement('h3');
    questionText.className = 'quiz-question-text';
    questionText.innerHTML = question.text;
    questionContainer.appendChild(questionText);
    
    // Add question image if available
    if (question.imageUrl) {
      const questionImage = document.createElement('img');
      questionImage.className = 'quiz-question-image';
      questionImage.src = question.imageUrl;
      questionImage.alt = question.text;
      questionContainer.appendChild(questionImage);
    }
    
    // Add answers
    const answersContainer = document.createElement('div');
    answersContainer.className = 'quiz-answers';
    
    let answers = question.answers || [];
    
    // Shuffle answers if enabled
    if (this.options.shuffleAnswers) {
      answers = this.shuffleArray([...answers]);
    }
    
    answers.forEach((answer, index) => {
      const answerItem = document.createElement('div');
      answerItem.className = 'quiz-answer';
      answerItem.dataset.answerIndex = index;
      
      const answerInput = document.createElement('input');
      answerInput.type = question.type === 'multiple' ? 'checkbox' : 'radio';
      answerInput.name = `quiz-question-${this.currentQuestionIndex}`;
      answerInput.id = `quiz-answer-${this.currentQuestionIndex}-${index}`;
      answerInput.value = index;
      
      const answerLabel = document.createElement('label');
      answerLabel.htmlFor = answerInput.id;
      answerLabel.innerHTML = answer.text;
      
      // Add image if available
      if (answer.imageUrl) {
        const answerImage = document.createElement('img');
        answerImage.className = 'quiz-answer-image';
        answerImage.src = answer.imageUrl;
        answerImage.alt = answer.text;
        answerLabel.appendChild(answerImage);
      }
      
      answerItem.appendChild(answerInput);
      answerItem.appendChild(answerLabel);
      answersContainer.appendChild(answerItem);
      
      // Add click event
      answerItem.addEventListener('click', () => {
        this.handleAnswer(index);
      });
    });
    
    questionContainer.appendChild(answersContainer);
    
    // Add submit button
    const submitButton = document.createElement('button');
    submitButton.className = 'quiz-submit-button';
    submitButton.textContent = 'Submit';
    submitButton.addEventListener('click', () => {
      this.submitAnswer();
    });
    
    questionContainer.appendChild(submitButton);
    
    this.container.appendChild(questionContainer);
    
    // Update progress
    if (this.options.onProgress) {
      this.options.onProgress({
        currentQuestion: this.currentQuestionIndex + 1,
        totalQuestions: this.questions.length,
        progress: (this.currentQuestionIndex + 1) / this.questions.length
      });
    }
  }

  /**
   * Handle answer selection
   * @param {number} answerIndex - Index of the selected answer
   */
  handleAnswer(answerIndex) {
    const question = this.questions[this.currentQuestionIndex];
    
    if (question.type === 'multiple') {
      // For multiple choice, toggle the answer
      if (!this.answers[this.currentQuestionIndex]) {
        this.answers[this.currentQuestionIndex] = [];
      }
      
      const index = this.answers[this.currentQuestionIndex].indexOf(answerIndex);
      
      if (index === -1) {
        this.answers[this.currentQuestionIndex].push(answerIndex);
      } else {
        this.answers[this.currentQuestionIndex].splice(index, 1);
      }
    } else {
      // For single choice, set the answer
      this.answers[this.currentQuestionIndex] = answerIndex;
    }
  }

  /**
   * Submit the current answer
   */
  submitAnswer() {
    const question = this.questions[this.currentQuestionIndex];
    const answer = this.answers[this.currentQuestionIndex];
    
    if (answer === null || (Array.isArray(answer) && answer.length === 0)) {
      alert('Please select an answer');
      return;
    }
    
    // Calculate score for this question
    const points = this.calculateQuestionScore(question, answer);
    this.score += points;
    
    // Show feedback if enabled
    if (this.options.showFeedback) {
      this.showFeedback(question, answer, points);
    } else {
      this.nextQuestion();
    }
  }

  /**
   * Show feedback for the current question
   * @param {Object} question - Question object
   * @param {number|Array} answer - Selected answer index or indices
   * @param {number} points - Points earned for this question
   */
  showFeedback(question, answer, points) {
    if (!this.container) return;
    
    // Clear container
    this.container.innerHTML = '';
    
    // Create feedback container
    const feedbackContainer = document.createElement('div');
    feedbackContainer.className = 'quiz-feedback';
    
    // Add result
    const resultElement = document.createElement('div');
    resultElement.className = `quiz-result ${points > 0 ? 'correct' : 'incorrect'}`;
    resultElement.textContent = points > 0 ? 'Correct!' : 'Incorrect';
    feedbackContainer.appendChild(resultElement);
    
    // Add question text
    const questionText = document.createElement('h3');
    questionText.className = 'quiz-question-text';
    questionText.innerHTML = question.text;
    feedbackContainer.appendChild(questionText);
    
    // Add explanation if available
    if (question.explanation) {
      const explanation = document.createElement('div');
      explanation.className = 'quiz-explanation';
      explanation.innerHTML = question.explanation;
      feedbackContainer.appendChild(explanation);
    }
    
    // Add correct answer information
    const correctAnswerInfo = document.createElement('div');
    correctAnswerInfo.className = 'quiz-correct-answer';
    
    if (question.type === 'multiple') {
      const correctAnswers = question.answers
        .filter(a => a.correct)
        .map(a => a.text)
        .join(', ');
      
      correctAnswerInfo.innerHTML = `<strong>Correct answers:</strong> ${correctAnswers}`;
    } else {
      const correctAnswer = question.answers.find(a => a.correct);
      if (correctAnswer) {
        correctAnswerInfo.innerHTML = `<strong>Correct answer:</strong> ${correctAnswer.text}`;
      }
    }
    
    feedbackContainer.appendChild(correctAnswerInfo);
    
    // Add next button
    const nextButton = document.createElement('button');
    nextButton.className = 'quiz-next-button';
    nextButton.textContent = this.currentQuestionIndex < this.questions.length - 1 ? 'Next Question' : 'Finish Quiz';
    nextButton.addEventListener('click', () => {
      this.nextQuestion();
    });
    
    feedbackContainer.appendChild(nextButton);
    
    this.container.appendChild(feedbackContainer);
  }

  /**
   * Move to the next question or finish the quiz
   */
  nextQuestion() {
    this.currentQuestionIndex++;
    
    if (this.currentQuestionIndex >= this.questions.length) {
      this.finish();
    } else {
      this.renderQuestion();
    }
  }

  /**
   * Finish the quiz
   */
  finish() {
    this.completed = true;
    this.endTime = new Date();
    
    // Calculate final score
    const finalScore = {
      score: this.score,
      maxScore: this.maxScore,
      percentage: Math.round((this.score / this.maxScore) * 100),
      timeSpent: (this.endTime - this.startTime) / 1000, // in seconds
      answers: this.answers,
      boothType: this.options.boothType
    };
    
    // Save results
    this.saveResults(finalScore);
    
    // Show results if enabled
    if (this.options.showResults) {
      this.showResults(finalScore);
    }
    
    // Call onComplete callback
    if (this.options.onComplete) {
      this.options.onComplete(finalScore);
    }
    
    console.log('Quiz completed:', finalScore);
  }

  /**
   * Show quiz results
   * @param {Object} results - Quiz results
   */
  showResults(results) {
    if (!this.container) return;
    
    // Clear container
    this.container.innerHTML = '';
    
    // Create results container
    const resultsContainer = document.createElement('div');
    resultsContainer.className = 'quiz-results';
    
    // Add title
    const title = document.createElement('h2');
    title.className = 'quiz-results-title';
    title.textContent = 'Quiz Results';
    resultsContainer.appendChild(title);
    
    // Add score
    const scoreElement = document.createElement('div');
    scoreElement.className = 'quiz-results-score';
    scoreElement.innerHTML = `
      <div class="quiz-results-percentage">${results.percentage}%</div>
      <div class="quiz-results-points">${results.score} / ${results.maxScore} points</div>
    `;
    resultsContainer.appendChild(scoreElement);
    
    // Add time spent
    const timeElement = document.createElement('div');
    timeElement.className = 'quiz-results-time';
    
    const minutes = Math.floor(results.timeSpent / 60);
    const seconds = Math.floor(results.timeSpent % 60);
    timeElement.textContent = `Time: ${minutes}m ${seconds}s`;
    
    resultsContainer.appendChild(timeElement);
    
    // Add feedback based on score
    const feedbackElement = document.createElement('div');
    feedbackElement.className = 'quiz-results-feedback';
    
    if (results.percentage >= 80) {
      feedbackElement.textContent = 'Great job! You really know your stuff!';
    } else if (results.percentage >= 60) {
      feedbackElement.textContent = 'Good work! You\'re on the right track.';
    } else {
      feedbackElement.textContent = 'Keep practicing! You\'ll get better with time.';
    }
    
    resultsContainer.appendChild(feedbackElement);
    
    // Add leaderboard info if points were added
    if (this.options.autoSubmitToLeaderboard) {
      const leaderboardInfo = document.createElement('div');
      leaderboardInfo.className = 'quiz-results-leaderboard';
      leaderboardInfo.textContent = 'Your score has been added to the leaderboard!';
      resultsContainer.appendChild(leaderboardInfo);
    }
    
    // Add restart button
    const restartButton = document.createElement('button');
    restartButton.className = 'quiz-restart-button';
    restartButton.textContent = 'Restart Quiz';
    restartButton.addEventListener('click', () => {
      this.start();
    });
    
    resultsContainer.appendChild(restartButton);
    
    this.container.appendChild(resultsContainer);
  }

  /**
   * Save quiz results
   * @param {Object} results - Quiz results
   * @returns {Promise<string|null>} - ID of the saved results or null if not saved
   */
  async saveResults(results) {
    try {
      const user = stateManager.get('user.profile');
      
      if (!user) {
        console.warn('User not authenticated, results not saved');
        return null;
      }
      
      const resultData = {
        quizId: this.quizId,
        userId: user.uid,
        displayName: user.displayName,
        score: results.score,
        maxScore: results.maxScore,
        percentage: results.percentage,
        timeSpent: results.timeSpent,
        answers: results.answers,
        boothType: this.options.boothType,
        timestamp: new Date().toISOString()
      };
      
      let resultId = null;
      
      // Save to Firebase if online
      if (navigator.onLine) {
        resultId = await firebaseService.addDocument('quiz_results', resultData);
        
        // Update leaderboard if enabled
        if (this.options.autoSubmitToLeaderboard) {
          await this.updateLeaderboard(resultData);
        }
      } else {
        // Store locally for later sync
        resultId = `local_${Date.now()}`;
        await offlineManager.storeOfflineData('pending_quiz_results', {
          ...resultData,
          localId: resultId
        }, resultId);
        
        console.log('Quiz results stored locally for later sync');
      }
      
      // Update user progress
      const userProgress = stateManager.get('user.progress') || {};
      userProgress.quizzes = userProgress.quizzes || {};
      userProgress.quizzes[this.quizId] = {
        completed: true,
        score: results.score,
        maxScore: results.maxScore,
        percentage: results.percentage,
        timestamp: new Date().toISOString(),
        resultId: resultId
      };
      
      stateManager.set('user.progress', userProgress);
      
      return resultId;
    } catch (error) {
      console.error('Error saving quiz results:', error);
      
      // Store locally if there was an error
      try {
        const resultId = `local_${Date.now()}`;
        await offlineManager.storeOfflineData('pending_quiz_results', {
          quizId: this.quizId,
          score: results.score,
          maxScore: results.maxScore,
          percentage: results.percentage,
          timeSpent: results.timeSpent,
          answers: results.answers,
          boothType: this.options.boothType,
          timestamp: new Date().toISOString(),
          localId: resultId
        }, resultId);
        
        console.log('Quiz results stored locally due to error');
        return resultId;
      } catch (storageError) {
        console.error('Failed to store quiz results locally:', storageError);
        return null;
      }
    }
  }

  /**
   * Update leaderboard with quiz results
   * @param {Object} resultData - Quiz result data
   * @returns {Promise<void>}
   * @private
   */
  async updateLeaderboard(resultData) {
    try {
      // Determine leaderboard ID based on booth type
      const leaderboardId = this.options.boothType === 'media' 
        ? 'media_booth_leaderboard' 
        : 'coaching_booth_leaderboard';
      
      // Check if user already has an entry
      const query = {
        where: {
          field: 'userId',
          operator: '==',
          value: resultData.userId
        }
      };
      
      const entries = await firebaseService.getCollection('leaderboard_entries', query);
      const existingEntry = entries.find(entry => entry.boardId === leaderboardId);
      
      if (existingEntry) {
        // Update existing entry
        const updatedScore = existingEntry.score + resultData.score;
        await firebaseService.updateDocument('leaderboard_entries', existingEntry.id, {
          score: updatedScore,
          updatedAt: new Date().toISOString()
        });
        
        console.log('Updated leaderboard entry:', existingEntry.id);
      } else {
        // Create new entry
        const entryData = {
          boardId: leaderboardId,
          userId: resultData.userId,
          displayName: resultData.displayName,
          score: resultData.score,
          metadata: {
            quizCount: 1,
            lastQuizId: resultData.quizId,
            boothType: this.options.boothType
          }
        };
        
        const entryId = await firebaseService.addDocument('leaderboard_entries', entryData);
        console.log('Created new leaderboard entry:', entryId);
      }
    } catch (error) {
      console.error('Failed to update leaderboard:', error);
      
      // Queue for later if there was an error
      if (navigator.onLine) {
        await offlineManager.queueOperation('updateLeaderboard', {
          resultData,
          boothType: this.options.boothType
        });
      }
    }
  }

  /**
   * Sync offline quiz results
   * @returns {Promise<void>}
   */
  async syncOfflineResults() {
    try {
      // Get all pending quiz results
      const pendingResults = await offlineManager.getAllOfflineData('pending_quiz_results');
      
      if (!pendingResults || pendingResults.length === 0) {
        return;
      }
      
      console.log(`Syncing ${pendingResults.length} offline quiz results`);
      
      for (const result of pendingResults) {
        try {
          // Save to Firebase
          const resultId = await firebaseService.addDocument('quiz_results', {
            quizId: result.quizId,
            userId: result.userId,
            displayName: result.displayName,
            score: result.score,
            maxScore: result.maxScore,
            percentage: result.percentage,
            timeSpent: result.timeSpent,
            answers: result.answers,
            boothType: result.boothType,
            timestamp: result.timestamp,
            syncedAt: new Date().toISOString()
          });
          
          // Update leaderboard
          if (this.options.autoSubmitToLeaderboard) {
            await this.updateLeaderboard(result);
          }
          
          // Remove from pending
          await offlineManager.deleteOfflineData('pending_quiz_results', result.localId);
          
          console.log('Synced offline quiz result:', resultId);
        } catch (error) {
          console.error('Failed to sync quiz result:', error);
        }
      }
    } catch (error) {
      console.error('Failed to sync offline quiz results:', error);
    }
  }

  /**
   * Calculate score for a question
   * @param {Object} question - Question object
   * @param {number|Array} answer - Selected answer index or indices
   * @returns {number} - Points earned
   */
  calculateQuestionScore(question, answer) {
    if (question.type === 'multiple') {
      // For multiple choice, all correct answers must be selected
      const correctIndices = question.answers
        .map((a, index) => a.correct ? index : -1)
        .filter(index => index !== -1);
      
      const isCorrect = 
        correctIndices.length === answer.length &&
        correctIndices.every(i => answer.includes(i)) &&
        answer.every(i => correctIndices.includes(i));
      
      return isCorrect ? (question.points || 1) : 0;
    } else {
      // For single choice, check if the correct answer is selected
      const correctIndex = question.answers.findIndex(a => a.correct);
      return answer === correctIndex ? (question.points || 1) : 0;
    }
  }

  /**
   * Shuffle an array using Fisher-Yates algorithm
   * @param {Array} array - Array to shuffle
   * @returns {Array} - Shuffled array
   */
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      const temp = array[i];
      array[i] = array[j];
      array[j] = temp;
    }
    return array;
  }
}

export default QuizEngine;