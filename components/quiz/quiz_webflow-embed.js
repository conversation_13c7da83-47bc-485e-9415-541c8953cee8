/**
 * @file quiz_webflow-embed.js
 * @description Webflow embed for quiz component for Atlas25 Web App
 * 
 * HOW TO USE:
 * 1. Add this code to a Webflow embed element
 * 2. Ensure you have the following elements with these specific classes:
 *    - .quiz-container: Container for the quiz
 *    - .quiz-title: (Optional) Title element
 *    - .quiz-description: (Optional) Description element
 *    - .quiz-start-button: Button to start the quiz
 * 3. Add data attributes to the container to configure the quiz:
 *    - data-quiz-id: ID of the quiz (required)
 *    - data-booth-type: Type of booth ('media' or 'coaching') (default: "media")
 *    - data-shuffle-questions: Whether to shuffle questions (default: "false")
 *    - data-shuffle-answers: Whether to shuffle answers (default: "true")
 *    - data-show-feedback: Whether to show feedback after each question (default: "true")
 *    - data-show-results: Whether to show results at the end (default: "true")
 *    - data-auto-submit-to-leaderboard: Whether to automatically submit score to leaderboard (default: "true")
 */

import QuizEngine from './quiz-engine.js';
import firebaseService from '../../core/firebase/firebase-service.js';
import stateManager from '../../core/state/state-manager.js';
import { getCurrentUser, isAuthenticated } from '../../core/auth/auth.js';

/**
 * Initialize the quiz in Webflow
 */
function initQuizWebflow() {
  try {
    console.log('Initializing quiz in Webflow');
    
    // Find all quiz containers
    const containers = document.querySelectorAll('.quiz-container');
    
    if (containers.length === 0) {
      console.warn('No quiz containers found');
      return;
    }
    
    // Initialize each quiz
    containers.forEach(container => {
      // Get configuration from data attributes
      const quizId = container.getAttribute('data-quiz-id');
      const boothType = container.getAttribute('data-booth-type') || 'media';
      const shuffleQuestions = container.getAttribute('data-shuffle-questions') === 'true';
      const shuffleAnswers = container.getAttribute('data-shuffle-answers') !== 'false';
      const showFeedback = container.getAttribute('data-show-feedback') !== 'false';
      const showResults = container.getAttribute('data-show-results') !== 'false';
      const autoSubmitToLeaderboard = container.getAttribute('data-auto-submit-to-leaderboard') !== 'false';
      
      // Check if quiz ID is provided
      if (!quizId) {
        console.error('Quiz container missing data-quiz-id attribute');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'quiz-error';
        errorElement.textContent = 'Quiz ID not specified';
        container.appendChild(errorElement);
        
        return;
      }
      
      // Create quiz engine instance
      const quizEngine = new QuizEngine({
        quizId,
        boothType,
        container,
        shuffleQuestions,
        shuffleAnswers,
        showFeedback,
        showResults,
        autoSubmitToLeaderboard,
        onComplete: handleComplete,
        onProgress: handleProgress
      });
      
      // Store quiz engine instance on container for later access
      container.quizEngine = quizEngine;
      
      // Check if user is authenticated
      if (!isAuthenticated() && autoSubmitToLeaderboard) {
        showAuthenticationPrompt(container, quizEngine);
        return;
      }
      
      // Load quiz data
      quizEngine.loadQuiz()
        .then(() => {
          renderQuizIntro(container, quizEngine.quiz);
        })
        .catch(error => {
          console.error('Failed to load quiz:', error);
          
          const errorElement = document.createElement('div');
          errorElement.className = 'quiz-error';
          errorElement.textContent = 'Failed to load quiz: ' + error.message;
          container.appendChild(errorElement);
        });
      
      // Handle quiz completion
      function handleComplete(results) {
        // Dispatch custom event
        const event = new CustomEvent('quiz-completed', { 
          detail: { 
            quizId, 
            boothType,
            results 
          },
          bubbles: true 
        });
        container.dispatchEvent(event);
        
        // Update user progress in state
        updateUserProgress(quizId, results);
      }
      
      // Handle quiz progress
      function handleProgress(progress) {
        // Dispatch custom event
        const event = new CustomEvent('quiz-progress', { 
          detail: { 
            quizId, 
            boothType,
            progress 
          },
          bubbles: true 
        });
        container.dispatchEvent(event);
        
        // Update progress bar if exists
        const progressBar = container.querySelector('.quiz-progress-bar');
        if (progressBar) {
          progressBar.style.width = `${progress.progress * 100}%`;
        }
      }
    });
    
    console.log('Quizzes initialized');
  } catch (error) {
    console.error('Failed to initialize quiz:', error);
  }
}

/**
 * Show authentication prompt
 * @param {HTMLElement} container - Quiz container
 * @param {QuizEngine} quizEngine - Quiz engine instance
 */
function showAuthenticationPrompt(container, quizEngine) {
  // Clear container
  container.innerHTML = '';
  
  // Create auth container
  const authContainer = document.createElement('div');
  authContainer.className = 'quiz-auth-prompt';
  
  // Add title
  const title = document.createElement('h2');
  title.className = 'quiz-auth-title';
  title.textContent = 'Sign In Required';
  authContainer.appendChild(title);
  
  // Add message
  const message = document.createElement('p');
  message.className = 'quiz-auth-message';
  message.textContent = 'Please sign in to take the quiz and save your results to the leaderboard.';
  authContainer.appendChild(message);
  
  // Add sign in button
  const signInButton = document.createElement('button');
  signInButton.className = 'quiz-auth-button';
  signInButton.textContent = 'Sign In';
  signInButton.addEventListener('click', () => {
    // Redirect to sign in page
    window.location.href = '/sign-in?redirect=' + encodeURIComponent(window.location.href);
  });
  authContainer.appendChild(signInButton);
  
  // Add guest mode option
  const guestModeContainer = document.createElement('div');
  guestModeContainer.className = 'quiz-guest-mode';
  
  const guestModeText = document.createElement('p');
  guestModeText.textContent = 'Or continue as guest (results won\'t be saved)';
  guestModeContainer.appendChild(guestModeText);
  
  const guestModeButton = document.createElement('button');
  guestModeButton.className = 'quiz-guest-button';
  guestModeButton.textContent = 'Continue as Guest';
  guestModeButton.addEventListener('click', () => {
    // Load quiz in guest mode
    quizEngine.options.autoSubmitToLeaderboard = false;
    quizEngine.loadQuiz()
      .then(() => {
        renderQuizIntro(container, quizEngine.quiz);
      })
      .catch(error => {
        console.error('Failed to load quiz:', error);
        
        const errorElement = document.createElement('div');
        errorElement.className = 'quiz-error';
        errorElement.textContent = 'Failed to load quiz: ' + error.message;
        container.appendChild(errorElement);
      });
  });
  guestModeContainer.appendChild(guestModeButton);
  
  authContainer.appendChild(guestModeContainer);
  
  container.appendChild(authContainer);
}

/**
 * Update user progress in state
 * @param {string} quizId - Quiz ID
 * @param {Object} results - Quiz results
 */
function updateUserProgress(quizId, results) {
  try {
    const userProgress = stateManager.get('user.progress') || {};
    userProgress.quizzes = userProgress.quizzes || {};
    userProgress.quizzes[quizId] = {
      completed: true,
      score: results.score,
      maxScore: results.maxScore,
      percentage: results.percentage,
      timestamp: new Date().toISOString()
    };
    
    stateManager.set('user.progress', userProgress);
  } catch (error) {
    console.error('Failed to update user progress:', error);
  }
}

/**
 * Render quiz intro screen
 * @param {HTMLElement} container - Quiz container
 * @param {Object} quiz - Quiz data
 */
function renderQuizIntro(container, quiz) {
  // Clear container
  container.innerHTML = '';
  
  // Create intro container
  const introContainer = document.createElement('div');
  introContainer.className = 'quiz-intro';
  
  // Add title
  const title = document.createElement('h2');
  title.className = 'quiz-title';
  title.textContent = quiz.title || 'Quiz';
  introContainer.appendChild(title);
  
  // Add description if available
  if (quiz.description) {
    const description = document.createElement('div');
    description.className = 'quiz-description';
    description.innerHTML = quiz.description;
    introContainer.appendChild(description);
  }
  
  // Add quiz info
  const infoContainer = document.createElement('div');
  infoContainer.className = 'quiz-info';
  
  // Add question count
  const questionCount = document.createElement('div');
  questionCount.className = 'quiz-question-count';
  questionCount.textContent = `${container.quizEngine.questions.length} Questions`;
  infoContainer.appendChild(questionCount);
  
  // Add estimated time
  const estimatedTime = document.createElement('div');
  estimatedTime.className = 'quiz-estimated-time';
  // Estimate 30 seconds per question
  const minutes = Math.ceil((container.quizEngine.questions.length * 30) / 60);
  estimatedTime.textContent = `Estimated Time: ${minutes} min`;
  infoContainer.appendChild(estimatedTime);
  
  introContainer.appendChild(infoContainer);
  
  // Add start button
  const startButton = document.createElement('button');
  startButton.className = 'quiz-start-button';
  startButton.textContent = 'Start Quiz';
  startButton.addEventListener('click', () => {
    container.quizEngine.start();
  });
  
  introContainer.appendChild(startButton);
  
  container.appendChild(introContainer);
}

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initQuizWebflow);

// Fallback for Webflow's preview mode where DOMContentLoaded might have already fired
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initQuizWebflow, 1);
}

// Expose API for external access
window.Atlas25Quiz = {
  /**
   * Start a quiz by ID
   * @param {string} quizId - Quiz ID
   * @param {string} [containerId] - Container ID (optional)
   */
  startQuiz: function(quizId, containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.quiz-container');
      
    if (container) {
      if (container.quizEngine && container.quizEngine.quizId === quizId) {
        container.quizEngine.start();
      } else {
        // Create new quiz engine
        const quizEngine = new QuizEngine({
          quizId,
          container,
          onComplete: (results) => {
            container.dispatchEvent(new CustomEvent('quiz-completed', { 
              detail: { quizId, results },
              bubbles: true 
            }));
          }
        });
        
        container.quizEngine = quizEngine;
        
        quizEngine.loadQuiz()
          .then(() => quizEngine.start())
          .catch(error => {
            console.error('Failed to load quiz:', error);
            
            const errorElement = document.createElement('div');
            errorElement.className = 'quiz-error';
            errorElement.textContent = 'Failed to load quiz: ' + error.message;
            container.appendChild(errorElement);
          });
      }
    } else {
      console.error('Quiz container not found');
    }
  },
  
  /**
   * Get quiz results for the current user
   * @param {string} [quizId] - Quiz ID (optional, if not provided, returns all results)
   * @returns {Promise<Array>} - Quiz results
   */
  getQuizResults: async function(quizId) {
    try {
      const user = getCurrentUser();
      
      if (!user) {
        console.warn('User not authenticated');
        return [];
      }
      
      const query = {
        where: {
          field: 'userId',
          operator: '==',
          value: user.uid
        },
        orderBy: {
          field: 'timestamp',
          direction: 'desc'
        }
      };
      
      if (quizId) {
        query.where = [
          query.where,
          {
            field: 'quizId',
            operator: '==',
            value: quizId
          }
        ];
      }
      
      const results = await firebaseService.getCollection('quiz_results', query);
      return results;
    } catch (error) {
      console.error('Failed to get quiz results:', error);
      return [];
    }
  },
  
  /**
   * Get leaderboard data
   * @param {string} [boothType='media'] - Booth type ('media' or 'coaching')
   * @param {number} [limit=10] - Maximum number of entries to return
   * @returns {Promise<Array>} - Leaderboard entries
   */
  getLeaderboard: async function(boothType = 'media', limit = 10) {
    try {
      const leaderboardId = boothType === 'media' 
        ? 'media_booth_leaderboard' 
        : 'coaching_booth_leaderboard';
      
      const query = {
        where: {
          field: 'boardId',
          operator: '==',
          value: leaderboardId
        },
        orderBy: {
          field: 'score',
          direction: 'desc'
        },
        limit
      };
      
      const entries = await firebaseService.getCollection('leaderboard_entries', query);
      
      // Add rank
      return entries.map((entry, index) => ({
        ...entry,
        rank: index + 1
      }));
    } catch (error) {
      console.error('Failed to get leaderboard:', error);
      return [];
    }
  }
};

export default initQuizWebflow;