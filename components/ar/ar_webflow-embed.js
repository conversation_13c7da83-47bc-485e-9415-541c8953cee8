/**
 * @file ar_webflow-embed.js
 * @description Webflow embed for AR experience component
 * 
 * HOW TO USE:
 * 1. Add this code to a Webflow embed element
 * 2. Ensure you have the following elements with these specific classes:
 *    - .ar-container: Container for the AR experience
 *    - .ar-title: (Optional) Title element
 *    - .ar-description: (Optional) Description element
 *    - .ar-start-button: Button to start the AR experience
 * 3. Add data attributes to the container to configure the AR experience:
 *    - data-experience-id: ID of the AR experience (required)
 *    - data-ar-framework: AR framework to use (default: "ar.js")
 *    - data-offline-support: Whether to support offline mode (default: "true")
 *    - data-track-engagement: Whether to track user engagement (default: "true")
 *    - data-auto-load: Whether to load data automatically (default: "true")
 * 4. Include the required AR.js scripts before this embed:
 *    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
 *    <script src="https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js"></script>
 */

import ARExperience from './ar-experience.js';
import offlineManager from '../../core/offline/offline-manager.js';
import stateManager from '../../core/state/state-manager.js';
import { getCurrentUser, isAuthenticated } from '../../core/auth/auth.js';

/**
 * Initialize the AR experience in Webflow
 */
function initARWebflow() {
  try {
    console.log('Initializing AR experience in Webflow');
    
    // Initialize offline manager
    offlineManager.initialize().catch(error => {
      console.error('Failed to initialize offline manager:', error);
    });
    
    // Find all AR containers
    const containers = document.querySelectorAll('.ar-container');
    
    if (containers.length === 0) {
      console.warn('No AR containers found');
      return;
    }
    
    // Initialize each AR experience
    containers.forEach(container => {
      // Get configuration from data attributes
      const experienceId = container.getAttribute('data-experience-id');
      const arFramework = container.getAttribute('data-ar-framework') || 'ar.js';
      const offlineSupport = container.getAttribute('data-offline-support') !== 'false';
      const trackEngagement = container.getAttribute('data-track-engagement') !== 'false';
      const autoLoad = container.getAttribute('data-auto-load') !== 'false';
      
      // Check if experience ID is provided
      if (!experienceId) {
        console.error('AR container missing data-experience-id attribute');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'ar-error';
        errorElement.textContent = 'AR experience ID not specified';
        container.appendChild(errorElement);
        
        return;
      }
      
      // Create AR experience instance
      const arExperience = new ARExperience({
        experienceId,
        container,
        arFramework,
        offlineSupport,
        trackEngagement,
        autoLoadAssets: false, // We'll handle loading manually
        onLoad: handleLoad,
        onError: handleError,
        onARStart: handleARStart,
        onAREnd: handleAREnd,
        onMarkerFound: handleMarkerFound,
        onMarkerLost: handleMarkerLost,
        onContentComplete: handleContentComplete
      });
      
      // Store AR experience instance on container for later access
      container.arExperience = arExperience;
      
      // Add loading indicator
      const loadingElement = document.createElement('div');
      loadingElement.className = 'ar-loading';
      loadingElement.innerHTML = '<div class="ar-loading-spinner"></div><div class="ar-loading-text">Loading AR Experience...</div>';
      container.appendChild(loadingElement);
      
      // Check if user is authenticated for tracking
      if (trackEngagement && !isAuthenticated()) {
        showAuthenticationPrompt(container, arExperience);
        return;
      }
      
      // Load experience data if auto-load is enabled
      if (autoLoad) {
        arExperience.load()
          .then(() => {
            // Remove loading indicator
            const loadingElement = container.querySelector('.ar-loading');
            if (loadingElement) {
              loadingElement.remove();
            }
            
            // Render AR intro
            renderARIntro(container, arExperience.experience);
          })
          .catch(error => {
            console.error('Failed to load AR experience:', error);
            
            // Update loading indicator to show error
            const loadingElement = container.querySelector('.ar-loading');
            if (loadingElement) {
              loadingElement.innerHTML = `<div class="ar-error">Failed to load AR experience: ${error.message}</div>`;
            }
          });
      } else {
        // Add start button
        const startButton = document.createElement('button');
        startButton.className = 'ar-start-button';
        startButton.textContent = 'Load AR Experience';
        startButton.addEventListener('click', () => {
          // Show loading indicator
          startButton.textContent = 'Loading...';
          startButton.disabled = true;
          
          arExperience.load()
            .then(() => {
              // Remove start button and loading indicator
              startButton.remove();
              const loadingElement = container.querySelector('.ar-loading');
              if (loadingElement) {
                loadingElement.remove();
              }
              
              // Render AR intro
              renderARIntro(container, arExperience.experience);
            })
            .catch(error => {
              console.error('Failed to load AR experience:', error);
              
              // Reset start button
              startButton.textContent = 'Load AR Experience';
              startButton.disabled = false;
              
              // Show error
              const errorElement = document.createElement('div');
              errorElement.className = 'ar-error';
              errorElement.textContent = `Failed to load AR experience: ${error.message}`;
              container.appendChild(errorElement);
            });
        });
        
        container.appendChild(startButton);
      }
      
      // Handle experience load
      function handleLoad(experience) {
        // Update user progress in state
        updateUserProgress(experienceId, 'loaded');
        
        // Dispatch custom event
        const event = new CustomEvent('ar-experience-loaded', { 
          detail: { experienceId, experience },
          bubbles: true 
        });
        container.dispatchEvent(event);
      }
      
      // Handle error
      function handleError(error) {
        // Dispatch custom event
        const event = new CustomEvent('ar-experience-error', { 
          detail: { experienceId, error },
          bubbles: true 
        });
        container.dispatchEvent(event);
      }
      
      // Handle AR session start
      function handleARStart() {
        // Update UI
        container.classList.add('ar-active');
        
        // Update user progress in state
        updateUserProgress(experienceId, 'started');
        
        // Dispatch custom event
        const event = new CustomEvent('ar-session-started', { 
          detail: { experienceId },
          bubbles: true 
        });
        container.dispatchEvent(event);
      }
      
      // Handle AR session end
      function handleAREnd() {
        // Update UI
        container.classList.remove('ar-active');
        
        // Update user progress in state
        updateUserProgress(experienceId, 'ended');
        
        // Dispatch custom event
        const event = new CustomEvent('ar-session-ended', { 
          detail: { experienceId },
          bubbles: true 
        });
        container.dispatchEvent(event);
      }
      
      // Handle marker found
      function handleMarkerFound(markerId, marker) {
        // Update UI to show which marker was found
        const markerIndicator = document.createElement('div');
        markerIndicator.className = 'ar-marker-indicator';
        markerIndicator.textContent = `Scanning: ${marker.content?.title || 'Marker ' + markerId}`;
        markerIndicator.dataset.markerId = markerId;
        
        // Remove any existing indicator for this marker
        const existingIndicator = container.querySelector(`.ar-marker-indicator[data-marker-id="${markerId}"]`);
        if (existingIndicator) {
          existingIndicator.remove();
        }
        
        container.appendChild(markerIndicator);
        
        // Fade out after 3 seconds
        setTimeout(() => {
          markerIndicator.classList.add('fade-out');
          setTimeout(() => {
            if (markerIndicator.parentNode) {
              markerIndicator.remove();
            }
          }, 1000);
        }, 3000);
        
        // Dispatch custom event
        const event = new CustomEvent('ar-marker-found', { 
          detail: { experienceId, markerId, marker },
          bubbles: true 
        });
        container.dispatchEvent(event);
      }
      
      // Handle marker lost
      function handleMarkerLost(markerId, marker) {
        // Dispatch custom event
        const event = new CustomEvent('ar-marker-lost', { 
          detail: { experienceId, markerId, marker },
          bubbles: true 
        });
        container.dispatchEvent(event);
      }
      
      // Handle content complete
      function handleContentComplete(markerId, content) {
        // Show completion message
        const completionMessage = document.createElement('div');
        completionMessage.className = 'ar-completion-message';
        completionMessage.textContent = `Completed: ${content.title || 'Content'}`;
        if (content.points) {
          completionMessage.textContent += ` (+${content.points} points)`;
        }
        
        container.appendChild(completionMessage);
        
        // Fade out after 3 seconds
        setTimeout(() => {
          completionMessage.classList.add('fade-out');
          setTimeout(() => {
            if (completionMessage.parentNode) {
              completionMessage.remove();
            }
          }, 1000);
        }, 3000);
        
        // Update user progress in state
        updateUserProgress(experienceId, 'content-complete', { markerId, content });
        
        // Dispatch custom event
        const event = new CustomEvent('ar-content-complete', { 
          detail: { experienceId, markerId, content },
          bubbles: true 
        });
        container.dispatchEvent(event);
      }
    });
    
    // Listen for quiz launch events
    document.addEventListener('launch-quiz', (event) => {
      const { quizId, source, experienceId } = event.detail;
      
      if (source === 'ar-experience') {
        console.log(`Launching quiz ${quizId} from AR experience ${experienceId}`);
        
        // Use the Atlas25Quiz API if available
        if (window.Atlas25Quiz && typeof window.Atlas25Quiz.startQuiz === 'function') {
          window.Atlas25Quiz.startQuiz(quizId);
        } else {
          // Fallback: redirect to quiz page
          window.location.href = `/quiz?id=${quizId}&source=ar&experienceId=${experienceId}`;
        }
      }
    });
    
    console.log('AR experiences initialized');
  } catch (error) {
    console.error('Failed to initialize AR experience:', error);
  }
}

/**
 * Show authentication prompt
 * @param {HTMLElement} container - AR container
 * @param {ARExperience} arExperience - AR experience instance
 */
function showAuthenticationPrompt(container, arExperience) {
  // Clear container
  container.innerHTML = '';
  
  // Create auth container
  const authContainer = document.createElement('div');
  authContainer.className = 'ar-auth-prompt';
  
  // Add title
  const title = document.createElement('h2');
  title.className = 'ar-auth-title';
  title.textContent = 'Sign In Required';
  authContainer.appendChild(title);
  
  // Add message
  const message = document.createElement('p');
  message.className = 'ar-auth-message';
  message.textContent = 'Please sign in to track your progress and earn points for the leaderboard.';
  authContainer.appendChild(message);
  
  // Add sign in button
  const signInButton = document.createElement('button');
  signInButton.className = 'ar-auth-button';
  signInButton.textContent = 'Sign In';
  signInButton.addEventListener('click', () => {
    // Redirect to sign in page
    window.location.href = '/sign-in?redirect=' + encodeURIComponent(window.location.href);
  });
  authContainer.appendChild(signInButton);
  
  // Add guest mode option
  const guestModeContainer = document.createElement('div');
  guestModeContainer.className = 'ar-guest-mode';
  
  const guestModeText = document.createElement('p');
  guestModeText.textContent = 'Or continue as guest (progress won\'t be saved)';
  guestModeContainer.appendChild(guestModeText);
  
  const guestModeButton = document.createElement('button');
  guestModeButton.className = 'ar-guest-button';
  guestModeButton.textContent = 'Continue as Guest';
  guestModeButton.addEventListener('click', () => {
    // Load AR in guest mode
    arExperience.options.trackEngagement = false;
    
    // Add loading indicator
    const loadingElement = document.createElement('div');
    loadingElement.className = 'ar-loading';
    loadingElement.innerHTML = '<div class="ar-loading-spinner"></div><div class="ar-loading-text">Loading AR Experience...</div>';
    
    // Clear container and add loading indicator
    container.innerHTML = '';
    container.appendChild(loadingElement);
    
    arExperience.load()
      .then(() => {
        // Remove loading indicator
        loadingElement.remove();
        
        // Render AR intro
        renderARIntro(container, arExperience.experience);
      })
      .catch(error => {
        console.error('Failed to load AR experience:', error);
        
        // Show error
        loadingElement.innerHTML = `<div class="ar-error">Failed to load AR experience: ${error.message}</div>`;
      });
  });
  guestModeContainer.appendChild(guestModeButton);
  
  authContainer.appendChild(guestModeContainer);
  
  container.appendChild(authContainer);
}

/**
 * Update user progress in state
 * @param {string} experienceId - Experience ID
 * @param {string} action - Action performed
 * @param {Object} [data] - Additional data
 */
function updateUserProgress(experienceId, action, data = {}) {
  try {
    const userProgress = stateManager.get('user.progress') || {};
    userProgress.arExperiences = userProgress.arExperiences || {};
    
    if (!userProgress.arExperiences[experienceId]) {
      userProgress.arExperiences[experienceId] = {
        started: false,
        completed: false,
        markersScanned: [],
        lastAction: null,
        lastActionTimestamp: null
      };
    }
    
    const experience = userProgress.arExperiences[experienceId];
    
    // Update based on action
    switch (action) {
      case 'loaded':
        experience.loaded = true;
        break;
        
      case 'started':
        experience.started = true;
        experience.startTime = new Date().toISOString();
        break;
        
      case 'ended':
        if (experience.startTime) {
          const startTime = new Date(experience.startTime).getTime();
          const endTime = new Date().getTime();
          const duration = (endTime - startTime) / 1000; // in seconds
          
          experience.totalDuration = (experience.totalDuration || 0) + duration;
          experience.lastSessionDuration = duration;
        }
        break;
        
      case 'content-complete':
        if (data.markerId && !experience.markersScanned.includes(data.markerId)) {
          experience.markersScanned.push(data.markerId);
        }
        break;
    }
    
    // Update last action
    experience.lastAction = action;
    experience.lastActionTimestamp = new Date().toISOString();
    
    // Check if all markers have been scanned
    if (experience.markersScanned.length >= 3) { // Assuming 3 markers for completion
      experience.completed = true;
    }
    
    stateManager.set('user.progress', userProgress);
  } catch (error) {
    console.error('Failed to update user progress:', error);
  }
}

/**
 * Render AR intro screen
 * @param {HTMLElement} container - AR container
 * @param {Object} experience - Experience data
 */
function renderARIntro(container, experience) {
  // Clear container
  container.innerHTML = '';
  
  // Create intro container
  const introContainer = document.createElement('div');
  introContainer.className = 'ar-intro';
  
  // Add title
  const title = document.createElement('h2');
  title.className = 'ar-title';
  title.textContent = experience.title || 'AR Experience';
  introContainer.appendChild(title);
  
  // Add description if available
  if (experience.description) {
    const description = document.createElement('div');
    description.className = 'ar-description';
    description.innerHTML = experience.description;
    introContainer.appendChild(description);
  }
  
  // Add marker info
  if (experience.markers && experience.markers.length > 0) {
    const markersContainer = document.createElement('div');
    markersContainer.className = 'ar-markers-info';
    
    const markersTitle = document.createElement('h3');
    markersTitle.textContent = 'Scan These Markers:';
    markersContainer.appendChild(markersTitle);
    
    const markersList = document.createElement('ul');
    markersList.className = 'ar-markers-list';
    
    experience.markers.forEach(marker => {
      const markerItem = document.createElement('li');
      markerItem.className = 'ar-marker-item';
      markerItem.textContent = marker.content?.title || `Marker ${marker.id}`;
      
      if (marker.content?.points) {
        const pointsBadge = document.createElement('span');
        pointsBadge.className = 'ar-points-badge';
        pointsBadge.textContent = `+${marker.content.points} pts`;
        markerItem.appendChild(pointsBadge);
      }
      
      markersList.appendChild(markerItem);
    });
    
    markersContainer.appendChild(markersList);
    introContainer.appendChild(markersContainer);
  }
  
  // Add instructions
  const instructions = document.createElement('div');
  instructions.className = 'ar-instructions';
  instructions.innerHTML = `
    <h3>Instructions:</h3>
    <ol>
      <li>Click "Start AR" to launch the experience</li>
      <li>Point your device at the markers around the booth</li>
      <li>Watch the videos or interact with 3D models</li>
      <li>Complete all markers to earn points and unlock a quiz</li>
    </ol>
  `;
  introContainer.appendChild(instructions);
  
  // Add start button
  const startButton = document.createElement('button');
  startButton.className = 'ar-start-button';
  startButton.textContent = 'Start AR Experience';
  startButton.addEventListener('click', () => {
    container.arExperience.startAR();
    
    // Replace intro with AR view
    container.innerHTML = '';
    
    // Add AR UI elements
    const arUI = document.createElement('div');
    arUI.className = 'ar-ui';
    
    // Add close button
    const closeButton = document.createElement('button');
    closeButton.className = 'ar-close-button';
    closeButton.textContent = '✕';
    closeButton.addEventListener('click', () => {
      container.arExperience.stopAR();
      renderARIntro(container, experience);
    });
    
    arUI.appendChild(closeButton);
    container.appendChild(arUI);
  });
  
  introContainer.appendChild(startButton);
  
  container.appendChild(introContainer);
}

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initARWebflow);

// Fallback for Webflow's preview mode where DOMContentLoaded might have already fired
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initARWebflow, 1);
}

// Add basic styles
const style = document.createElement('style');
style.textContent = `
  .ar-container {
    position: relative;
    width: 100%;
    min-height: 400px;
    background-color: #f5f5f5;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .ar-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 10;
  }
  
  .ar-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: ar-spin 1s linear infinite;
    margin-bottom: 10px;
  }
  
  @keyframes ar-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .ar-error {
    color: #e74c3c;
    padding: 10px;
    text-align: center;
    font-weight: bold;
  }
  
  .ar-intro {
    padding: 20px;
    text-align: center;
  }
  
  .ar-title {
    font-size: 24px;
    margin-bottom: 10px;
    color: #2c3e50;
  }
  
  .ar-description {
    margin-bottom: 20px;
    color: #34495e;
  }
  
  .ar-markers-info {
    margin: 20px 0;
    text-align: left;
  }
  
  .ar-markers-list {
    list-style-type: none;
    padding: 0;
  }
  
  .ar-marker-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .ar-points-badge {
    background-color: #2ecc71;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
  }
  
  .ar-instructions {
    margin: 20px 0;
    text-align: left;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
  }
  
  .ar-instructions h3 {
    margin-top: 0;
  }
  
  .ar-instructions ol {
    padding-left: 20px;
  }
  
  .ar-start-button {
    padding: 12px 24px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.3s;
  }
  
  .ar-start-button:hover {
    background-color: #2980b9;
  }
  
  .ar-ui {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
  }
  
  .ar-close-button {
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
  }
  
  .ar-marker-indicator {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    z-index: 100;
    transition: opacity 0.5s;
  }
  
  .ar-completion-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(46, 204, 113, 0.9);
    color: white;
    padding: 15px 30px;
    border-radius: 5px;
    font-weight: bold;
    z-index: 100;
    transition: opacity 0.5s;
  }
  
  .fade-out {
    opacity: 0;
  }
  
  .ar-auth-prompt {
    padding: 20px;
    text-align: center;
  }
  
  .ar-auth-title {
    font-size: 24px;
    margin-bottom: 10px;
  }
  
  .ar-auth-message {
    margin-bottom: 20px;
  }
  
  .ar-auth-button {
    padding: 12px 24px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  
  .ar-guest-mode {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
  }
  
  .ar-guest-button {
    padding: 8px 16px;
    background-color: #95a5a6;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  
  a-scene {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
`;

document.head.appendChild(style);

// Expose API for external access
window.Atlas25AR = {
  /**
   * Start AR experience
   * @param {string} [containerId] - Container ID (optional)
   * @returns {Promise<void>}
   */
  startAR: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.ar-container');
      
    if (container && container.arExperience) {
      return container.arExperience.startAR();
    }
    
    return Promise.reject(new Error('AR experience not found'));
  },
  
  /**
   * Stop AR experience
   * @param {string} [containerId] - Container ID (optional)
   */
  stopAR: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.ar-container');
      
    if (container && container.arExperience) {
      container.arExperience.stopAR();
    }
  },
  
  /**
   * Load AR experience
   * @param {string} [containerId] - Container ID (optional)
   * @returns {Promise<Object>} - Experience data
   */
  load: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.ar-container');
      
    if (container && container.arExperience) {
      return container.arExperience.load();
    }
    
    return Promise.reject(new Error('AR experience not found'));
  },
  
  /**
   * Get scanned markers
   * @param {string} [containerId] - Container ID (optional)
   * @returns {Array} - Array of scanned marker IDs
   */
  getScannedMarkers: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.ar-container');
      
    if (container && container.arExperience) {
      return Array.from(container.arExperience.scannedMarkers);
    }
    
    return [];
  }
};

export default initARWebflow;