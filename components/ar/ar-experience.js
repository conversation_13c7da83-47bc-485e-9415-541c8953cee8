/**
 * @file ar-experience.js
 * @description AR experience component for Atlas25 Web App
 * @module components/ar
 */

import offlineManager from '../../core/offline/offline-manager.js';
import syncQueue from '../../core/offline/sync-queue.js';
import stateManager from '../../core/state/state-manager.js';
import firebaseService from '../../core/firebase/firebase-service.js';
import { getCurrentUser } from '../../core/auth/auth.js';

/**
 * AR marker types
 * @enum {string}
 */
export const AR_MARKER_TYPES = {
  HIRO: 'hiro',
  KANJI: 'kanji',
  PATTERN: 'pattern',
  BARCODE: 'barcode',
  CUSTOM: 'custom'
};

/**
 * AR content types
 * @enum {string}
 */
export const AR_CONTENT_TYPES = {
  MODEL: 'model',
  VIDEO: 'video',
  IMAGE: 'image',
  TEXT: 'text'
};

/**
 * ARExperience class for managing AR experiences
 */
class ARExperience {
  /**
   * Create an AR experience
   * @param {Object} options - Configuration options
   * @param {string} options.experienceId - ID of the AR experience
   * @param {HTMLElement|string} [options.container] - Container element or selector
   * @param {string} [options.arFramework='ar.js'] - AR framework to use ('ar.js', 'model-viewer', 'aframe', 'webxr')
   * @param {boolean} [options.offlineSupport=true] - Whether to support offline mode
   * @param {boolean} [options.trackEngagement=true] - Whether to track user engagement
   * @param {boolean} [options.autoLoadAssets=true] - Whether to automatically load assets
   * @param {Function} [options.onLoad] - Callback function when AR experience is loaded
   * @param {Function} [options.onError] - Callback function when error occurs
   * @param {Function} [options.onARStart] - Callback function when AR session starts
   * @param {Function} [options.onAREnd] - Callback function when AR session ends
   * @param {Function} [options.onMarkerFound] - Callback function when marker is found
   * @param {Function} [options.onMarkerLost] - Callback function when marker is lost
   * @param {Function} [options.onContentComplete] - Callback function when content (like video) completes
   */
  constructor(options) {
    this.options = {
      arFramework: 'ar.js',
      offlineSupport: true,
      trackEngagement: true,
      autoLoadAssets: true,
      ...options
    };
    
    this.experienceId = options.experienceId;
    this.container = typeof options.container === 'string' 
      ? document.querySelector(options.container) 
      : options.container;
      
    this.experience = null;
    this.assets = [];
    this.markers = [];
    this.scannedMarkers = new Set();
    this.loaded = false;
    this.arActive = false;
    this.error = null;
    this.startTime = null;
    this.engagementData = {
      totalDuration: 0,
      interactions: 0,
      markersScanned: 0,
      lastInteraction: null
    };
    
    // Framework-specific elements
    this.arScene = null;
    this.arCamera = null;
    this.arMarkers = [];
    this.videoElements = {};
    this.modelElements = {};
    
    // Offline cache
    this.assetCache = new Map();
    this.offlineMode = !navigator.onLine;
    
    // Bind methods
    this.load = this.load.bind(this);
    this.startAR = this.startAR.bind(this);
    this.stopAR = this.stopAR.bind(this);
    this.handleMarkerFound = this.handleMarkerFound.bind(this);
    this.handleMarkerLost = this.handleMarkerLost.bind(this);
    this.handleContentComplete = this.handleContentComplete.bind(this);
    
    // Set up network listeners
    this.setupNetworkListeners();
    
    // Auto-load if specified
    if (this.options.autoLoadAssets) {
      this.load().catch(error => {
        console.error('Failed to auto-load AR experience:', error);
      });
    }
  }

  /**
   * Set up network listeners for online/offline events
   * @private
   */
  setupNetworkListeners() {
    window.addEventListener('online', () => {
      this.offlineMode = false;
      console.log('AR Experience: Online mode');
      
      // Sync engagement data if tracking is enabled
      if (this.options.trackEngagement) {
        this.syncEngagementData();
      }
    });
    
    window.addEventListener('offline', () => {
      this.offlineMode = true;
      console.log('AR Experience: Offline mode');
    });
  }

  /**
   * Load AR experience data and assets
   * @returns {Promise<void>}
   */
  async load() {
    try {
      console.log(`Loading AR experience: ${this.experienceId}`);
      
      // Fetch experience data
      this.experience = await this.fetchExperienceData();
      
      // Load required libraries based on framework
      await this.loadFramework();
      
      // Load assets
      await this.loadAssets();
      
      this.loaded = true;
      
      if (this.options.onLoad) {
        this.options.onLoad(this.experience);
      }
      
      console.log('AR experience loaded successfully');
    } catch (error) {
      console.error('Failed to load AR experience:', error);
      this.error = error.message || 'Failed to load AR experience';
      
      if (this.options.onError) {
        this.options.onError(error);
      }
      
      throw error;
    }
  }

  /**
   * Fetch experience data from server or cache
   * @returns {Promise<Object>} - Experience data
   * @private
   */
  async fetchExperienceData() {
    try {
      // Check if we have cached data
      if (this.options.offlineSupport) {
        const cachedData = await this.getCachedExperienceData();
        if (cachedData && this.offlineMode) {
          console.log('Using cached AR experience data');
          return cachedData;
        }
      }
      
      // If online, fetch from server
      if (!this.offlineMode) {
        const data = await this.fetchFromServer();
        
        // Cache the data for offline use
        if (this.options.offlineSupport) {
          await this.cacheExperienceData(data);
        }
        
        return data;
      }
      
      throw new Error('Cannot fetch experience data: offline and no cached data available');
    } catch (error) {
      console.error('Error fetching experience data:', error);
      throw error;
    }
  }
  
  /**
   * Fetch experience data from server
   * @returns {Promise<Object>} - Experience data
   * @private
   */
  async fetchFromServer() {
    try {
      // In a real implementation, this would fetch from Firebase
      const experienceDoc = await firebaseService.getDocument('ar_experiences', this.experienceId);
      
      if (!experienceDoc) {
        throw new Error(`AR experience not found: ${this.experienceId}`);
      }
      
      return experienceDoc;
    } catch (error) {
      // If Firebase fetch fails, use fallback data for demo purposes
      console.warn('Falling back to demo AR experience data');
      
      // Sample experience data
      return {
        id: this.experienceId,
        title: 'NBA Coaching Experience',
        description: 'Scan the markers to learn basketball techniques',
        type: 'marker-based',
        framework: this.options.arFramework,
        markers: [
          {
            id: 'marker-1',
            type: AR_MARKER_TYPES.PATTERN,
            patternUrl: '/markers/pattern-marker-1.patt',
            content: {
              type: AR_CONTENT_TYPES.VIDEO,
              url: '/videos/basketball-technique-1.mp4',
              title: 'Free Throw Technique',
              duration: 30,
              points: 5
            }
          },
          {
            id: 'marker-2',
            type: AR_MARKER_TYPES.PATTERN,
            patternUrl: '/markers/pattern-marker-2.patt',
            content: {
              type: AR_CONTENT_TYPES.VIDEO,
              url: '/videos/basketball-technique-2.mp4',
              title: 'Dribbling Skills',
              duration: 45,
              points: 5
            }
          },
          {
            id: 'marker-3',
            type: AR_MARKER_TYPES.PATTERN,
            patternUrl: '/markers/pattern-marker-3.patt',
            content: {
              type: AR_CONTENT_TYPES.MODEL,
              url: '/models/basketball-court.glb',
              title: '3D Basketball Court',
              scale: 0.5,
              points: 5
            }
          }
        ],
        settings: {
          quizAfterCompletion: true,
          quizId: 'basketball-techniques-quiz',
          requiredMarkers: 3,
          timeLimit: 300, // 5 minutes
          cameraSettings: {
            facingMode: 'environment'
          }
        }
      };
    }
  }
  
  /**
   * Cache experience data for offline use
   * @param {Object} data - Experience data to cache
   * @returns {Promise<void>}
   * @private
   */
  async cacheExperienceData(data) {
    try {
      if (!this.options.offlineSupport) return;
      
      await offlineManager.storeOfflineData('ar_experiences', data, this.experienceId);
      console.log('AR experience data cached for offline use');
    } catch (error) {
      console.error('Failed to cache AR experience data:', error);
    }
  }
  
  /**
   * Get cached experience data
   * @returns {Promise<Object|null>} - Cached experience data or null if not found
   * @private
   */
  async getCachedExperienceData() {
    try {
      if (!this.options.offlineSupport) return null;
      
      const data = await offlineManager.getOfflineData('ar_experiences', this.experienceId);
      return data;
    } catch (error) {
      console.error('Failed to get cached AR experience data:', error);
      return null;
    }
  }

  /**
   * Load required AR framework
   * @returns {Promise<void>}
   * @private
   */
  async loadFramework() {
    const framework = this.options.arFramework;
    
    // Check if framework is already loaded
    if (framework === 'ar.js' && window.THREEx && window.THREEx.ArToolkitContext) {
      console.log('AR.js already loaded');
      return;
    }
    
    if (framework === 'aframe' && window.AFRAME) {
      console.log('A-Frame already loaded');
      return;
    }
    
    if (framework === 'model-viewer' && window.customElements && window.customElements.get('model-viewer')) {
      console.log('model-viewer already loaded');
      return;
    }
    
    // Load required scripts
    return new Promise((resolve, reject) => {
      const scripts = [];
      
      // Set script sources based on framework
      switch (framework) {
        case 'ar.js':
          // AR.js requires A-Frame first
          scripts.push({
            src: 'https://aframe.io/releases/1.4.0/aframe.min.js',
            id: 'aframe-script'
          });
          scripts.push({
            src: 'https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js',
            id: 'arjs-script'
          });
          break;
          
        case 'aframe':
          scripts.push({
            src: 'https://aframe.io/releases/1.4.0/aframe.min.js',
            id: 'aframe-script'
          });
          break;
          
        case 'model-viewer':
          scripts.push({
            src: 'https://unpkg.com/@google/model-viewer/dist/model-viewer.min.js',
            id: 'model-viewer-script'
          });
          break;
          
        case 'webxr':
          // WebXR is built into modern browsers, no need to load external script
          resolve();
          return;
          
        default:
          reject(new Error(`Unsupported AR framework: ${framework}`));
          return;
      }
      
      // Load scripts sequentially
      const loadScript = (index) => {
        if (index >= scripts.length) {
          resolve();
          return;
        }
        
        const scriptInfo = scripts[index];
        
        // Check if script is already loaded
        if (document.getElementById(scriptInfo.id)) {
          loadScript(index + 1);
          return;
        }
        
        const script = document.createElement('script');
        script.id = scriptInfo.id;
        script.src = scriptInfo.src;
        
        script.onload = () => {
          console.log(`${scriptInfo.id} loaded successfully`);
          loadScript(index + 1);
        };
        
        script.onerror = () => {
          const error = new Error(`Failed to load ${scriptInfo.id}`);
          console.error(error);
          reject(error);
        };
        
        document.head.appendChild(script);
      };
      
      loadScript(0);
    });
  }

  /**
   * Load AR assets
   * @returns {Promise<void>}
   * @private
   */
  async loadAssets() {
    if (!this.experience || !this.experience.markers) {
      console.warn('No markers or assets to load');
      return;
    }
    
    console.log(`Loading assets for ${this.experience.markers.length} markers`);
    
    // Store marker data
    this.markers = this.experience.markers;
    
    // Preload assets if offline support is enabled
    if (this.options.offlineSupport) {
      await this.preloadAssets();
    }
  }
  
  /**
   * Preload assets for offline use
   * @returns {Promise<void>}
   * @private
   */
  async preloadAssets() {
    const assetPromises = [];
    
    // Preload all marker pattern files
    for (const marker of this.markers) {
      if (marker.type === AR_MARKER_TYPES.PATTERN && marker.patternUrl) {
        assetPromises.push(this.preloadAsset(marker.patternUrl, 'pattern'));
      }
      
      // Preload content based on type
      if (marker.content) {
        const content = marker.content;
        
        if (content.type === AR_CONTENT_TYPES.VIDEO && content.url) {
          assetPromises.push(this.preloadAsset(content.url, 'video'));
        } else if (content.type === AR_CONTENT_TYPES.MODEL && content.url) {
          assetPromises.push(this.preloadAsset(content.url, 'model'));
        } else if (content.type === AR_CONTENT_TYPES.IMAGE && content.url) {
          assetPromises.push(this.preloadAsset(content.url, 'image'));
        }
      }
    }
    
    // Wait for all assets to preload
    await Promise.allSettled(assetPromises);
    console.log('Assets preloaded for offline use');
  }
  
  /**
   * Preload a single asset
   * @param {string} url - Asset URL
   * @param {string} type - Asset type
   * @returns {Promise<void>}
   * @private
   */
  async preloadAsset(url, type) {
    try {
      // Check if already cached
      if (this.assetCache.has(url)) {
        return;
      }
      
      // Use offline manager to cache the resource
      if (offlineManager && typeof offlineManager.cacheResource === 'function') {
        await offlineManager.cacheResource(url, { type });
        this.assetCache.set(url, true);
        console.log(`Asset cached: ${url}`);
      } else {
        // Fallback: fetch and store in memory
        const response = await fetch(url);
        const blob = await response.blob();
        this.assetCache.set(url, blob);
        console.log(`Asset loaded in memory: ${url}`);
      }
    } catch (error) {
      console.error(`Failed to preload asset ${url}:`, error);
    }
  }

  /**
   * Initialize AR scene based on framework
   * @private
   */
  initARScene() {
    if (!this.container) {
      console.error('Container element not found');
      return;
    }
    
    // Clear container
    this.container.innerHTML = '';
    
    const framework = this.options.arFramework;
    
    switch (framework) {
      case 'ar.js':
        this.initARjs();
        break;
        
      case 'aframe':
        this.initAFrame();
        break;
        
      case 'model-viewer':
        this.initModelViewer();
        break;
        
      case 'webxr':
        this.initWebXR();
        break;
        
      default:
        console.error(`Unsupported AR framework: ${framework}`);
        break;
    }
  }

  /**
   * Initialize AR.js
   * @private
   */
  initARjs() {
    // Create A-Frame scene with AR.js
    const scene = document.createElement('a-scene');
    scene.setAttribute('embedded', '');
    scene.setAttribute('arjs', 'sourceType: webcam; debugUIEnabled: false; detectionMode: mono_and_matrix; matrixCodeType: 3x3;');
    scene.setAttribute('vr-mode-ui', 'enabled: false');
    
    // Add assets
    const assets = document.createElement('a-assets');
    
    // Add all video and model assets
    this.markers.forEach(marker => {
      if (marker.content) {
        const content = marker.content;
        
        if (content.type === AR_CONTENT_TYPES.VIDEO && content.url) {
          const video = document.createElement('video');
          video.id = `video-${marker.id}`;
          video.src = content.url;
          video.preload = 'auto';
          video.crossOrigin = 'anonymous';
          video.loop = false;
          video.muted = false;
          video.playsInline = true;
          
          // Store video element for later reference
          this.videoElements[marker.id] = video;
          
          // Add event listeners
          video.addEventListener('ended', () => {
            this.handleContentComplete(marker.id, content);
          });
          
          assets.appendChild(video);
        } else if (content.type === AR_CONTENT_TYPES.MODEL && content.url) {
          const item = document.createElement('a-asset-item');
          item.id = `model-${marker.id}`;
          item.setAttribute('src', content.url);
          assets.appendChild(item);
        }
      }
    });
    
    scene.appendChild(assets);
    
    // Add markers
    this.markers.forEach(marker => {
      let markerElement;
      
      // Create marker based on type
      switch (marker.type) {
        case AR_MARKER_TYPES.HIRO:
          markerElement = document.createElement('a-marker');
          markerElement.setAttribute('preset', 'hiro');
          break;
          
        case AR_MARKER_TYPES.KANJI:
          markerElement = document.createElement('a-marker');
          markerElement.setAttribute('preset', 'kanji');
          break;
          
        case AR_MARKER_TYPES.PATTERN:
          markerElement = document.createElement('a-marker');
          markerElement.setAttribute('type', 'pattern');
          markerElement.setAttribute('url', marker.patternUrl);
          break;
          
        case AR_MARKER_TYPES.BARCODE:
          markerElement = document.createElement('a-marker');
          markerElement.setAttribute('type', 'barcode');
          markerElement.setAttribute('value', marker.barcodeValue);
          break;
          
        case AR_MARKER_TYPES.CUSTOM:
          markerElement = document.createElement('a-marker');
          markerElement.setAttribute('type', 'pattern');
          markerElement.setAttribute('url', marker.patternUrl);
          break;
          
        default:
          console.error(`Unsupported marker type: ${marker.type}`);
          return;
      }
      
      // Set marker ID
      markerElement.id = `marker-${marker.id}`;
      
      // Add content based on type
      if (marker.content) {
        const content = marker.content;
        
        if (content.type === AR_CONTENT_TYPES.VIDEO) {
          // Create video plane
          const videoPlane = document.createElement('a-video');
          videoPlane.setAttribute('src', `#video-${marker.id}`);
          videoPlane.setAttribute('width', '4');
          videoPlane.setAttribute('height', '2.25');
          videoPlane.setAttribute('position', '0 0.1 0');
          videoPlane.setAttribute('rotation', '-90 0 0');
          
          markerElement.appendChild(videoPlane);
        } else if (content.type === AR_CONTENT_TYPES.MODEL) {
          // Create 3D model
          const model = document.createElement('a-entity');
          model.setAttribute('gltf-model', `#model-${marker.id}`);
          model.setAttribute('scale', `${content.scale || 1} ${content.scale || 1} ${content.scale || 1}`);
          model.setAttribute('position', '0 0 0');
          model.setAttribute('rotation', '-90 0 0');
          
          markerElement.appendChild(model);
        } else if (content.type === AR_CONTENT_TYPES.IMAGE) {
          // Create image plane
          const imagePlane = document.createElement('a-image');
          imagePlane.setAttribute('src', content.url);
          imagePlane.setAttribute('width', '4');
          imagePlane.setAttribute('height', '3');
          imagePlane.setAttribute('position', '0 0.1 0');
          imagePlane.setAttribute('rotation', '-90 0 0');
          
          markerElement.appendChild(imagePlane);
        } else if (content.type === AR_CONTENT_TYPES.TEXT) {
          // Create text
          const text = document.createElement('a-text');
          text.setAttribute('value', content.text);
          text.setAttribute('align', 'center');
          text.setAttribute('position', '0 0.1 0');
          text.setAttribute('rotation', '-90 0 0');
          text.setAttribute('scale', '2 2 2');
          text.setAttribute('color', content.color || '#000000');
          
          markerElement.appendChild(text);
        }
      }
      
      // Add event listeners
      markerElement.addEventListener('markerFound', () => {
        this.handleMarkerFound(marker.id);
      });
      
      markerElement.addEventListener('markerLost', () => {
        this.handleMarkerLost(marker.id);
      });
      
      // Store marker element
      this.arMarkers.push(markerElement);
      
      // Add to scene
      scene.appendChild(markerElement);
    });
    
    // Add camera
    const camera = document.createElement('a-entity');
    camera.setAttribute('camera', '');
    scene.appendChild(camera);
    
    // Add to container
    this.container.appendChild(scene);
    this.arScene = scene;
    this.arCamera = camera;
    
    console.log('AR.js initialized');
  }

  /**
   * Initialize A-Frame (without AR.js)
   * @private
   */
  initAFrame() {
    // Create A-Frame scene
    const scene = document.createElement('a-scene');
    scene.setAttribute('embedded', '');
    scene.setAttribute('vr-mode-ui', 'enabled: false');
    
    // Add assets
    const assets = document.createElement('a-assets');
    scene.appendChild(assets);
    
    // Add camera
    const camera = document.createElement('a-entity');
    camera.setAttribute('camera', '');
    camera.setAttribute('position', '0 1.6 3');
    camera.setAttribute('look-controls', '');
    
    scene.appendChild(camera);
    
    // Add to container
    this.container.appendChild(scene);
    this.arScene = scene;
    this.arCamera = camera;
    
    console.log('A-Frame initialized');
  }

  /**
   * Initialize model-viewer
   * @private
   */
  initModelViewer() {
    // Create model-viewer element
    const modelViewer = document.createElement('model-viewer');
    
    // Set main model (first asset with model type)
    const mainMarker = this.markers.find(marker => 
      marker.content && marker.content.type === AR_CONTENT_TYPES.MODEL
    );
    
    if (mainMarker && mainMarker.content) {
      modelViewer.src = mainMarker.content.url;
      modelViewer.alt = mainMarker.content.title || this.experience.title;
      modelViewer.ar = true;
      modelViewer.arModes = 'webxr scene-viewer quick-look';
      modelViewer.arScale = 'auto';
      modelViewer.arPlacement = 'floor';
      modelViewer.autoRotate = false;
      modelViewer.cameraControls = true;
      
      // Set up AR button
      const arButton = document.createElement('button');
      arButton.className = 'ar-button';
      arButton.innerHTML = 'View in AR';
      arButton.slot = 'ar-button';
      
      modelViewer.appendChild(arButton);
      
      // Set up event listeners
      modelViewer.addEventListener('ar-status', (event) => {
        this.arActive = event.detail.status === 'session-started';
        
        if (this.arActive) {
          this.startTime = Date.now();
          if (this.options.onARStart) {
            this.options.onARStart();
          }
        } else if (this.startTime) {
          const duration = (Date.now() - this.startTime) / 1000; // in seconds
          this.engagementData.totalDuration += duration;
          this.startTime = null;
          
          if (this.options.onAREnd) {
            this.options.onAREnd();
          }
        }
      });
      
      // Add to container
      this.container.appendChild(modelViewer);
      this.modelViewer = modelViewer;
      
      console.log('model-viewer initialized');
    } else {
      console.error('No model asset found for model-viewer');
      
      // Show error message
      const errorMessage = document.createElement('div');
      errorMessage.className = 'ar-error';
      errorMessage.textContent = 'No 3D model found for this experience';
      this.container.appendChild(errorMessage);
    }
  }

  /**
   * Initialize WebXR
   * @private
   */
  initWebXR() {
    // Check if WebXR is supported
    if (!navigator.xr) {
      console.error('WebXR not supported');
      
      // Show error message
      const errorMessage = document.createElement('div');
      errorMessage.className = 'ar-error';
      errorMessage.textContent = 'WebXR is not supported in your browser';
      this.container.appendChild(errorMessage);
      
      return;
    }
    
    // Create canvas
    const canvas = document.createElement('canvas');
    canvas.width = this.container.clientWidth;
    canvas.height = this.container.clientHeight;
    this.container.appendChild(canvas);
    
    // Create AR button
    const arButton = document.createElement('button');
    arButton.className = 'ar-button';
    arButton.textContent = 'Start AR';
    arButton.addEventListener('click', () => {
      this.startAR();
    });
    
    this.container.appendChild(arButton);
    
    console.log('WebXR initialized');
  }

  /**
   * Start AR experience
   * @returns {Promise<void>}
   */
  async startAR() {
    if (!this.loaded) {
      await this.load();
    }
    
    // Initialize AR scene if not already done
    if (!this.arScene && !this.modelViewer) {
      this.initARScene();
    }
    
    this.arActive = true;
    this.startTime = Date.now();
    
    const framework = this.options.arFramework;
    
    switch (framework) {
      case 'model-viewer':
        if (this.modelViewer) {
          this.modelViewer.activateAR();
        }
        break;
        
      case 'webxr':
        await this.startWebXRSession();
        break;
        
      case 'ar.js':
      case 'aframe':
        // AR.js and A-Frame start automatically when the scene is added to the DOM
        break;
        
      default:
        console.error(`Unsupported AR framework: ${framework}`);
        break;
    }
    
    if (this.options.onARStart) {
      this.options.onARStart();
    }
    
    console.log('AR experience started');
  }

  /**
   * Start WebXR session
   * @returns {Promise<void>}
   * @private
   */
  async startWebXRSession() {
    if (!navigator.xr) {
      throw new Error('WebXR not supported');
    }
    
    try {
      // Check if AR is supported
      const isSupported = await navigator.xr.isSessionSupported('immersive-ar');
      
      if (!isSupported) {
        throw new Error('AR not supported in this browser');
      }
      
      // Request AR session
      const session = await navigator.xr.requestSession('immersive-ar', {
        requiredFeatures: ['hit-test', 'dom-overlay'],
        domOverlay: { root: this.container }
      });
      
      this.webxrSession = session;
      
      // Set up session end event
      session.addEventListener('end', () => {
        this.webxrSession = null;
        this.arActive = false;
        
        if (this.startTime) {
          const duration = (Date.now() - this.startTime) / 1000; // in seconds
          this.engagementData.totalDuration += duration;
          this.startTime = null;
        }
        
        if (this.options.onAREnd) {
          this.options.onAREnd();
        }
      });
      
      console.log('WebXR session started');
    } catch (error) {
      console.error('Failed to start WebXR session:', error);
      throw error;
    }
  }

  /**
   * Stop AR experience
   */
  stopAR() {
    if (!this.arActive) return;
    
    // Record engagement time
    if (this.startTime) {
      const duration = (Date.now() - this.startTime) / 1000; // in seconds
      this.engagementData.totalDuration += duration;
      this.startTime = null;
    }
    
    const framework = this.options.arFramework;
    
    switch (framework) {
      case 'webxr':
        if (this.webxrSession) {
          this.webxrSession.end();
          this.webxrSession = null;
        }
        break;
        
      case 'model-viewer':
        // model-viewer doesn't have a direct way to stop AR
        // It's handled by the browser's AR system
        break;
        
      case 'ar.js':
      case 'aframe':
        // For AR.js, we can pause or hide the scene
        if (this.arScene) {
          this.arScene.pause();
        }
        break;
    }
    
    this.arActive = false;
    
    if (this.options.onAREnd) {
      this.options.onAREnd();
    }
    
    console.log('AR experience stopped');
  }

  /**
   * Handle marker found event
   * @param {string} markerId - ID of the marker
   * @private
   */
  handleMarkerFound(markerId) {
    console.log(`Marker found: ${markerId}`);
    
    // Find marker data
    const marker = this.markers.find(m => m.id === markerId);
    if (!marker) return;
    
    // Track engagement
    if (this.options.trackEngagement) {
      this.engagementData.interactions++;
      this.engagementData.lastInteraction = new Date().toISOString();
      
      // Track as new marker if not already scanned
      if (!this.scannedMarkers.has(markerId)) {
        this.scannedMarkers.add(markerId);
        this.engagementData.markersScanned++;
      }
    }
    
    // Play video content if available
    if (marker.content && marker.content.type === AR_CONTENT_TYPES.VIDEO) {
      const videoElement = this.videoElements[markerId];
      if (videoElement) {
        videoElement.play().catch(error => {
          console.error(`Failed to play video for marker ${markerId}:`, error);
        });
      }
    }
    
    // Call onMarkerFound callback
    if (this.options.onMarkerFound) {
      this.options.onMarkerFound(markerId, marker);
    }
    
    // Check if all required markers have been scanned
    this.checkCompletionStatus();
  }

  /**
   * Handle marker lost event
   * @param {string} markerId - ID of the marker
   * @private
   */
  handleMarkerLost(markerId) {
    console.log(`Marker lost: ${markerId}`);
    
    // Find marker data
    const marker = this.markers.find(m => m.id === markerId);
    if (!marker) return;
    
    // Pause video content if available
    if (marker.content && marker.content.type === AR_CONTENT_TYPES.VIDEO) {
      const videoElement = this.videoElements[markerId];
      if (videoElement) {
        videoElement.pause();
      }
    }
    
    // Call onMarkerLost callback
    if (this.options.onMarkerLost) {
      this.options.onMarkerLost(markerId, marker);
    }
  }

  /**
   * Handle content completion (e.g., video ended)
   * @param {string} markerId - ID of the marker
   * @param {Object} content - Content data
   * @private
   */
  handleContentComplete(markerId, content) {
    console.log(`Content completed for marker ${markerId}`);
    
    // Award points if specified
    if (content.points) {
      this.awardPoints(markerId, content.points);
    }
    
    // Call onContentComplete callback
    if (this.options.onContentComplete) {
      this.options.onContentComplete(markerId, content);
    }
    
    // Check if all required content has been viewed
    this.checkCompletionStatus();
  }

  /**
   * Award points for scanning a marker or completing content
   * @param {string} markerId - ID of the marker
   * @param {number} points - Number of points to award
   * @private
   */
  async awardPoints(markerId, points) {
    try {
      const user = getCurrentUser();
      
      if (!user) {
        console.warn('User not authenticated, points not awarded');
        return;
      }
      
      const pointsData = {
        userId: user.uid,
        markerId,
        experienceId: this.experienceId,
        points,
        timestamp: new Date().toISOString()
      };
      
      // Save to Firebase or queue if offline
      if (navigator.onLine) {
        try {
          await firebaseService.addDocument('participation_points', pointsData);
          console.log(`${points} points awarded for marker ${markerId}`);
          
          // Update leaderboard
          this.updateLeaderboard(points);
        } catch (error) {
          console.error('Error awarding points:', error);
          
          // Queue for later if Firebase error
          if (this.options.offlineSupport) {
            await syncQueue.addToQueue({
              endpoint: 'firestore',
              method: 'ADD',
              data: {
                collection: 'participation_points',
                docData: pointsData
              }
            });
          }
        }
      } else if (this.options.offlineSupport) {
        // Queue for later if offline
        await syncQueue.addToQueue({
          endpoint: 'firestore',
          method: 'ADD',
          data: {
            collection: 'participation_points',
            docData: pointsData
          }
        });
        
        console.log(`${points} points queued for offline sync`);
      }
    } catch (error) {
      console.error('Failed to award points:', error);
    }
  }

  /**
   * Update leaderboard with earned points
   * @param {number} points - Points to add
   * @private
   */
  async updateLeaderboard(points) {
    try {
      const user = getCurrentUser();
      
      if (!user) return;
      
      // Get current user's leaderboard entry
      const query = {
        where: [
          {
            field: 'userId',
            operator: '==',
            value: user.uid
          },
          {
            field: 'boardId',
            operator: '==',
            value: 'coaching_booth_leaderboard'
          }
        ]
      };
      
      const entries = await firebaseService.getCollection('leaderboard_entries', query);
      
      if (entries && entries.length > 0) {
        // Update existing entry
        const entry = entries[0];
        const updatedScore = (entry.score || 0) + points;
        
        await firebaseService.updateDocument('leaderboard_entries', entry.id, {
          score: updatedScore,
          updatedAt: new Date().toISOString()
        });
        
        console.log(`Leaderboard updated: ${updatedScore} points`);
      } else {
        // Create new entry
        const leaderboardEntry = {
          userId: user.uid,
          displayName: user.displayName || 'Anonymous Player',
          photoURL: user.photoURL || null,
          boardId: 'coaching_booth_leaderboard',
          score: points,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        await firebaseService.addDocument('leaderboard_entries', leaderboardEntry);
        console.log(`New leaderboard entry created: ${points} points`);
      }
    } catch (error) {
      console.error('Failed to update leaderboard:', error);
    }
  }

  /**
   * Check if the AR experience is complete
   * @private
   */
  checkCompletionStatus() {
    if (!this.experience || !this.experience.settings) return;
    
    const settings = this.experience.settings;
    const requiredMarkers = settings.requiredMarkers || this.markers.length;
    
    // Check if enough markers have been scanned
    if (this.scannedMarkers.size >= requiredMarkers) {
      console.log('All required markers scanned!');
      
      // Launch quiz if specified
      if (settings.quizAfterCompletion && settings.quizId) {
        this.launchQuiz(settings.quizId);
      }
    }
  }

  /**
   * Launch quiz after completing AR experience
   * @param {string} quizId - ID of the quiz to launch
   * @private
   */
  launchQuiz(quizId) {
    // Dispatch event to launch quiz
    const launchEvent = new CustomEvent('launch-quiz', {
      detail: {
        quizId,
        source: 'ar-experience',
        experienceId: this.experienceId
      },
      bubbles: true
    });
    
    document.dispatchEvent(launchEvent);
    console.log(`Quiz launched: ${quizId}`);
  }

  /**
   * Sync engagement data to server
   * @private
   */
  async syncEngagementData() {
    if (!this.options.trackEngagement) return;
    
    try {
      const user = getCurrentUser();
      
      if (!user) return;
      
      const engagementData = {
        userId: user.uid,
        experienceId: this.experienceId,
        totalDuration: this.engagementData.totalDuration,
        interactions: this.engagementData.interactions,
        markersScanned: this.engagementData.markersScanned,
        scannedMarkers: Array.from(this.scannedMarkers),
        lastInteraction: this.engagementData.lastInteraction,
        timestamp: new Date().toISOString()
      };
      
      // Save to Firebase
      await firebaseService.addDocument('ar_engagement', engagementData);
      console.log('AR engagement data synced');
    } catch (error) {
      console.error('Failed to sync engagement data:', error);
    }
  }
}

export default ARExperience;