# Project Documentation

This document provides instructions for setting up, configuring, and integrating the project features into a Webflow site.

## Getting Started

Follow these steps to get the project running.

### Memberstack Configuration

1.  **Navigate to Authentication Configuration:**
    Open the authentication configuration files where Memberstack is initialized.

2.  **Update Memberstack Credentials:**
    Replace the placeholder Memberstack public key with your actual project credentials.

    ```javascript
    // Example location where <PERSON><PERSON><PERSON> is configured
    const memberstackPublicKey = "YOUR_MEMBERSTACK_PUBLIC_KEY";
    ```

3.  **Initialize Memberstack:**
    Ensure the Memberstack configuration is properly initialized in your authentication flow.

### Firebase Configuration

1.  **Navigate to Firebase Configuration:**
    Open the file [`core/firebase/firebase-config.js`](core/firebase/firebase-config.js).

2.  **Update Firebase Credentials:**
    Replace the placeholder Firebase configuration with your actual project credentials.

    ```javascript
    // core/firebase/firebase-config.js
    const firebaseConfig = {
      apiKey: "YOUR_API_KEY",
      authDomain: "YOUR_AUTH_DOMAIN",
      projectId: "YOUR_PROJECT_ID",
      storageBucket: "YOUR_STORAGE_BUCKET",
      messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
      appId: "YOUR_APP_ID"
    };
    ```

3.  **Initialize Firebase:**
    The configuration is automatically used by [`core/firebase/firebase-service.js`](core/firebase/firebase-service.js) to initialize and export Firebase services.

### Authentication Integration

This project uses Memberstack exclusively for authentication, with Firebase for data storage:

1.  **Authentication Flow:**
    - Memberstack is used as the exclusive authentication provider
    - Firebase is used only for real-time data and storage (not for authentication)
    - User identity is managed through Memberstack's membership system

2.  **Login Process:**
    - When a user logs in, the system authenticates with Memberstack
    - After successful authentication, the user's Memberstack ID is used to access their data in Firebase
    - No separate Firebase Authentication is required

3.  **Registration Process:**
    - New users are registered only with Memberstack
    - User profile data is stored in Firebase Firestore using the Memberstack ID as the document ID

## Webflow Integration

To integrate any module into your Webflow site, you need to add its corresponding `_webflow-embed.js` script to your site's custom code settings.

### Steps:

1.  **Go to your Webflow Dashboard.**
2.  **Select your project and go to "Project Settings".**
3.  **Click on the "Custom Code" tab.**
4.  **Copy the content** of the desired `_webflow-embed.js` file.
5.  **Paste the script** into the "Footer Code" section (`</body>` tag) and save the changes.

For example, to add the **Augmented Reality (AR)** feature, you would copy the contents of [`components/ar/ar_webflow-embed.js`](components/ar/ar_webflow-embed.js) and paste it into the custom code section.

Repeat this process for each feature you want to include.

## Project Structure Summary

### **1. Admin**
-   **Module: Dashboard**
    -   **Purpose:** Provides an administrative interface for managing the application.
    -   **Main Logic:** [`admin/dashboard/dashboard.js`](admin/dashboard/dashboard.js)
    -   **Webflow Embed:** [`admin/dashboard/dashboard_webflow-embed.js`](admin/dashboard/dashboard_webflow-embed.js)

### **2. Components**
-   **Module: Augmented Reality (AR)**
    -   **Purpose:** Delivers an interactive AR experience to users.
    -   **Main Logic:** [`components/ar/ar-experience.js`](components/ar/ar-experience.js)
    -   **Webflow Embed:** [`components/ar/ar_webflow-embed.js`](components/ar/ar_webflow-embed.js)
-   **Module: Leaderboard**
    -   **Purpose:** Displays user rankings and scores.
    -   **Main Logic:** [`components/leaderboard/leaderboard.js`](components/leaderboard/leaderboard.js)
    -   **Webflow Embed:** [`components/leaderboard/leaderboard_webflow-embed.js`](components/leaderboard/leaderboard_webflow-embed.js)
-   **Module: QR Scanner**
    -   **Purpose:** Enables users to scan QR codes for interactive experiences.
    -   **Main Logic:** [`components/qr-scanner/qr-scanner.js`](components/qr-scanner/qr-scanner.js)
    -   **Webflow Embed:** [`components/qr-scanner/qr-scanner_webflow-embed.js`](components/qr-scanner/qr-scanner_webflow-embed.js)
-   **Module: Quiz**
    -   **Purpose:** Provides an engaging quiz or trivia experience for users.
    -   **Main Logic:** [`components/quiz/quiz-engine.js`](components/quiz/quiz-engine.js)
    -   **Webflow Embed:** [`components/quiz/quiz_webflow-embed.js`](components/quiz/quiz_webflow-embed.js)

### **3. Core**
-   **Module: Authentication**
    -   **Purpose:** Manages user authentication using Memberstack.
    -   **Main Logic:** [`core/auth/auth.js`](core/auth/auth.js)
    -   **Webflow Embed:** [`core/auth/auth_webflow-embed.js`](core/auth/auth_webflow-embed.js)
    -   **Features:** Memberstack authentication, offline support, user data management
-   **Module: Firebase**
    -   **Purpose:** Provides real-time data storage and backend services.
    -   **Configuration:** [`core/firebase/firebase-config.js`](core/firebase/firebase-config.js)
    -   **Service Logic:** [`core/firebase/firebase-service.js`](core/firebase/firebase-service.js)
-   **Module: Memberstack**
    -   **Purpose:** Exclusive authentication provider with membership management.
    -   **Integration:** Connected to Firebase for data persistence using Memberstack IDs.
    -   **Benefits:** User management, subscription handling, member-only content
-   **Module: Offline Support**
    -   **Purpose:** Enables offline functionality.
    -   **Service Worker:** [`core/offline/service-worker.js`](core/offline/service-worker.js)
    -   **Offline Manager:** [`core/offline/offline-manager.js`](core/offline/offline-manager.js)
    -   **Sync Queue:** [`core/offline/sync-queue.js`](core/offline/sync-queue.js)
    -   **Features:** Caches user data locally, provides offline authentication, syncs data when connection is restored
-   **Module: State Management**
    -   **Purpose:** Manages global application state.
    -   **Manager:** [`core/state/state-manager.js`](core/state/state-manager.js)

### **4. Pages**
-   **Module: Community Impact**
    -   **Purpose:** Displays content related to community initiatives.
    -   **Main Logic:** [`pages/community-impact/community-impact.js`](pages/community-impact/community-impact.js)
    -   **Webflow Embed:** [`pages/community-impact/community-impact_webflow-embed.js`](pages/community-impact/community-impact_webflow-embed.js)
-   **Module: Onboarding**
    -   **Purpose:** Guides new users through setup.
    -   **Main Logic:** [`pages/onboarding/onboarding.js`](pages/onboarding/onboarding.js)
    -   **Webflow Embed:** [`pages/onboarding/onboarding_webflow-embed.js`](pages/onboarding/onboarding_webflow-embed.js)

### **5. Utils**
-   Helper functions for APIs, UI, validation, etc.

## Offline Support

The application includes robust offline functionality:

1. **Cached Authentication:**
   - User credentials are securely cached for offline access
   - Authentication can proceed even without an internet connection

2. **Data Synchronization:**
   - Changes made offline are queued for synchronization
   - Data is automatically synchronized when connection is restored

3. **Service Worker:**
   - Manages caching of application assets and data
   - Enables the application to load and function without network access

## Troubleshooting

### Authentication Issues

1. **Memberstack Configuration:**
   - If users cannot log in, ensure the Memberstack public key is correctly configured
   - Verify that the Memberstack initialization is properly set up in the authentication flow

2. **Offline Authentication:**
   - If offline authentication fails, ensure the user has successfully logged in at least once while online
   - Verify that the offline cache hasn't been cleared or corrupted

3. **Configuration Issues:**
   - Double-check that the Memberstack public key and Firebase configuration values have been replaced with actual credentials
   - Ensure all required configuration values are properly set

### Performance Optimization

For optimal performance, especially on slower connections:

1. **Lazy Loading:**
   - The authentication providers are loaded only when needed
   - This reduces initial load time and improves performance

2. **Background Synchronization:**
   - Data synchronization happens in the background
   - This prevents blocking the user interface during sync operations

## Best Practices for Memberstack and Firebase Integration

When working with the Memberstack authentication and Firebase data storage, follow these best practices:

1. **Single Source of Truth:**
   - Memberstack is the exclusive source of authentication truth
   - User identity and membership status should always be determined by Memberstack

2. **Unified User Management:**
   - Always create or update users through the authentication service
   - Use the Memberstack ID as the unique identifier for user data in Firebase

3. **Secure Token Management:**
   - Never expose Memberstack tokens in client-side code
   - Implement proper token refresh and validation mechanisms

4. **Graceful Degradation:**
   - Design features to work with limited functionality when offline
   - Prioritize core user experience over complete feature parity

5. **Testing Authentication Flows:**
   - Test authentication scenarios including Memberstack service unavailability
   - Verify that offline authentication works correctly