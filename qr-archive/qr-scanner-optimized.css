/* 
 * QR Scanner Optimized CSS
 * Specifically optimized for Surface Go 4 (10.5", 1920x1280, 3:2 aspect ratio)
 * and iPad 11" (2388x1668, ~1.43:1 aspect ratio) in landscape orientation
 */

/* Base Container Styles */
.qr-scanner-container {
  position: relative;
  width: 100%;
  max-width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
  overflow: hidden;
  display: flex;
  flex-direction: row; /* Split-screen layout */
}

/* Split-screen Layout: 60% camera, 40% controls */
.qr-scanner-camera-section {
  width: 60%;
  height: 100%;
  position: relative;
  background-color: #000;
  overflow: hidden;
}

.qr-scanner-controls-section {
  width: 40%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #f8f9fa;
  overflow-y: auto;
}

/* Video Element */
.qr-scanner-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #000;
}

/* Canvas Element */
.qr-scanner-canvas {
  display: none; /* Hidden by default */
  position: absolute;
  top: 0;
  left: 0;
}

/* Enhanced Scanning Target with Corner Markers */
.qr-scanner-target {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 250px;
  height: 250px;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* Corner Markers */
.qr-scanner-target::before,
.qr-scanner-target::after,
.qr-scanner-target-corner-tl,
.qr-scanner-target-corner-tr,
.qr-scanner-target-corner-bl,
.qr-scanner-target-corner-br {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  border-color: #28a745;
  border-style: solid;
  border-width: 0;
}

.qr-scanner-target-corner-tl {
  top: 0;
  left: 0;
  border-top-width: 4px;
  border-left-width: 4px;
}

.qr-scanner-target-corner-tr {
  top: 0;
  right: 0;
  border-top-width: 4px;
  border-right-width: 4px;
}

.qr-scanner-target-corner-bl {
  bottom: 0;
  left: 0;
  border-bottom-width: 4px;
  border-left-width: 4px;
}

.qr-scanner-target-corner-br {
  bottom: 0;
  right: 0;
  border-bottom-width: 4px;
  border-right-width: 4px;
}

/* Scanning Animation */
@keyframes scanAnimation {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

.qr-scanner-scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(40, 167, 69, 0.8);
  animation: scanAnimation 2s infinite linear;
  box-shadow: 0 0 8px rgba(40, 167, 69, 0.8);
  transform-origin: center;
}

/* Button Styling */
.qr-scanner-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  margin-top: 15px;
}

.qr-scanner-start-button,
.qr-scanner-stop-button,
.qr-scanner-switch-camera,
.qr-scanner-toggle-flash {
  display: inline-block;
  padding: 14px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
  text-align: center;
}

.qr-scanner-start-button:hover,
.qr-scanner-stop-button:hover,
.qr-scanner-switch-camera:hover,
.qr-scanner-toggle-flash:hover {
  background-color: #0069d9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.qr-scanner-start-button:active,
.qr-scanner-stop-button:active,
.qr-scanner-switch-camera:active,
.qr-scanner-toggle-flash:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.qr-scanner-stop-button {
  background-color: #dc3545;
  display: none; /* Hidden by default */
}

.qr-scanner-stop-button:hover {
  background-color: #c82333;
}

/* Result Display */
.qr-scanner-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #d4edda;
  color: #155724;
  border-radius: 8px;
  display: none; /* Hidden by default */
  word-break: break-word;
  font-size: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Error Display */
.qr-scanner-error {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 8px;
  display: none; /* Hidden by default */
  font-size: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Status Display */
.qr-scanner-status {
  margin-top: 10px;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  display: none; /* Hidden by default */
  font-size: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-info {
  background-color: #cce5ff;
  color: #004085;
}

.status-success {
  background-color: #d4edda;
  color: #155724;
}

.status-warning {
  background-color: #fff3cd;
  color: #856404;
}

.status-error {
  background-color: #f8d7da;
  color: #721c24;
}

/* Offline Indicator */
.qr-scanner-offline-indicator {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 8px 12px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 8px;
  font-size: 14px;
  display: none; /* Hidden by default */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Animation for successful scan */
@keyframes scanSuccess {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); opacity: 0.7; }
}

.scan-success {
  animation: scanSuccess 0.8s ease;
}

/* Visual feedback for successful scans */
.qr-scanner-success-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(40, 167, 69, 0.3);
  display: none;
  pointer-events: none;
  z-index: 5;
}

.qr-scanner-success-overlay.active {
  display: block;
  animation: fadeOut 1s forwards;
}

@keyframes fadeOut {
  0% { opacity: 1; }
  100% { opacity: 0; }
}

/* Flash on state */
.qr-scanner-toggle-flash.flash-on {
  background-color: #ffc107;
  color: #212529;
}

/* Device-specific media queries */

/* Surface Go 4 (10.5", 1920x1280, 3:2 aspect ratio) in landscape orientation */
@media screen and (min-width: 1280px) and (max-width: 1920px) and (min-height: 800px) and (max-height: 1280px) and (aspect-ratio: 3/2), 
       screen and (min-width: 1280px) and (max-width: 1920px) and (min-height: 800px) and (max-height: 1280px) and (orientation: landscape) {
  .qr-scanner-target {
    width: 280px;
    height: 280px;
  }
  
  .qr-scanner-controls-section {
    padding: 24px;
  }
  
  .qr-scanner-buttons {
    gap: 16px;
  }
  
  .qr-scanner-start-button,
  .qr-scanner-stop-button,
  .qr-scanner-switch-camera,
  .qr-scanner-toggle-flash {
    padding: 16px 24px;
    font-size: 18px;
  }
}

/* iPad 11" (2388x1668, ~1.43:1 aspect ratio) in landscape orientation */
@media screen and (min-width: 1668px) and (max-width: 2388px) and (min-height: 1100px) and (max-height: 1668px) and (orientation: landscape) {
  .qr-scanner-target {
    width: 320px;
    height: 320px;
  }
  
  .qr-scanner-target-corner-tl,
  .qr-scanner-target-corner-tr,
  .qr-scanner-target-corner-bl,
  .qr-scanner-target-corner-br {
    width: 40px;
    height: 40px;
  }
  
  .qr-scanner-controls-section {
    padding: 30px;
  }
  
  .qr-scanner-buttons {
    gap: 20px;
  }
  
  .qr-scanner-start-button,
  .qr-scanner-stop-button,
  .qr-scanner-switch-camera,
  .qr-scanner-toggle-flash {
    padding: 18px 28px;
    font-size: 20px;
    border-radius: 10px;
  }
  
  .qr-scanner-result,
  .qr-scanner-error,
  .qr-scanner-status {
    font-size: 18px;
    padding: 18px;
  }
}

/* Fallback for other devices in landscape orientation */
@media (orientation: landscape) {
  .qr-scanner-container {
    flex-direction: row;
  }
  
  .qr-scanner-camera-section {
    width: 60%;
  }
  
  .qr-scanner-controls-section {
    width: 40%;
  }
}

/* Portrait orientation fallback (revert to stacked layout) */
@media (orientation: portrait) {
  .qr-scanner-container {
    flex-direction: column;
    height: auto;
  }
  
  .qr-scanner-camera-section {
    width: 100%;
    height: 60vh;
  }
  
  .qr-scanner-controls-section {
    width: 100%;
    height: auto;
  }
}