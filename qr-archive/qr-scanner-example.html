<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Optimized QR Scanner for Surface Go 4 and iPad 11"</title>
  
  <!-- Include the HTML5 QR Code library -->
  <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
  
  <!-- Include the optimized QR Scanner styles -->
  <link rel="stylesheet" href="qr-scanner-optimized.css">
  
  <style>
    /* Additional page styles */
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f0f2f5;
      height: 100vh;
      width: 100vw;
      overflow: hidden;
    }
    
    .header {
      background-color: #007bff;
      color: white;
      padding: 10px 20px;
      text-align: center;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .header h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
    
    .device-info {
      font-size: 14px;
      background-color: rgba(0, 0, 0, 0.1);
      padding: 4px 8px;
      border-radius: 4px;
    }
    
    .main-content {
      height: 100vh;
      padding-top: 50px;
      box-sizing: border-box;
    }
    
    .scan-history {
      margin-top: 15px;
      padding: 15px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      max-height: 200px;
      overflow-y: auto;
    }
    
    .scan-history h3 {
      margin-top: 0;
      color: #333;
      font-size: 16px;
      font-weight: 500;
    }
    
    .scan-history-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .scan-history-list li {
      padding: 8px;
      border-bottom: 1px solid #eee;
      font-size: 14px;
    }
    
    .scan-history-list li:last-child {
      border-bottom: none;
    }
    
    .clear-history {
      margin-top: 10px;
      padding: 8px 15px;
      background-color: #dc3545;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .clear-history:hover {
      background-color: #c82333;
    }
    
    .booth-selector {
      margin-bottom: 15px;
    }
    
    .booth-selector label {
      margin-right: 10px;
      font-weight: 500;
      font-size: 14px;
    }
    
    .booth-selector select {
      padding: 8px;
      border-radius: 5px;
      border: 1px solid #ccc;
      font-size: 14px;
      background-color: white;
    }
    
    /* Battery indicator */
    .battery-indicator {
      display: flex;
      align-items: center;
      margin-top: 10px;
      font-size: 14px;
    }
    
    .battery-icon {
      width: 30px;
      height: 15px;
      border: 2px solid #333;
      border-radius: 3px;
      position: relative;
      margin-right: 8px;
    }
    
    .battery-icon::after {
      content: '';
      position: absolute;
      top: 3px;
      right: -5px;
      width: 3px;
      height: 9px;
      background-color: #333;
      border-radius: 0 2px 2px 0;
    }
    
    .battery-level {
      position: absolute;
      top: 2px;
      left: 2px;
      bottom: 2px;
      background-color: #4CAF50;
      border-radius: 1px;
    }
    
    .battery-low .battery-level {
      background-color: #f44336;
    }
  </style>
</head>
<body>
  <header class="header">
    <h1>Optimized QR Scanner</h1>
    <div class="device-info" id="device-info">Detecting device...</div>
  </header>
  
  <div class="main-content">
    <!-- QR Scanner Container with split-screen layout -->
    <div class="qr-scanner-container" data-scan-mode="continuous">
      <!-- Camera Section (60%) -->
      <div class="qr-scanner-camera-section">
        <!-- Video element for camera feed -->
        <div class="qr-scanner-video" id="qr-video"></div>
        
        <!-- Enhanced scanning target with corner markers -->
        <div class="qr-scanner-target" id="qr-target">
          <div class="qr-scanner-target-corner-tl"></div>
          <div class="qr-scanner-target-corner-tr"></div>
          <div class="qr-scanner-target-corner-bl"></div>
          <div class="qr-scanner-target-corner-br"></div>
        </div>
        
        <!-- Success overlay for visual feedback -->
        <div class="qr-scanner-success-overlay" id="qr-success-overlay"></div>
        
        <!-- Offline indicator -->
        <div class="qr-scanner-offline-indicator">Offline Mode</div>
      </div>
      
      <!-- Controls Section (40%) -->
      <div class="qr-scanner-controls-section">
        <!-- Booth Type Selector -->
        <div class="booth-selector">
          <label for="booth-type">Booth Type:</label>
          <select id="booth-type">
            <option value="referee">Referee Booth</option>
            <option value="media">Media Booth</option>
            <option value="coaching">Coaching Booth</option>
          </select>
        </div>
        
        <!-- Battery indicator -->
        <div class="battery-indicator" id="battery-indicator">
          <div class="battery-icon">
            <div class="battery-level" id="battery-level" style="width: 70%;"></div>
          </div>
          <span id="battery-percentage">70%</span>
        </div>
        
        <!-- Buttons -->
        <div class="qr-scanner-buttons">
          <button class="qr-scanner-start-button">Start Scanning</button>
          <button class="qr-scanner-stop-button">Stop Scanning</button>
          <button class="qr-scanner-switch-camera">Switch Camera</button>
          <button class="qr-scanner-toggle-flash">Toggle Flash</button>
        </div>
        
        <!-- Result display -->
        <div class="qr-scanner-result"></div>
        
        <!-- Error display -->
        <div class="qr-scanner-error"></div>
        
        <!-- Status display -->
        <div class="qr-scanner-status"></div>
        
        <!-- Scan History -->
        <div class="scan-history">
          <h3>Scan History</h3>
          <ul class="scan-history-list" id="scan-history-list">
            <!-- Scan history items will be added here -->
            <li>No scans yet</li>
          </ul>
          <button class="clear-history" id="clear-history">Clear History</button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Include the optimized QR Scanner script -->
  <script type="module">
    import QRScannerOptimized from './qr-scanner-optimized.js';
    
    document.addEventListener('DOMContentLoaded', function() {
      const boothTypeSelect = document.getElementById('booth-type');
      const qrScannerContainer = document.querySelector('.qr-scanner-container');
      const scanHistoryList = document.getElementById('scan-history-list');
      const clearHistoryButton = document.getElementById('clear-history');
      const deviceInfoElement = document.getElementById('device-info');
      const batteryLevelElement = document.getElementById('battery-level');
      const batteryPercentageElement = document.getElementById('battery-percentage');
      const batteryIndicatorElement = document.getElementById('battery-indicator');
      
      // Initialize QR Scanner
      const qrScanner = new QRScannerOptimized({
        videoElement: document.getElementById('qr-video'),
        targetElement: document.getElementById('qr-target'),
        successOverlayElement: document.getElementById('qr-success-overlay'),
        startOnInit: false,
        scanMode: 'continuous',
        highlightScanRegion: true,
        highlightCodeOutline: true,
        optimizeForDevice: true,
        onScan: handleScan,
        onError: handleError,
        onStart: handleStart,
        onStop: handleStop
      });
      
      // Update device info
      const deviceType = qrScanner.getDeviceType();
      if (deviceType === 'surface-go') {
        deviceInfoElement.textContent = 'Surface Go 4';
      } else if (deviceType === 'ipad-11') {
        deviceInfoElement.textContent = 'iPad 11"';
      } else {
        deviceInfoElement.textContent = 'Other Device';
      }
      
      // Update booth type when selection changes
      boothTypeSelect.addEventListener('change', function() {
        qrScannerContainer.setAttribute('data-booth-type', this.value);
        console.log(`Booth type changed to: ${this.value}`);
      });
      
      // Set up battery monitoring
      if ('getBattery' in navigator) {
        navigator.getBattery().then(battery => {
          updateBatteryInfo(battery);
          
          battery.addEventListener('levelchange', () => {
            updateBatteryInfo(battery);
          });
        });
      } else {
        batteryIndicatorElement.style.display = 'none';
      }
      
      // Update battery display
      function updateBatteryInfo(battery) {
        const level = Math.floor(battery.level * 100);
        batteryLevelElement.style.width = `${level}%`;
        batteryPercentageElement.textContent = `${level}%`;
        
        if (level <= 20) {
          batteryIndicatorElement.classList.add('battery-low');
        } else {
          batteryIndicatorElement.classList.remove('battery-low');
        }
      }
      
      // Handle QR code scans
      function handleScan(result) {
        console.log('QR code scanned:', result);
        
        // Update scan history
        updateScanHistory(result);
      }
      
      // Handle errors
      function handleError(error) {
        console.error('QR scanner error:', error);
      }
      
      // Handle scanner start
      function handleStart() {
        console.log('QR scanner started');
        document.querySelector('.qr-scanner-start-button').style.display = 'none';
        document.querySelector('.qr-scanner-stop-button').style.display = 'block';
      }
      
      // Handle scanner stop
      function handleStop() {
        console.log('QR scanner stopped');
        document.querySelector('.qr-scanner-start-button').style.display = 'block';
        document.querySelector('.qr-scanner-stop-button').style.display = 'none';
      }
      
      // Set up button event listeners
      document.querySelector('.qr-scanner-start-button').addEventListener('click', () => {
        qrScanner.start();
      });
      
      document.querySelector('.qr-scanner-stop-button').addEventListener('click', () => {
        qrScanner.stop();
      });
      
      document.querySelector('.qr-scanner-switch-camera').addEventListener('click', async () => {
        try {
          const newMode = await qrScanner.switchCamera();
          console.log(`Camera switched to ${newMode} mode`);
        } catch (error) {
          console.error('Failed to switch camera:', error);
        }
      });
      
      document.querySelector('.qr-scanner-toggle-flash').addEventListener('click', async () => {
        try {
          const isOn = await qrScanner.toggleFlashlight();
          const flashButton = document.querySelector('.qr-scanner-toggle-flash');
          
          if (isOn) {
            flashButton.classList.add('flash-on');
          } else {
            flashButton.classList.remove('flash-on');
          }
          
          console.log(`Flashlight turned ${isOn ? 'on' : 'off'}`);
        } catch (error) {
          console.error('Failed to toggle flashlight:', error);
        }
      });
      
      // Clear scan history
      clearHistoryButton.addEventListener('click', function() {
        clearScanHistory();
      });
      
      // Update scan history display
      function updateScanHistory(result) {
        // Get existing history from local storage
        const key = 'atlas25_scan_history';
        let history = JSON.parse(localStorage.getItem(key) || '[]');
        
        // Add new scan
        const scan = {
          ...result,
          createdAt: new Date().toISOString()
        };
        
        history.push(scan);
        
        // Save to local storage
        localStorage.setItem(key, JSON.stringify(history));
        
        // Update UI
        if (history.length > 0) {
          scanHistoryList.innerHTML = '';
          
          // Display most recent 10 scans
          const recentScans = history.slice(-10).reverse();
          
          recentScans.forEach(scan => {
            const li = document.createElement('li');
            const timestamp = new Date(scan.createdAt).toLocaleTimeString();
            
            let scanInfo = '';
            if (scan.type === 'score') {
              scanInfo = `Score: ${scan.data.score || 'N/A'}`;
            } else if (scan.type === 'quiz') {
              scanInfo = `Quiz: ${scan.data.title || 'Unknown Quiz'}`;
            } else if (scan.type === 'ar') {
              scanInfo = `AR: ${scan.data.name || 'Unknown AR Experience'}`;
            } else {
              scanInfo = `QR: ${typeof scan.data === 'object' ? JSON.stringify(scan.data) : scan.data}`;
            }
            
            li.textContent = `[${timestamp}] ${scanInfo}`;
            scanHistoryList.appendChild(li);
          });
        } else {
          scanHistoryList.innerHTML = '<li>No scans yet</li>';
        }
      }
      
      // Clear scan history
      function clearScanHistory() {
        localStorage.removeItem('atlas25_scan_history');
        scanHistoryList.innerHTML = '<li>No scans yet</li>';
        console.log('Scan history cleared');
      }
      
      // Initialize scan history on page load
      updateScanHistory();
      
      // Clean up resources when page is unloaded
      window.addEventListener('beforeunload', () => {
        qrScanner.destroy();
      });
    });
  </script>
</body>
</html>