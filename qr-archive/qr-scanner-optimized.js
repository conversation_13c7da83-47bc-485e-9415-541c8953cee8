/**
 * @file qr-scanner-optimized.js
 * @description Optimized QR scanner for Surface Go 4 and iPad 11" devices
 * Specifically optimized for:
 * - Surface Go 4 (10.5", 1920x1280, 3:2 aspect ratio)
 * - iPad 11" (2388x1668, ~1.43:1 aspect ratio)
 * - Landscape orientation
 */

/**
 * QR code format types
 * @enum {string}
 */
const QR_CODE_TYPES = {
  SCORE: 'score',
  QUIZ: 'quiz',
  AR: 'ar',
  UNKNOWN: 'unknown'
};

/**
 * Scan modes for QR scanner
 * @enum {string}
 */
const SCAN_MODES = {
  SINGLE: 'single',
  CONTINUOUS: 'continuous'
};

/**
 * Device types for optimization
 * @enum {string}
 */
const DEVICE_TYPES = {
  SURFACE_GO: 'surface-go',
  IPAD_11: 'ipad-11',
  OTHER: 'other'
};

/**
 * QRScannerOptimized class for scanning QR codes with device-specific optimizations
 */
class QRScannerOptimized {
  /**
   * Create an optimized QR scanner
   * @param {Object} options - Configuration options
   * @param {HTMLElement|string} options.videoElement - Video element or selector
   * @param {HTMLElement|string} [options.canvasElement] - Canvas element or selector
   * @param {HTMLElement|string} [options.targetElement] - Target overlay element or selector
   * @param {HTMLElement|string} [options.successOverlayElement] - Success overlay element or selector
   * @param {Function} [options.onScan] - Callback function when QR code is scanned
   * @param {Function} [options.onError] - Callback function when error occurs
   * @param {Function} [options.onStart] - Callback function when scanner starts
   * @param {Function} [options.onStop] - Callback function when scanner stops
   * @param {boolean} [options.startOnInit=false] - Whether to start scanning on initialization
   * @param {string} [options.scanMode='continuous'] - Scan mode (single or continuous)
   * @param {boolean} [options.highlightScanRegion=true] - Whether to highlight the scan region
   * @param {boolean} [options.highlightCodeOutline=true] - Whether to highlight the QR code outline
   * @param {boolean} [options.optimizeForDevice=true] - Whether to apply device-specific optimizations
   */
  constructor(options) {
    this.options = {
      startOnInit: false,
      scanMode: SCAN_MODES.CONTINUOUS,
      highlightScanRegion: true,
      highlightCodeOutline: true,
      optimizeForDevice: true,
      ...options
    };
    
    // Find elements
    this.videoElement = typeof options.videoElement === 'string' 
      ? document.querySelector(options.videoElement) 
      : options.videoElement;
      
    this.canvasElement = options.canvasElement 
      ? (typeof options.canvasElement === 'string' 
        ? document.querySelector(options.canvasElement) 
        : options.canvasElement)
      : document.createElement('canvas');
      
    this.targetElement = options.targetElement
      ? (typeof options.targetElement === 'string'
        ? document.querySelector(options.targetElement)
        : options.targetElement)
      : null;
      
    this.successOverlayElement = options.successOverlayElement
      ? (typeof options.successOverlayElement === 'string'
        ? document.querySelector(options.successOverlayElement)
        : options.successOverlayElement)
      : null;
      
    this.canvasContext = this.canvasElement.getContext('2d');
    this.scanning = false;
    this.lastResult = null;
    this.stream = null;
    this.html5QrCode = null;
    this.scanHistory = [];
    this.offlineQueue = [];
    this.isOnline = navigator.onLine;
    this.deviceType = this.detectDeviceType();
    this.batteryManager = null;
    this.batteryLow = false;
    
    // Initialize QR code library
    this.initQRCodeLibrary();
    
    // Set up network listeners
    this.setupNetworkListeners();
    
    // Set up battery monitoring
    this.setupBatteryMonitoring();
    
    // Create scan line animation if target element exists
    if (this.targetElement) {
      this.createScanLineAnimation();
    }
    
    if (this.options.startOnInit) {
      this.start();
    }
  }

  /**
   * Initialize QR code library
   * @private
   */
  initQRCodeLibrary() {
    try {
      // Check if html5-qrcode is available
      if (typeof Html5Qrcode === 'undefined') {
        console.warn('Html5Qrcode library not found. Make sure to include the script in your HTML.');
        return;
      }
      
      // Create scanner instance
      this.html5QrCode = new Html5Qrcode(
        this.videoElement.id || 'qr-scanner-video',
        { formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE] }
      );
      
      console.log('QR code library initialized');
    } catch (error) {
      console.error('Failed to initialize QR code library:', error);
      if (this.options.onError) {
        this.options.onError(error);
      }
    }
  }
  
  /**
   * Set up network listeners for online/offline events
   * @private
   */
  setupNetworkListeners() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processPendingScans();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }
  
  /**
   * Set up battery monitoring
   * @private
   */
  async setupBatteryMonitoring() {
    try {
      if ('getBattery' in navigator) {
        this.batteryManager = await navigator.getBattery();
        
        // Check initial battery level
        this.batteryLow = this.batteryManager.level <= 0.2;
        
        // Listen for battery level changes
        this.batteryManager.addEventListener('levelchange', () => {
          const wasLow = this.batteryLow;
          this.batteryLow = this.batteryManager.level <= 0.2;
          
          // If battery became low while scanning, adjust settings
          if (!wasLow && this.batteryLow && this.scanning) {
            this.applyBatteryOptimizations();
          }
          
          // If battery is no longer low, restore normal settings
          if (wasLow && !this.batteryLow && this.scanning) {
            this.restoreFromBatteryOptimizations();
          }
        });
      }
    } catch (error) {
      console.warn('Battery API not supported or permission denied:', error);
    }
  }
  
  /**
   * Apply optimizations for low battery
   * @private
   */
  applyBatteryOptimizations() {
    if (!this.scanning) return;
    
    // Reduce frame rate to save power
    this.stop();
    this.start({ 
      facingMode: this.options.facingMode || 'environment',
      fps: 5, // Lower FPS to save battery
      qrbox: this.calculateQrboxSize()
    });
    
    console.log('Applied battery optimizations: reduced frame rate to 5 FPS');
  }
  
  /**
   * Restore normal settings after battery optimization
   * @private
   */
  restoreFromBatteryOptimizations() {
    if (!this.scanning) return;
    
    // Restore normal frame rate
    this.stop();
    this.start({ 
      facingMode: this.options.facingMode || 'environment',
      fps: this.getOptimalFps(), 
      qrbox: this.calculateQrboxSize()
    });
    
    console.log('Restored normal settings from battery optimizations');
  }

  /**
   * Create scan line animation
   * @private
   */
  createScanLineAnimation() {
    if (!this.targetElement) return;
    
    // Create scan line element if it doesn't exist
    let scanLine = this.targetElement.querySelector('.qr-scanner-scan-line');
    if (!scanLine) {
      scanLine = document.createElement('div');
      scanLine.className = 'qr-scanner-scan-line';
      this.targetElement.appendChild(scanLine);
      
      // Add animation
      let position = 0;
      let direction = 1;
      
      this.scanLineInterval = setInterval(() => {
        if (!this.scanning) return;
        
        position += direction * 2;
        if (position >= this.targetElement.offsetHeight - 2) {
          direction = -1;
        } else if (position <= 0) {
          direction = 1;
        }
        
        scanLine.style.top = `${position}px`;
      }, 20);
    }
  }
  
  /**
   * Detect device type for optimizations
   * @private
   * @returns {string} Device type
   */
  detectDeviceType() {
    // Check for Surface Go 4
    if (window.matchMedia('(min-width: 1280px) and (max-width: 1920px) and (min-height: 800px) and (max-height: 1280px) and (aspect-ratio: 3/2)').matches ||
        (window.matchMedia('(min-width: 1280px) and (max-width: 1920px) and (min-height: 800px) and (max-height: 1280px)').matches && 
         window.matchMedia('(orientation: landscape)').matches)) {
      console.log('Detected Surface Go 4 device');
      return DEVICE_TYPES.SURFACE_GO;
    }
    
    // Check for iPad 11"
    if (window.matchMedia('(min-width: 1668px) and (max-width: 2388px) and (min-height: 1100px) and (max-height: 1668px) and (orientation: landscape)').matches) {
      console.log('Detected iPad 11" device');
      return DEVICE_TYPES.IPAD_11;
    }
    
    // Default to other
    return DEVICE_TYPES.OTHER;
  }
  
  /**
   * Get optimal FPS based on device type
   * @private
   * @returns {number} Optimal FPS
   */
  getOptimalFps() {
    switch (this.deviceType) {
      case DEVICE_TYPES.SURFACE_GO:
        return 15; // Surface Go 4 optimal FPS
      case DEVICE_TYPES.IPAD_11:
        return 20; // iPad 11" optimal FPS
      default:
        return 10; // Default FPS for other devices
    }
  }
  
  /**
   * Get optimal camera resolution based on device type
   * @private
   * @returns {Object} Width and height constraints
   */
  getOptimalResolution() {
    switch (this.deviceType) {
      case DEVICE_TYPES.SURFACE_GO:
        return {
          width: { ideal: 1280 },
          height: { ideal: 720 }
        };
      case DEVICE_TYPES.IPAD_11:
        return {
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        };
      default:
        return {}; // Default to browser's choice
    }
  }

  /**
   * Start scanning for QR codes
   * @param {Object} [config] - Configuration options for scanning
   * @param {string} [config.facingMode='environment'] - Camera facing mode ('environment' or 'user')
   * @param {number} [config.fps] - Frames per second for scanning
   * @returns {Promise<void>}
   */
  async start(config = {}) {
    if (this.scanning) return;
    
    const scanConfig = {
      facingMode: config.facingMode || 'environment',
      fps: config.fps || this.getOptimalFps()
    };
    
    try {
      if (!this.html5QrCode) {
        this.initQRCodeLibrary();
        if (!this.html5QrCode) {
          throw new Error('QR scanner library not initialized');
        }
      }
      
      // Configure camera with device-specific optimizations
      const cameraConfig = { 
        facingMode: scanConfig.facingMode,
        fps: scanConfig.fps,
        ...this.getOptimalResolution()
      };
      
      // Start scanning
      await this.html5QrCode.start(
        { facingMode: scanConfig.facingMode },
        {
          fps: scanConfig.fps,
          qrbox: this.calculateQrboxSize(),
          aspectRatio: this.getOptimalAspectRatio(),
          disableFlip: false,
          videoConstraints: cameraConfig
        },
        this.handleScanSuccess.bind(this),
        this.handleScanError.bind(this)
      );
      
      // Store stream for flashlight control
      const videoTrack = this.html5QrCode.getRunningTrackCameraCapabilities();
      if (videoTrack) {
        this.stream = new MediaStream([videoTrack]);
      }
      
      this.scanning = true;
      
      // Show scanning UI elements
      if (this.targetElement) {
        this.targetElement.style.display = 'block';
      }
      
      // Call onStart callback if provided
      if (this.options.onStart) {
        this.options.onStart();
      }
      
      console.log('QR scanner started with optimized settings for', this.deviceType);
    } catch (error) {
      console.error('Failed to start QR scanner:', error);
      
      if (this.options.onError) {
        this.options.onError(this.formatError(error));
      }
      
      throw error;
    }
  }
  
  /**
   * Get optimal aspect ratio based on device type
   * @private
   * @returns {number} Aspect ratio
   */
  getOptimalAspectRatio() {
    switch (this.deviceType) {
      case DEVICE_TYPES.SURFACE_GO:
        return 3/2; // Surface Go 4 aspect ratio
      case DEVICE_TYPES.IPAD_11:
        return 1.43; // iPad 11" aspect ratio
      default:
        return 1.0; // Default square aspect ratio
    }
  }
  
  /**
   * Calculate the optimal QR box size based on video dimensions and device type
   * @private
   * @returns {Object} - QR box dimensions
   */
  calculateQrboxSize() {
    if (!this.videoElement) return { width: 250, height: 250 };
    
    // Base calculation on video dimensions
    const minDimension = Math.min(
      this.videoElement.offsetWidth, 
      this.videoElement.offsetHeight
    );
    
    // Adjust size based on device type
    let qrboxSizePercentage;
    switch (this.deviceType) {
      case DEVICE_TYPES.SURFACE_GO:
        qrboxSizePercentage = 0.6; // 60% of min dimension for Surface Go 4
        break;
      case DEVICE_TYPES.IPAD_11:
        qrboxSizePercentage = 0.5; // 50% of min dimension for iPad 11"
        break;
      default:
        qrboxSizePercentage = 0.7; // 70% for other devices
    }
    
    const qrboxSize = Math.floor(minDimension * qrboxSizePercentage);
    return { width: qrboxSize, height: qrboxSize };
  }
  
  /**
   * Handle successful QR code scan
   * @private
   * @param {string} decodedText - Decoded QR code text
   * @param {Object} decodedResult - Full decoded result
   */
  handleScanSuccess(decodedText, decodedResult) {
    // Parse the QR code data
    const qrData = this.parseQRData(decodedText);
    
    // Process the scan result
    this.processScanResult(qrData);
    
    // Show success visual feedback
    this.showSuccessFeedback();
    
    // If in single scan mode, stop scanning after successful scan
    if (this.options.scanMode === SCAN_MODES.SINGLE) {
      this.stop();
    }
  }
  
  /**
   * Show visual feedback for successful scan
   * @private
   */
  showSuccessFeedback() {
    // Activate success overlay if available
    if (this.successOverlayElement) {
      this.successOverlayElement.classList.add('active');
      
      // Remove active class after animation completes
      setTimeout(() => {
        this.successOverlayElement.classList.remove('active');
      }, 1000);
    }
    
    // Vibrate if available (100ms)
    if ('vibrate' in navigator) {
      navigator.vibrate(100);
    }
  }
  
  /**
   * Handle QR code scan error
   * @private
   * @param {Error} error - Scan error
   */
  handleScanError(error) {
    // Only log errors that aren't just "QR code not found"
    if (error && !error.message.includes('QR code not found')) {
      console.error('QR scan error:', error);
    }
  }

  /**
   * Stop scanning for QR codes
   */
  stop() {
    if (!this.scanning) return;
    
    try {
      if (this.html5QrCode) {
        this.html5QrCode.stop();
      }
      
      this.scanning = false;
      
      // Hide scanning UI elements
      if (this.targetElement) {
        this.targetElement.style.display = 'none';
      }
      
      // Call onStop callback if provided
      if (this.options.onStop) {
        this.options.onStop();
      }
      
      console.log('QR scanner stopped');
    } catch (error) {
      console.error('Error stopping QR scanner:', error);
    }
  }

  /**
   * Parse QR code data into a structured format
   * @param {string} qrData - Raw QR code data
   * @returns {Object} - Parsed QR code data
   * @private
   */
  parseQRData(qrData) {
    try {
      // Try to parse as JSON first
      if (qrData.startsWith('{') && qrData.endsWith('}')) {
        return JSON.parse(qrData);
      }
      
      // Check for URL format with parameters
      if (qrData.includes('://')) {
        const url = new URL(qrData);
        const type = url.searchParams.get('type');
        const id = url.searchParams.get('id');
        const data = url.searchParams.get('data');
        
        return {
          type: type || QR_CODE_TYPES.UNKNOWN,
          id: id || null,
          url: qrData,
          data: data ? JSON.parse(decodeURIComponent(data)) : null,
          timestamp: new Date().toISOString()
        };
      }
      
      // Check for custom format (type:id:data)
      if (qrData.includes(':')) {
        const [type, id, ...dataParts] = qrData.split(':');
        const dataStr = dataParts.join(':');
        
        return {
          type: type || QR_CODE_TYPES.UNKNOWN,
          id: id || null,
          data: dataStr ? JSON.parse(dataStr) : null,
          timestamp: new Date().toISOString()
        };
      }
      
      // Default format
      return {
        type: QR_CODE_TYPES.UNKNOWN,
        data: qrData,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error parsing QR data:', error);
      
      // Return basic format if parsing fails
      return {
        type: QR_CODE_TYPES.UNKNOWN,
        data: qrData,
        rawData: qrData,
        timestamp: new Date().toISOString(),
        error: 'Parse error'
      };
    }
  }

  /**
   * Process a scan result
   * @param {Object} qrData - Parsed QR code data
   * @private
   */
  processScanResult(qrData) {
    // Avoid duplicate scans (within 2 seconds)
    const isDuplicate = this.isDuplicateScan(qrData);
    if (isDuplicate) return;
    
    // Add to scan history
    this.addToScanHistory(qrData);
    
    // Store the last result
    this.lastResult = qrData;
    console.log('QR code scanned:', qrData);
    
    // Handle offline mode
    if (!this.isOnline) {
      this.storeOfflineScan(qrData);
    }
    
    // Call onScan callback
    if (this.options.onScan) {
      this.options.onScan(qrData);
    }
    
    // Emit scan event
    const event = new CustomEvent('qr-scan', { 
      detail: qrData,
      bubbles: true
    });
    this.videoElement.dispatchEvent(event);
  }
  
  /**
   * Check if a scan is a duplicate (within last 2 seconds)
   * @param {Object} qrData - Parsed QR code data
   * @returns {boolean} - True if duplicate
   * @private
   */
  isDuplicateScan(qrData) {
    if (!qrData) return true;
    
    // Check if we have a previous scan with the same data in the last 2 seconds
    const now = Date.now();
    const recentScans = this.scanHistory.filter(scan => {
      return (now - scan.timestamp) < 2000 && 
             JSON.stringify(scan.data) === JSON.stringify(qrData);
    });
    
    return recentScans.length > 0;
  }
  
  /**
   * Add a scan to the history
   * @param {Object} qrData - Parsed QR code data
   * @private
   */
  addToScanHistory(qrData) {
    // Add timestamp for tracking
    const scan = {
      ...qrData,
      timestamp: Date.now()
    };
    
    // Add to history
    this.scanHistory.push(scan);
    
    // Limit history size
    if (this.scanHistory.length > 50) {
      this.scanHistory.shift();
    }
  }
  
  /**
   * Store a scan for offline processing
   * @param {Object} qrData - Parsed QR code data
   * @private
   */
  storeOfflineScan(qrData) {
    // Add to offline queue
    const offlineScan = {
      ...qrData,
      offlineTimestamp: Date.now()
    };
    
    this.offlineQueue.push(offlineScan);
    
    // Store in local storage
    this.saveToLocalStorage('offline_scans', offlineScan);
    
    console.log('Stored scan for offline processing:', offlineScan);
  }
  
  /**
   * Save data to local storage
   * @param {string} key - Storage key
   * @param {Object} data - Data to store
   * @private
   */
  saveToLocalStorage(key, data) {
    try {
      // Get existing data
      const storageKey = `atlas25_${key}`;
      const existingData = JSON.parse(localStorage.getItem(storageKey) || '[]');
      
      // Add new data
      existingData.push(data);
      
      // Save back to local storage
      localStorage.setItem(storageKey, JSON.stringify(existingData));
    } catch (error) {
      console.error('Error saving to local storage:', error);
    }
  }
  
  /**
   * Process any pending scans when coming back online
   * @private
   */
  async processPendingScans() {
    if (this.offlineQueue.length === 0) return;
    
    console.log(`Processing ${this.offlineQueue.length} pending scans`);
    
    // Process the queue (in a real implementation, this would sync with a server)
    // For now, we'll just clear the queue
    this.offlineQueue = [];
    
    // Clear from local storage
    localStorage.removeItem('atlas25_offline_scans');
  }

  /**
   * Toggle flashlight
   * @returns {Promise<boolean>} - True if flashlight is on
   */
  async toggleFlashlight() {
    try {
      if (!this.html5QrCode) {
        throw new Error('Scanner not initialized');
      }
      
      // Get current torch state
      const torchState = await this.html5QrCode.getTorchState();
      
      // Toggle torch
      if (torchState) {
        await this.html5QrCode.turnOffFlash();
        return false;
      } else {
        await this.html5QrCode.turnOnFlash();
        return true;
      }
    } catch (error) {
      console.error('Error toggling flashlight:', error);
      
      // If html5-qrcode method fails, try the native approach
      if (this.stream) {
        try {
          const track = this.stream.getVideoTracks()[0];
          
          if (!track) {
            throw new Error('No video track available');
          }
          
          // Check if flashlight is supported
          const capabilities = track.getCapabilities();
          if (!capabilities.torch) {
            throw new Error('Flashlight not supported');
          }
          
          // Get current flashlight state
          const settings = track.getSettings();
          const currentState = settings.torch || false;
          
          // Toggle flashlight
          const newState = !currentState;
          await track.applyConstraints({ advanced: [{ torch: newState }] });
          
          return newState;
        } catch (nativeError) {
          console.error('Native flashlight toggle failed:', nativeError);
          throw nativeError;
        }
      }
      
      throw error;
    }
  }

  /**
   * Switch camera
   * @returns {Promise<string>} - New camera facing mode
   */
  async switchCamera() {
    try {
      // Stop current scanner
      this.stop();
      
      // Get current camera facing mode
      const currentFacingMode = this.options.facingMode || 'environment';
      
      // Toggle facing mode
      this.options.facingMode = currentFacingMode === 'environment' ? 'user' : 'environment';
      
      // Restart with new facing mode
      await this.start({ 
        facingMode: this.options.facingMode,
        fps: this.getOptimalFps()
      });
      
      return this.options.facingMode;
    } catch (error) {
      console.error('Error switching camera:', error);
      throw error;
    }
  }
  
  /**
   * Format error message for user display
   * @param {Error} error - Error object
   * @returns {Object} - Formatted error
   * @private
   */
  formatError(error) {
    let errorMessage = 'An unknown error occurred';
    let errorType = 'unknown';
    
    if (error) {
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Camera access denied. Please grant permission to use the camera.';
        errorType = 'permission';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No camera found on this device.';
        errorType = 'no-camera';
      } else if (error.name === 'NotReadableError') {
        errorMessage = 'Camera is already in use by another application.';
        errorType = 'in-use';
      } else if (error.message) {
        errorMessage = error.message;
      }
    }
    
    return {
      message: errorMessage,
      type: errorType,
      originalError: error
    };
  }
  
  /**
   * Get scan history
   * @returns {Array} - Scan history
   */
  getScanHistory() {
    return [...this.scanHistory];
  }
  
  /**
   * Clear scan history
   */
  clearScanHistory() {
    this.scanHistory = [];
  }
  
  /**
   * Get offline queue
   * @returns {Array} - Offline queue
   */
  getOfflineQueue() {
    return [...this.offlineQueue];
  }
  
  /**
   * Check if the scanner is currently scanning
   * @returns {boolean} - True if scanning
   */
  isScanning() {
    return this.scanning;
  }
  
  /**
   * Get the current scan mode
   * @returns {string} - Scan mode
   */
  getScanMode() {
    return this.options.scanMode;
  }
  
  /**
   * Set the scan mode
   * @param {string} mode - Scan mode (SCAN_MODES.SINGLE or SCAN_MODES.CONTINUOUS)
   */
  setScanMode(mode) {
    if (Object.values(SCAN_MODES).includes(mode)) {
      this.options.scanMode = mode;
    } else {
      console.warn(`Invalid scan mode: ${mode}. Using default.`);
    }
  }
  
  /**
   * Get detected device type
   * @returns {string} - Device type
   */
  getDeviceType() {
    return this.deviceType;
  }
  
  /**
   * Check if battery is low
   * @returns {boolean} - True if battery is low
   */
  isBatteryLow() {
    return this.batteryLow;
  }
  
  /**
   * Clean up resources when done
   */
  destroy() {
    // Stop scanning if active
    if (this.scanning) {
      this.stop();
    }
    
    // Clear scan line animation interval
    if (this.scanLineInterval) {
      clearInterval(this.scanLineInterval);
    }
    
    // Remove event listeners
    window.removeEventListener('online', this.processPendingScans);
    window.removeEventListener('offline', () => { this.isOnline = false; });
    
    // Clean up battery manager
    if (this.batteryManager) {
      this.batteryManager.removeEventListener('levelchange', () => {});
    }
    
    console.log('QR scanner resources cleaned up');
  }
}

export default QRScannerOptimized;