# QR Scanner Implementation for Webflow

This document provides a comprehensive guide for implementing a QR scanner in Webflow with booth type functionality (referee, media, coaching) without Firebase integration.

## Table of Contents

1. [HTML Structure](#html-structure)
2. [CSS Styling](#css-styling)
3. [Modified JavaScript](#modified-javascript)
4. [Testing and Debugging](#testing-and-debugging)
5. [Future Integration with Firebase](#future-integration-with-firebase)

## HTML Structure

You've already created the HTML structure with the following class names:

- `.qr-scanner-container`: Container for the scanner
- `.qr-scanner-video`: Video element for camera feed
- `.qr-scanner-canvas`: (Optional) Canvas element for processing
- `.qr-scanner-start-button`: Button to start scanning
- `.qr-scanner-stop-button`: Button to stop scanning
- `.qr-scanner-switch-camera`: Button to switch cameras
- `.qr-scanner-toggle-flash`: Button to toggle flashlight
- `.qr-scanner-result`: Element to display scan result
- `.qr-scanner-error`: Element to display error messages
- `.qr-scanner-status`: Element to display scanner status
- `.qr-scanner-offline-indicator`: Element to display offline status

Make sure all these elements are present in your Webflow page. You can create them as div elements with the appropriate classes.

## CSS Styling

Add the following CSS to your Webflow page's custom code section:

```css
/* QR Scanner Container */
.qr-scanner-container {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Video Element */
.qr-scanner-video {
  width: 100%;
  height: auto;
  min-height: 300px;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
}

/* Canvas Element */
.qr-scanner-canvas {
  display: none; /* Hidden by default */
  position: absolute;
  top: 0;
  left: 0;
}

/* Button Styling */
.qr-scanner-start-button,
.qr-scanner-stop-button,
.qr-scanner-switch-camera,
.qr-scanner-toggle-flash {
  display: inline-block;
  padding: 10px 15px;
  margin: 10px 5px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.qr-scanner-start-button:hover,
.qr-scanner-stop-button:hover,
.qr-scanner-switch-camera:hover,
.qr-scanner-toggle-flash:hover {
  background-color: #0069d9;
}

.qr-scanner-stop-button {
  background-color: #dc3545;
  display: none; /* Hidden by default */
}

.qr-scanner-stop-button:hover {
  background-color: #c82333;
}

/* Button Container */
.qr-scanner-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 15px;
}

/* Result Display */
.qr-scanner-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #d4edda;
  color: #155724;
  border-radius: 5px;
  display: none; /* Hidden by default */
  word-break: break-word;
}

/* Error Display */
.qr-scanner-error {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 5px;
  display: none; /* Hidden by default */
}

/* Status Display */
.qr-scanner-status {
  margin-top: 10px;
  padding: 10px;
  border-radius: 5px;
  text-align: center;
  display: none; /* Hidden by default */
}

.status-info {
  background-color: #cce5ff;
  color: #004085;
}

.status-success {
  background-color: #d4edda;
  color: #155724;
}

.status-warning {
  background-color: #fff3cd;
  color: #856404;
}

.status-error {
  background-color: #f8d7da;
  color: #721c24;
}

/* Offline Indicator */
.qr-scanner-offline-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 5px;
  font-size: 12px;
  display: none; /* Hidden by default */
}

/* Animation for successful scan */
@keyframes scanSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.scan-success {
  animation: scanSuccess 0.5s ease;
}

/* Scanning active state */
.qr-scanner-container.scanning .qr-scanner-video {
  border: 2px solid #28a745;
}

/* Scan region overlay */
.qr-scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.qr-scanner-overlay::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  height: 70%;
  transform: translate(-50%, -50%);
  border: 2px solid #28a745;
  border-radius: 10px;
  box-shadow: 0 0 0 100vmax rgba(0, 0, 0, 0.3);
  clip-path: inset(0 -100vmax -100vmax 0);
}

/* Flash on state */
.qr-scanner-toggle-flash.flash-on {
  background-color: #ffc107;
  color: #212529;
}

/* Responsive Design */
@media (max-width: 768px) {
  .qr-scanner-container {
    padding: 15px;
  }
  
  .qr-scanner-video {
    min-height: 250px;
  }
  
  .qr-scanner-buttons {
    flex-direction: column;
  }
  
  .qr-scanner-start-button,
  .qr-scanner-stop-button,
  .qr-scanner-switch-camera,
  .qr-scanner-toggle-flash {
    width: 100%;
    margin: 5px 0;
  }
}
```

## Modified JavaScript

Here's the modified JavaScript code that works without Firebase integration but maintains booth type functionality:

```javascript
/**
 * @file qr-scanner_webflow-embed.js
 * @description Webflow embed for QR scanner component (Firebase-independent version)
 * 
 * HOW TO USE:
 * 1. Add this code to a Webflow embed element
 * 2. Include the html5-qrcode library:
 *    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
 * 3. Ensure you have the following elements with these specific classes:
 *    - .qr-scanner-container: Container for the scanner
 *    - .qr-scanner-video: Video element for camera feed
 *    - .qr-scanner-canvas: (Optional) Canvas element for processing
 *    - .qr-scanner-start-button: Button to start scanning
 *    - .qr-scanner-stop-button: Button to stop scanning
 *    - .qr-scanner-switch-camera: Button to switch cameras
 *    - .qr-scanner-toggle-flash: Button to toggle flashlight
 *    - .qr-scanner-result: Element to display scan result
 *    - .qr-scanner-error: Element to display error messages
 *    - .qr-scanner-status: Element to display scanner status
 *    - .qr-scanner-offline-indicator: Element to display offline status
 * 4. Optional data attributes for configuration:
 *    - data-auto-start="true": Start scanning automatically
 *    - data-scan-mode="single": Set scan mode (single or continuous)
 *    - data-booth-type="referee": Set booth type (referee, media, coaching)
 */

/**
 * QR code format types
 * @enum {string}
 */
const QR_CODE_TYPES = {
  SCORE: 'score',
  QUIZ: 'quiz',
  AR: 'ar',
  UNKNOWN: 'unknown'
};

/**
 * Scan modes for QR scanner
 * @enum {string}
 */
const SCAN_MODES = {
  SINGLE: 'single',
  CONTINUOUS: 'continuous'
};

/**
 * Booth types for different experiences
 * @enum {string}
 */
const BOOTH_TYPES = {
  REFEREE: 'referee',
  MEDIA: 'media',
  COACHING: 'coaching'
};

/**
 * Local storage keys
 * @enum {string}
 */
const STORAGE_KEYS = {
  SCAN_HISTORY: 'atlas25_scan_history',
  OFFLINE_QUEUE: 'atlas25_offline_queue'
};

/**
 * Initialize the QR scanner in Webflow
 */
function initQRScannerWebflow() {
  try {
    console.log('Initializing QR scanner in Webflow');
    
    // Find elements
    const container = document.querySelector('.qr-scanner-container');
    const video = document.querySelector('.qr-scanner-video');
    const canvas = document.querySelector('.qr-scanner-canvas');
    const startButton = document.querySelector('.qr-scanner-start-button');
    const stopButton = document.querySelector('.qr-scanner-stop-button');
    const switchCameraButton = document.querySelector('.qr-scanner-switch-camera');
    const toggleFlashButton = document.querySelector('.qr-scanner-toggle-flash');
    const resultElement = document.querySelector('.qr-scanner-result');
    const errorElement = document.querySelector('.qr-scanner-error');
    const statusElement = document.querySelector('.qr-scanner-status');
    const offlineIndicator = document.querySelector('.qr-scanner-offline-indicator');
    
    // Check if required elements exist
    if (!container || !video) {
      showError('Required QR scanner elements not found');
      return;
    }
    
    // Set unique ID for video element if not present
    if (!video.id) {
      video.id = `qr-scanner-video-${Date.now()}`;
    }
    
    // Get configuration from data attributes
    const autoStart = container.getAttribute('data-auto-start') === 'true';
    const scanMode = container.getAttribute('data-scan-mode') || SCAN_MODES.CONTINUOUS;
    const boothType = container.getAttribute('data-booth-type') || BOOTH_TYPES.REFEREE;
    
    // Create HTML5 QR Scanner instance
    let html5QrCode = null;
    let scanning = false;
    let currentCamera = 'environment';
    
    // Initialize QR code library
    try {
      // Check if html5-qrcode is available
      if (typeof Html5Qrcode === 'undefined') {
        showError('Html5Qrcode library not found. Make sure to include the script in your HTML.');
        return;
      }
      
      // Create scanner instance
      html5QrCode = new Html5Qrcode(
        video.id,
        { formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE] }
      );
      
      console.log('QR code library initialized');
    } catch (error) {
      console.error('Failed to initialize QR code library:', error);
      showError('Failed to initialize QR scanner');
      return;
    }
    
    // Set up event listeners
    if (startButton) {
      startButton.addEventListener('click', () => {
        startScanning();
      });
    }
    
    if (stopButton) {
      stopButton.addEventListener('click', () => {
        stopScanning();
      });
    }
    
    if (switchCameraButton) {
      switchCameraButton.addEventListener('click', () => {
        switchCamera();
      });
    }
    
    if (toggleFlashButton) {
      toggleFlashButton.addEventListener('click', () => {
        toggleFlash();
      });
    }
    
    // Set up offline indicator
    updateOfflineIndicator();
    window.addEventListener('online', () => updateOfflineIndicator());
    window.addEventListener('offline', () => updateOfflineIndicator());
    
    // Handle scan result based on booth type
    function handleScan(decodedText, decodedResult) {
      // Parse the QR code data
      const qrData = parseQRData(decodedText);
      
      console.log('QR code scanned:', qrData);
      
      if (resultElement) {
        // Format result display based on type
        let displayText = '';
        
        if (qrData.type === QR_CODE_TYPES.SCORE) {
          displayText = `Score: ${qrData.data.score || 'N/A'}`;
        } else if (qrData.type === QR_CODE_TYPES.QUIZ) {
          displayText = `Quiz: ${qrData.data.title || 'Quiz'}`;
        } else if (qrData.type === QR_CODE_TYPES.AR) {
          displayText = `AR Experience: ${qrData.data.name || 'AR Experience'}`;
        } else {
          displayText = `QR Code: ${typeof qrData.data === 'object' ? JSON.stringify(qrData.data) : qrData.data}`;
        }
        
        resultElement.textContent = displayText;
        resultElement.style.display = 'block';
        
        // Add success animation
        resultElement.classList.add('scan-success');
        setTimeout(() => {
          resultElement.classList.remove('scan-success');
        }, 2000);
      }
      
      // Process scan based on booth type
      processScanByBoothType(qrData, boothType);
      
      // Dispatch custom event
      const event = new CustomEvent('qr-code-scanned', { 
        detail: { result: qrData, boothType },
        bubbles: true 
      });
      container.dispatchEvent(event);
      
      // If in single scan mode, stop scanning after successful scan
      if (scanMode === SCAN_MODES.SINGLE) {
        stopScanning();
      }
    }
    
    // Process scan based on booth type
    function processScanByBoothType(result, boothType) {
      try {
        switch (boothType) {
          case BOOTH_TYPES.REFEREE:
            processRefereeBoothScan(result);
            break;
          
          case BOOTH_TYPES.MEDIA:
            processMediaBoothScan(result);
            break;
          
          case BOOTH_TYPES.COACHING:
            processCoachingBoothScan(result);
            break;
          
          default:
            console.warn(`Unknown booth type: ${boothType}`);
        }
      } catch (error) {
        console.error(`Error processing scan for ${boothType} booth:`, error);
        showError(`Failed to process scan: ${error.message}`);
      }
    }
    
    // Process scan for referee booth
    function processRefereeBoothScan(result) {
      if (result.type !== QR_CODE_TYPES.SCORE) {
        showError('Invalid QR code for referee booth. Expected score data.');
        return;
      }
      
      // Store score data locally
      const scoreData = {
        score: result.data.score,
        gameId: result.data.gameId,
        timestamp: new Date().toISOString(),
        boothId: result.data.boothId || 'unknown'
      };
      
      // Save score to local storage
      saveToLocalStorage('scores', scoreData);
      showStatus('Score recorded locally!', 'success');
    }
    
    // Process scan for media booth
    function processMediaBoothScan(result) {
      if (result.type !== QR_CODE_TYPES.QUIZ) {
        showError('Invalid QR code for media booth. Expected quiz data.');
        return;
      }
      
      // Launch quiz
      try {
        const quizId = result.data.quizId || result.id;
        
        if (!quizId) {
          showError('Invalid quiz QR code. Missing quiz ID.');
          return;
        }
        
        // Dispatch event to launch quiz
        const launchEvent = new CustomEvent('launch-quiz', {
          detail: { quizId, quizData: result.data },
          bubbles: true
        });
        
        document.dispatchEvent(launchEvent);
        showStatus('Launching quiz...', 'success');
      } catch (error) {
        console.error('Error launching quiz:', error);
        showError('Failed to launch quiz. Please try again.');
      }
    }
    
    // Process scan for coaching booth
    function processCoachingBoothScan(result) {
      if (result.type !== QR_CODE_TYPES.AR) {
        showError('Invalid QR code for coaching booth. Expected AR data.');
        return;
      }
      
      // Launch AR experience
      try {
        const arId = result.data.arId || result.id;
        
        if (!arId) {
          showError('Invalid AR QR code. Missing AR ID.');
          return;
        }
        
        // Dispatch event to launch AR experience
        const launchEvent = new CustomEvent('launch-ar', {
          detail: { arId, arData: result.data },
          bubbles: true
        });
        
        document.dispatchEvent(launchEvent);
        showStatus('Launching AR experience...', 'success');
      } catch (error) {
        console.error('Error launching AR experience:', error);
        showError('Failed to launch AR experience. Please try again.');
      }
    }
    
    // Parse QR code data into a structured format
    function parseQRData(qrData) {
      try {
        // Try to parse as JSON first
        if (qrData.startsWith('{') && qrData.endsWith('}')) {
          const parsed = JSON.parse(qrData);
          return {
            type: parsed.type || QR_CODE_TYPES.UNKNOWN,
            data: parsed,
            timestamp: new Date().toISOString()
          };
        }
        
        // Check for URL format with parameters
        if (qrData.includes('://')) {
          const url = new URL(qrData);
          const type = url.searchParams.get('type');
          const id = url.searchParams.get('id');
          const data = url.searchParams.get('data');
          
          return {
            type: type || QR_CODE_TYPES.UNKNOWN,
            id: id || null,
            url: qrData,
            data: data ? JSON.parse(decodeURIComponent(data)) : null,
            timestamp: new Date().toISOString()
          };
        }
        
        // Check for custom format (type:id:data)
        if (qrData.includes(':')) {
          const [type, id, ...dataParts] = qrData.split(':');
          const dataStr = dataParts.join(':');
          
          return {
            type: type || QR_CODE_TYPES.UNKNOWN,
            id: id || null,
            data: dataStr ? JSON.parse(dataStr) : null,
            timestamp: new Date().toISOString()
          };
        }
        
        // Default format
        return {
          type: QR_CODE_TYPES.UNKNOWN,
          data: qrData,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        console.error('Error parsing QR data:', error);
        
        // Return basic format if parsing fails
        return {
          type: QR_CODE_TYPES.UNKNOWN,
          data: qrData,
          rawData: qrData,
          timestamp: new Date().toISOString(),
          error: 'Parse error'
        };
      }
    }
    
    // Handle errors
    function handleError(error) {
      console.error('QR scanner error:', error);
      showError(error.message || 'Failed to access camera');
    }
    
    // Show error message
    function showError(message) {
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        
        // Hide error after 5 seconds
        setTimeout(() => {
          errorElement.style.display = 'none';
        }, 5000);
      }
    }
    
    // Show status message
    function showStatus(message, type = 'info') {
      if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `qr-scanner-status status-${type}`;
        statusElement.style.display = 'block';
        
        // Hide status after 3 seconds
        setTimeout(() => {
          statusElement.style.display = 'none';
        }, 3000);
      }
    }
    
    // Update offline indicator
    function updateOfflineIndicator() {
      if (offlineIndicator) {
        if (navigator.onLine) {
          offlineIndicator.style.display = 'none';
        } else {
          offlineIndicator.style.display = 'block';
          offlineIndicator.textContent = 'Offline Mode';
        }
      }
    }
    
    // Save data to local storage
    function saveToLocalStorage(collection, data) {
      try {
        const key = `atlas25_${collection}`;
        let items = JSON.parse(localStorage.getItem(key) || '[]');
        
        // Add unique ID and timestamp
        const newItem = {
          ...data,
          id: `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date().toISOString()
        };
        
        items.push(newItem);
        
        // Limit storage size
        if (items.length > 100) {
          items = items.slice(-100);
        }
        
        localStorage.setItem(key, JSON.stringify(items));
        return newItem.id;
      } catch (error) {
        console.error(`Error saving to local storage (${collection}):`, error);
        return null;
      }
    }
    
    // Start scanning
    function startScanning() {
      if (scanning) return;
      
      if (resultElement) {
        resultElement.textContent = '';
        resultElement.style.display = 'none';
      }
      
      if (errorElement) {
        errorElement.style.display = 'none';
      }
      
      // Configure camera
      const cameraConfig = { 
        facingMode: currentCamera
      };
      
      // Calculate QR box size
      const minDimension = Math.min(
        video.offsetWidth, 
        video.offsetHeight
      );
      const qrboxSize = Math.floor(minDimension * 0.7);
      
      // Start scanning
      html5QrCode.start(
        { facingMode: currentCamera },
        {
          fps: 10,
          qrbox: { width: qrboxSize, height: qrboxSize },
          aspectRatio: 1.0,
          disableFlip: false
        },
        handleScan,
        (error) => {
          // Only log errors that aren't just "QR code not found"
          if (error && !error.message.includes('QR code not found')) {
            handleError(error);
          }
        }
      ).then(() => {
        scanning = true;
        
        // Update UI
        if (container) container.classList.add('scanning');
        if (startButton) startButton.style.display = 'none';
        if (stopButton) stopButton.style.display = 'block';
        if (statusElement) {
          statusElement.textContent = 'Scanner active';
          statusElement.className = 'qr-scanner-status status-info';
          statusElement.style.display = 'block';
        }
        
        console.log('QR scanner started');
      }).catch(handleError);
    }
    
    // Stop scanning
    function stopScanning() {
      if (!scanning) return;
      
      html5QrCode.stop().then(() => {
        scanning = false;
        
        // Update UI
        if (container) container.classList.remove('scanning');
        if (startButton) startButton.style.display = 'block';
        if (stopButton) stopButton.style.display = 'none';
        if (statusElement) {
          statusElement.textContent = 'Scanner stopped';
          statusElement.style.display = 'block';
          
          // Hide status after 2 seconds
          setTimeout(() => {
            statusElement.style.display = 'none';
          }, 2000);
        }
        
        console.log('QR scanner stopped');
      }).catch(error => {
        console.error('Error stopping QR scanner:', error);
      });
    }
    
    // Switch camera
    function switchCamera() {
      if (statusElement) {
        statusElement.textContent = 'Switching camera...';
        statusElement.style.display = 'block';
      }
      
      // Stop current scanner
      const wasScanning = scanning;
      if (scanning) {
        html5QrCode.stop().then(() => {
          // Toggle camera
          currentCamera = currentCamera === 'environment' ? 'user' : 'environment';
          
          // Restart scanner if it was active
          if (wasScanning) {
            startScanning();
          }
          
          if (statusElement) {
            statusElement.textContent = `Using ${currentCamera === 'environment' ? 'back' : 'front'} camera`;
            
            // Hide status after 2 seconds
            setTimeout(() => {
              statusElement.style.display = 'none';
            }, 2000);
          }
        }).catch(handleError);
      } else {
        // Just toggle camera if not scanning
        currentCamera = currentCamera === 'environment' ? 'user' : 'environment';
        
        if (statusElement) {
          statusElement.textContent = `Camera switched to ${currentCamera === 'environment' ? 'back' : 'front'}`;
          
          // Hide status after 2 seconds
          setTimeout(() => {
            statusElement.style.display = 'none';
          }, 2000);
        }
      }
    }
    
    // Toggle flash
    function toggleFlash() {
      if (!scanning) {
        showStatus('Start scanning first', 'warning');
        return;
      }
      
      html5QrCode.getState().then(state => {
        // Check if torch is currently on
        const isTorchOn = state.torch;
        
        // Toggle torch
        html5QrCode.applyVideoConstraints({
          advanced: [{ torch: !isTorchOn }]
        }).then(() => {
          if (toggleFlashButton) {
            toggleFlashButton.classList.toggle('flash-on', !isTorchOn);
          }
          
          if (statusElement) {
            statusElement.textContent = !isTorchOn ? 'Flash: ON' : 'Flash: OFF';
            statusElement.style.display = 'block';
            
            // Hide status after 2 seconds
            setTimeout(() => {
              statusElement.style.display = 'none';
            }, 2000);
          }
        }).catch(error => {
          console.error('Error toggling flashlight:', error);
          
          // Only show error if it's not a "not supported" error
          if (!error.message.includes('not supported')) {
            handleError(error);
          } else {
            // Show a more user-friendly message
            if (statusElement) {
              statusElement.textContent = 'Flash not supported on this device';
              statusElement.style.display = 'block';
              
              // Hide status after 2 seconds
              setTimeout(() => {
                statusElement.style.display = 'none';
              }, 2000);
            }
          }
        });
      }).catch(error => {
        console.error('Error getting scanner state:', error);
        handleError(error);
      });
    }
    
    // Auto-start if specified
    if (autoStart) {
      startScanning();
    }
    
    // Create overlay for scan region
    const overlay = document.createElement('div');
    overlay.className = 'qr-scanner-overlay';
    container.appendChild(overlay);
    
    console.log('QR scanner initialized');
    
    // Expose API for external access
    container.qrScanner = {
      start: startScanning,
      stop: stopScanning,
      switchCamera: switchCamera,
      toggleFlash: toggleFlash
    };
  } catch (error) {
    console.error('Failed to initialize QR scanner:', error);
  }
}

// Initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initQRScannerWebflow);

// Fallback for Webflow's preview mode where DOMContentLoaded might have already fired
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initQRScannerWebflow, 1);
}

// Expose API for external access
window.Atlas25QRScanner = {
  start: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.start();
    }
  },
  
  stop: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.stop();
    }
  },
  
  switchCamera: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.switchCamera();
    }
  },
  
  toggleFlash: function(containerId) {
    const container = containerId 
      ? document.getElementById(containerId)
      : document.querySelector('.qr-scanner-container');
      
    if (container && container.qrScanner) {
      container.qrScanner.toggleFlash();
    }
  }
};
```

## Testing and Debugging

To test your QR scanner implementation:

1. **Create Test QR Codes**:
   - For Referee Booth: Create a QR code with JSON data like `{"type":"score","score":100,"gameId":"game123"}`
   - For Media Booth: Create a QR code with JSON data like `{"type":"quiz","quizId":"quiz123","title":"Basketball Quiz"}`
   - For Coaching Booth: Create a QR code with JSON data like `{"type":"ar","arId":"ar123","name":"Shooting Technique"}`

2. **Test Different QR Code Formats**:
   - Test JSON format: `{"type":"score","score":100}`
   - Test URL format: `https://atlas25.nba.com/scan?type=quiz&id=quiz123&data=%7B%22title%22%3A%22Basketball%20Quiz%22%7D`
   - Test custom format: `score:game123:{"score":100}`

3. **Test Responsive Design**:
   - Test on desktop browsers
   - Test on mobile devices (both portrait and landscape)
   - Test with different screen sizes

4. **Test Camera Functionality**:
   - Test switching between front and back cameras
   - Test flashlight toggle (on supported devices)
   - Test scanning in different lighting conditions

5. **Test Offline Functionality**:
   - Test scanning while offline
   - Verify offline indicator appears
   - Verify scans are stored locally

6. **Debug Common Issues**:
   - Camera access denied: Make sure to request camera permissions
   - QR code not detected: Ensure proper lighting and QR code is in frame
   - Flash not working: Not all devices support flash control via browser
   - Scan result not displaying: Check console for errors

## Future Integration with Firebase

When you're ready to integrate with Firebase, follow these steps:

1. **Add Firebase SDK to your Webflow page**:
   ```html
   <!-- Firebase App (the core Firebase SDK) -->
   <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app.js"></script>
   
   <!-- Add Firebase products that you want to use -->
   <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth.js"></script>
   <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore.js"></script>
   ```

2. **Initialize Firebase**:
   ```javascript
   // Your web app's Firebase configuration
   const firebaseConfig = {
     apiKey: "YOUR_API_KEY",
     authDomain: "YOUR_AUTH_DOMAIN",
     projectId: "YOUR_PROJECT_ID",
     storageBucket: "YOUR_STORAGE_BUCKET",
     messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
     appId: "YOUR_APP_ID"
   };
   
   // Initialize Firebase
   firebase.initializeApp(firebaseConfig);
   ```

3. **Modify the QR Scanner Code**:
   - Replace the local storage functions with Firebase calls
   - Update the booth type processing functions to save data to Firebase
   - Implement proper authentication before saving data

4. **Add Offline Synchronization**:
   - Implement the offline manager functionality
   - Set up background sync for offline data
   - Add conflict resolution for data synced while offline

5. **Add User Authentication**:
   - Implement user login/registration
   - Secure Firebase rules to protect data
   - Associate scanned data with user accounts

By following this implementation plan, you'll have a fully functional QR scanner in Webflow that works with booth type functionality and can be easily extended to integrate with Firebase in the future.