/**
 * @file dashboard.js
 * @description Admin dashboard functionality for Atlas25 Web App.
 * @module admin/dashboard
 */

import firebaseService from '../../core/firebase/firebase-service.js';
import stateManager from '../../core/state/state-manager.js';
import { showNotification } from '../../utils/ui-helpers.js';
import { exportToCSV } from '../../utils/export-helpers.js'; // Assuming this helper will be created

/**
 * AdminDashboard class for managing the admin dashboard
 */
class AdminDashboard {
  /**
   * Create an AdminDashboard instance.
   * @param {Object} options - Configuration options.
   * @param {HTMLElement|string} [options.container] - The container element for the dashboard.
   */
  constructor(options = {}) {
    this.container = typeof options.container === 'string'
      ? document.querySelector(options.container)
      : options.container;
    
    this.participants = [];
    this.booths = [];

    // Bind methods
    this.handleParticipantClick = this.handleParticipantClick.bind(this);
    this.handleExportClick = this.handleExportClick.bind(this);
  }

  /**
   * Initializes the admin dashboard.
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // 1. Check for admin authorization
      const user = stateManager.get('user.profile');
      if (!user || !user.isAdmin) {
        this.renderUnauthorized();
        return;
      }

      // 2. Show loading state
      this.renderLoading();

      // 3. Fetch necessary data
      await this.fetchData();

      // 4. Render the dashboard
      this.renderDashboard();

      console.log('Admin Dashboard initialized');
    } catch (error) {
      console.error('Failed to initialize Admin Dashboard:', error);
      this.renderError('Failed to load dashboard. Please try again later.');
      showNotification('Failed to initialize dashboard', { type: 'error' });
    }
  }

  /**
   * Fetches all required data for the dashboard.
   * @returns {Promise<void>}
   */
  async fetchData() {
    // In a real app, you'd fetch this from Firebase.
    // Using mock data for now.
    const participantsPromise = Promise.resolve([
        { id: 'user1', name: 'John Doe', progress: 80, leadershipPoints: 120, communityCause: 'Education' },
        { id: 'user2', name: 'Jane Smith', progress: 100, leadershipPoints: 150, communityCause: 'Environment' },
        { id: 'user3', name: 'Sam Wilson', progress: 60, leadershipPoints: 90, communityCause: 'Health' },
    ]);
    const boothsPromise = Promise.resolve([
        { id: 'booth1', name: 'Welcome Booth', completionRate: 95 },
        { id: 'booth2', name: 'AR Experience', completionRate: 75 },
        { id: 'booth3', name: 'Quiz Challenge', completionRate: 88 },
    ]);

    [this.participants, this.booths] = await Promise.all([participantsPromise, boothsPromise]);
  }

  /**
   * Renders the main dashboard view.
   */
  renderDashboard() {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="dashboard-header">
        <h1>Admin Dashboard</h1>
        <button id="export-data-btn" class="btn btn-primary">Export Data</button>
      </div>
      <div class="dashboard-summary">
        ${this.renderSummaryCards()}
      </div>
      <div class="dashboard-main">
        <div class="participant-list">
          <h2>Participants</h2>
          ${this.renderParticipantList()}
        </div>
        <div class="booth-overview">
          <h2>Booth Progress</h2>
          ${this.renderBoothOverview()}
        </div>
      </div>
    `;

    this.attachEventListeners();
  }

  /**
   * Renders summary cards.
   * @returns {string} HTML string for summary cards.
   */
  renderSummaryCards() {
    const totalParticipants = this.participants.length;
    const averageProgress = this.participants.reduce((sum, p) => sum + p.progress, 0) / totalParticipants;
    return `
      <div class="summary-card">
        <h3>Total Participants</h3>
        <p>${totalParticipants}</p>
      </div>
      <div class="summary-card">
        <h3>Avg. Progress</h3>
        <p>${averageProgress.toFixed(1)}%</p>
      </div>
    `;
  }

  /**
   * Renders the list of participants.
   * @returns {string} HTML string for the participant list.
   */
  renderParticipantList() {
    return `
      <ul>
        ${this.participants.map(p => `
          <li data-participant-id="${p.id}">
            <span>${p.name}</span>
            <span>${p.progress}%</span>
          </li>
        `).join('')}
      </ul>
    `;
  }

  /**
   * Renders the booth overview.
   * @returns {string} HTML string for the booth overview.
   */
  renderBoothOverview() {
    return `
      <ul>
        ${this.booths.map(b => `
          <li>
            <span>${b.name}</span>
            <div class="progress-bar">
              <div class="progress" style="width: ${b.completionRate}%;"></div>
            </div>
            <span>${b.completionRate}%</span>
          </li>
        `).join('')}
      </ul>
    `;
  }

  /**
   * Renders a detailed view for a single participant.
   * @param {string} participantId - The ID of the participant to show.
   */
  renderParticipantDetail(participantId) {
    const participant = this.participants.find(p => p.id === participantId);
    if (!participant) {
      this.renderError('Participant not found.');
      return;
    }

    const detailView = document.createElement('div');
    detailView.className = 'modal-overlay';
    detailView.innerHTML = `
      <div class="modal-content">
        <button class="modal-close">&times;</button>
        <h2>${participant.name}</h2>
        <p><strong>Leadership Points:</strong> ${participant.leadershipPoints}</p>
        <p><strong>Community Cause:</strong> ${participant.communityCause}</p>
        <p><strong>Progress:</strong> ${participant.progress}%</p>
        {/* More details here */}
      </div>
    `;

    this.container.appendChild(detailView);
    detailView.querySelector('.modal-close').addEventListener('click', () => {
      detailView.remove();
    });
  }

  /**
   * Renders a loading state.
   */
  renderLoading() {
    if (!this.container) return;
    this.container.innerHTML = '<div class="loading-spinner">Loading...</div>';
  }

  /**
   * Renders an unauthorized message.
   */
  renderUnauthorized() {
    if (!this.container) return;
    this.container.innerHTML = '<div class="error-message">You are not authorized to view this page.</div>';
  }

  /**
   * Renders an error message.
   * @param {string} message - The error message to display.
   */
  renderError(message) {
    if (!this.container) return;
    this.container.innerHTML = `<div class="error-message">${message}</div>`;
  }

  /**
   * Attaches event listeners for the dashboard.
   */
  attachEventListeners() {
    const exportBtn = this.container.querySelector('#export-data-btn');
    if (exportBtn) {
      exportBtn.addEventListener('click', this.handleExportClick);
    }

    const participantItems = this.container.querySelectorAll('.participant-list li');
    participantItems.forEach(item => {
      item.addEventListener('click', () => this.handleParticipantClick(item.dataset.participantId));
    });
  }

  /**
   * Handles clicking on a participant.
   * @param {string} participantId - The ID of the clicked participant.
   */
  handleParticipantClick(participantId) {
    this.renderParticipantDetail(participantId);
  }

  /**
   * Handles the click on the export button.
   */
  handleExportClick() {
    const dataToExport = this.participants.map(p => ({
      'Name': p.name,
      'Leadership Points': p.leadershipPoints,
      'Community Cause': p.communityCause,
      'Progress (%)': p.progress,
    }));
    exportToCSV(dataToExport, 'participant_report.csv');
    showNotification('Data exported successfully!', { type: 'success' });
  }
}

export default AdminDashboard;