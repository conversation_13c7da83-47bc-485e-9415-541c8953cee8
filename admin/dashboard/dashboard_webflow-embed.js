/**
 * @file dashboard_webflow-embed.js
 * @description Webflow embed script for the Admin Dashboard.
 * @module admin/dashboard/embed
 */

import AdminDashboard from './dashboard.js';

/**
 * Initializes the admin dashboard on the Webflow page.
 */
function initializeAdminDashboard() {
  const dashboardContainer = document.querySelector('#admin-dashboard-container');
  
  if (dashboardContainer) {
    const dashboard = new AdminDashboard({
      container: dashboardContainer,
    });
    dashboard.initialize();
  } else {
    console.error('Admin dashboard container not found. Please ensure an element with id="admin-dashboard-container" exists.');
  }
}

// Initialize the dashboard when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeAdminDashboard);
} else {
  initializeAdminDashboard();
}