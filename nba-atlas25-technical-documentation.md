# NBA Atlas25 Summer League Technical Documentation

## 1. Executive Summary

The NBA Atlas25 Summer League project is a web application designed to enhance the NBA Summer League experience through interactive digital features. The application provides a comprehensive platform that integrates various interactive elements to engage fans and participants during the Summer League events.

### Purpose and Goals

The primary purpose of the NBA Atlas25 Summer League application is to create an immersive and interactive experience for attendees, particularly in venue environments where connectivity may be limited. The application achieves this through:

- **Interactive Experiences**: Augmented Reality (AR) features, QR code scanning, and quiz/trivia functionality
- **User Engagement**: Leaderboards, points system, and personalized experiences
- **Offline Functionality**: Robust offline support for environments with limited connectivity
- **Community Connection**: Content related to community impact initiatives

### Key Technical Highlights

- **Modular Architecture**: Components can be selectively integrated into a Webflow site
- **Offline-First Approach**: Comprehensive offline functionality with synchronization capabilities
- **Memberstack-Firebase Integration**: Seamless authentication and data management
- **Interactive Features**: AR experiences, QR scanning, and quiz functionality
- **Responsive Design**: Optimized for various devices and screen sizes

The application is designed with flexibility and scalability in mind, allowing for easy integration into the existing Webflow site infrastructure while providing a robust foundation for future enhancements.

## 2. Project Architecture

The NBA Atlas25 Summer League project follows a modular architecture with clear separation of concerns. This approach allows for selective integration of components into the Webflow site and facilitates maintenance and future development.

### High-Level Architecture

```mermaid
graph TD
    A[Webflow Site] --> B[Core Services]
    A --> C[Components]
    A --> D[Pages]
    
    B --> B1[Authentication]
    B --> B2[Firebase Services]
    B --> B3[Offline Support]
    B --> B4[State Management]
    
    C --> C1[AR Experience]
    C --> C2[Leaderboard]
    C --> C3[QR Scanner]
    C --> C4[Quiz Engine]
    
    D --> D1[Community Impact]
    D --> D2[Onboarding]
    
    B1 --> E[Memberstack]
    B2 --> F[Firebase]
    
    subgraph "Backend Services"
    E
    F
    end
```

### Key Architectural Patterns

1. **Modular Component Architecture**: The project is organized into independent modules that can be selectively embedded into a Webflow site. Each component is self-contained with its own implementation and Webflow integration files.

2. **Service-Oriented Design**: Core services (authentication, Firebase, offline support, state management) are implemented as singleton services that other components can consume. This promotes code reuse and maintainability.

3. **Offline-First Approach**: The application is designed to work offline with synchronization capabilities when connectivity is restored. This is crucial for venue environments with limited connectivity.

4. **State Management Pattern**: A centralized state management system with a pub/sub pattern for state changes ensures consistent application state across components.

5. **Adapter Pattern**: The Firebase service acts as an adapter between the application and Firebase SDK, abstracting the complexity of Firebase operations.

### Directory Structure and Organization

The project follows a logical directory structure that reflects its architecture:

```
/
├── admin/                  # Administrative interfaces
│   └── dashboard/          # Admin dashboard
├── components/             # Reusable UI components
│   ├── ar/                 # Augmented Reality experience
│   ├── leaderboard/        # User rankings display
│   ├── qr-scanner/         # QR code scanning functionality
│   └── quiz/               # Quiz/trivia functionality
├── core/                   # Core services and infrastructure
│   ├── auth/               # Authentication services
│   ├── firebase/           # Firebase integration
│   ├── offline/            # Offline functionality
│   └── state/              # State management
├── pages/                  # Page-specific code
│   ├── community-impact/   # Community initiatives page
│   └── onboarding/         # User onboarding flow
└── utils/                  # Utility functions
```

Each module typically contains:
- A main implementation file (e.g., `leaderboard.js`)
- A Webflow embed file (e.g., `leaderboard_webflow-embed.js`) for integration

This structure ensures clear separation of concerns and makes it easy to locate and maintain specific functionality.

## 3. Tech Stack

The NBA Atlas25 Summer League project utilizes a modern tech stack that balances performance, flexibility, and developer experience. The technology choices reflect the project's requirements for offline functionality, interactive features, and integration with Webflow.

### Frontend

- **JavaScript**: Core programming language for all client-side functionality
- **Webflow**: Base website platform providing the foundation for the application
- **Service Workers**: For offline functionality, asset caching, and background synchronization
- **IndexedDB**: Client-side database for offline data storage
- **Web APIs**:
  - Camera API (for QR scanning and AR)
  - Geolocation API
  - Canvas API
  - Web Storage API (localStorage and sessionStorage)
  - Fetch API for network requests

### Backend Services

- **Firebase**:
  - Firestore: NoSQL database for data storage
  - Storage: For file storage (images, videos, 3D models)
  - Functions: Serverless backend functionality
  - Analytics: User behavior tracking (optional)

- **Memberstack**:
  - Authentication and user management
  - Membership and subscription handling
  - User profile management

### AR Technologies

- **AR.js**: Marker-based AR experiences
- **A-Frame**: 3D scene creation and management
- **model-viewer**: 3D model display and interaction
- **WebXR**: Advanced AR experiences (where supported)

### Integration & Deployment

- **Custom embed scripts**: For integrating with Webflow
- **Environment-based configuration**: Development vs. production environments
- **Dynamic loading**: For optimized performance and resource usage

### Development Tools

- **JSDoc**: Code documentation
- **Modular design**: For code organization and maintainability
- **Progressive enhancement**: For broad device support

The tech stack is designed to be modern yet practical, focusing on technologies that provide the best balance of features, performance, and browser compatibility.

## 4. Core Components

### Firebase Integration

The Firebase integration serves as the data backbone of the application, providing real-time data storage, file storage, and serverless functions.

#### Configuration and Initialization

The Firebase configuration is managed in `firebase-config.js`, which provides environment-specific configurations:

```javascript
// Development environment configuration
const devConfig = {
  apiKey: "DEV_API_KEY",
  authDomain: "atlas25-dev.firebaseapp.com",
  projectId: "atlas25-dev",
  // ...other configuration properties
};

// Production environment configuration
const prodConfig = {
  apiKey: "PROD_API_KEY",
  authDomain: "atlas25-prod.firebaseapp.com",
  projectId: "atlas25-prod",
  // ...other configuration properties
};
```

The configuration is automatically selected based on the environment:

```javascript
function isProduction() {
  return window.location.hostname === 'atlas25.nba.com' ||
         window.location.hostname === 'www.atlas25.nba.com';
}

export function getFirebaseConfig() {
  return isProduction() ? prodConfig : devConfig;
}
```

#### Firebase Service Implementation

The `FirebaseService` class in `firebase-service.js` implements the adapter pattern, providing a simplified interface to Firebase operations:

- **CRUD Operations**: Methods for creating, reading, updating, and deleting documents
- **File Upload**: Methods for uploading files to Firebase Storage
- **Cloud Functions**: Methods for calling Firebase Cloud Functions
- **Offline Support**: Queuing operations when offline for later synchronization

Key features of the Firebase service include:

1. **Automatic Timestamps**: Creation and update timestamps are automatically added to documents
2. **Error Handling**: Comprehensive error handling with retry mechanisms
3. **Offline Queue**: Operations performed while offline are queued for later execution
4. **Exponential Backoff**: Failed operations are retried with exponential backoff

#### Offline Support with Synchronization Queue

The Firebase service integrates with the offline manager to provide seamless offline functionality:

```javascript
async queueOperation(operationType, operationData) {
  const operation = {
    id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: operationType,
    data: operationData,
    timestamp: new Date().toISOString(),
    retryCount: 0
  };
  
  // Add to sync queue
  this.syncQueue.push(operation);
  
  // Notify offline manager
  await offlineManager.addToSyncQueue(operation);
  
  console.log(`Operation queued for later execution: ${operationType}`);
}
```

When the device comes back online, the queued operations are processed:

```javascript
async processSyncQueue() {
  if (!navigator.onLine) {
    console.log('Still offline, sync deferred');
    return { success: 0, failed: 0 };
  }
  
  // Process each operation with exponential backoff retry
  for (const operation of this.syncQueue) {
    try {
      await this.executeOperation(operation);
      await offlineManager.removeFromSyncQueue(operation.id);
      successCount++;
    } catch (error) {
      // Implement retry with exponential backoff
      if (operation.retryCount < this.retryConfig.maxRetries) {
        operation.retryCount++;
        const delay = this.retryConfig.retryDelay *
                     Math.pow(this.retryConfig.backoffFactor, operation.retryCount - 1);
        
        // Schedule retry
        setTimeout(() => this.processSyncQueue(), delay);
      }
    }
  }
}
```

### Authentication System

The authentication system uses Memberstack as the primary authentication provider, with integration to Firebase for data storage.

#### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant App
    participant Memberstack
    participant Firebase
    
    User->>App: Login with credentials
    App->>Memberstack: Authenticate user
    Memberstack-->>App: Authentication result
    
    alt Authentication successful
        App->>Firebase: Store/retrieve user data using Memberstack ID
        Firebase-->>App: User data
        App->>App: Cache user data locally
    else Authentication failed
        App-->>User: Show error
    end
```

#### Key Features

1. **Memberstack Integration**: Uses Memberstack for authentication and user management
2. **Firebase Data Storage**: Stores user data in Firebase using Memberstack ID as the document ID
3. **Offline Authentication**: Supports offline authentication through local caching
4. **User Profile Management**: Handles user registration, login, and profile updates

#### Implementation Details

The authentication system is implemented in `auth.js` and provides the following functionality:

- **User Authentication**: Login with email and password via Memberstack
- **User Registration**: Registration with Memberstack and creation of user profile in Firebase
- **Profile Management**: Updating user profile information
- **Offline Support**: Caching user data for offline access
- **Permission Management**: Checking user permissions for access control

#### Offline Authentication

The system implements a simplified offline authentication mechanism:

```javascript
async function signInOffline(email, password) {
  try {
    console.log('Attempting offline sign in');
    
    const cachedUser = localStorage.getItem('atlas25_auth_user');
    if (!cachedUser) {
      throw new Error('No cached user available for offline login');
    }
    
    const userData = JSON.parse(cachedUser);
    
    // Check if the cached user matches the login attempt
    if (userData.user.email === email) {
      // In a real implementation, you would verify the password hash
      currentUser = userData.user;
      notifyAuthStateChange(currentUser);
      
      return currentUser;
    } else {
      throw new Error('Offline login failed: email does not match cached user');
    }
  } catch (error) {
    throw new Error('Offline authentication failed. Please connect to the internet and try again.');
  }
}
```

#### Current Implementation Analysis

The current implementation has several strengths:
- Clear separation of authentication and data storage concerns
- Offline support for authentication
- Comprehensive error handling

However, there are also areas for improvement:
- The offline authentication only verifies the email matches the cached user, not the password
- Limited error handling for scenarios where Memberstack is available but Firebase fails
- No explicit mechanism to ensure user data stays synchronized between Memberstack and Firebase

### State Management

The state management system follows a Redux-like pattern with a centralized store, actions, and reducers.

#### State Management Architecture

```mermaid
graph LR
    A[Component] --> B[Dispatch Action]
    B --> C[Reducer]
    C --> D[Update State]
    D --> E[Notify Subscribers]
    E --> A
    
    F[LocalStorage] <--> D
```

#### Key Features

1. **Centralized Store**: Single source of truth for application state
2. **Action-Based Updates**: State changes through well-defined actions
3. **Reducers**: Pure functions that handle state transitions
4. **Subscription Model**: Components subscribe to state changes
5. **Persistence**: Optional state persistence to localStorage

#### Implementation Details

The state management system is implemented in two main files:

1. **app-state.js**: Core state management functionality
   - Maintains the application state
   - Registers reducers for different action types
   - Handles state updates and notifications
   - Provides persistence to localStorage

2. **state-manager.js**: Simplified interface for components
   - Provides methods for getting and setting state values
   - Handles subscriptions to state changes
   - Manages auto-saving of state
   - Provides convenience methods for common state operations

#### State Structure

The application state is structured into logical sections:

```javascript
const initialState = {
  // User state
  user: {
    isAuthenticated: false,
    profile: null,
    preferences: {},
    progress: {}
  },
  
  // UI state
  ui: {
    currentPage: null,
    isLoading: false,
    activeModal: null,
    notifications: [],
    theme: 'light',
    sidebarOpen: false
  },
  
  // Content state
  content: {
    currentModule: null,
    currentLesson: null,
    completedLessons: [],
    quizResults: {},
    leaderboard: []
  },
  
  // Offline state
  offline: {
    isOnline: navigator.onLine,
    pendingSyncs: 0,
    lastSyncTime: null
  },
  
  // App metadata
  meta: {
    version: '1.0.0',
    buildNumber: '1',
    lastUpdated: new Date().toISOString()
  }
};
```

#### Usage Example

Components can interact with the state manager as follows:

```javascript
// Get a value from state
const isAuthenticated = stateManager.get('user.isAuthenticated');

// Update a value in state
stateManager.set('ui.theme', 'dark');

// Subscribe to state changes
const unsubscribe = stateManager.subscribeTo('content.leaderboard', (leaderboard) => {
  // Update UI when leaderboard changes
  updateLeaderboardUI(leaderboard);
});

// Dispatch an action
stateManager.dispatch({
  type: 'SET_USER',
  payload: userData
});
```

### Offline Capabilities

The offline functionality is a sophisticated part of the architecture, enabling the application to work seamlessly in environments with limited connectivity.

#### Offline Architecture

```mermaid
graph TD
    A[User Action] --> B{Online?}
    B -->|Yes| C[Direct API Call]
    B -->|No| D[Queue Operation]
    D --> E[Store in IndexedDB]
    
    F[Connection Restored] --> G[Process Queue]
    G --> H[Sync with Backend]
    H --> I{Conflicts?}
    I -->|Yes| J[Resolve Conflicts]
    I -->|No| K[Update Local State]
    
    L[Service Worker] --> M[Cache Assets]
    L --> N[Handle Offline Requests]
```

#### Key Components

1. **Service Worker**: Manages caching of application assets and handles offline requests
2. **Sync Queue**: Stores operations when offline for later synchronization
3. **IndexedDB**: Provides client-side storage for data and files
4. **Conflict Resolution**: Strategies for handling conflicts during synchronization
5. **Network Detection**: Monitors online/offline status and updates UI accordingly

#### Implementation Details

The offline functionality is implemented in several files:

1. **offline-manager.js**: Core offline functionality
   - Manages online/offline status
   - Handles asset caching
   - Provides IndexedDB storage
   - Manages the sync queue

2. **sync-queue.js**: Synchronization queue
   - Stores operations for later execution
   - Prioritizes operations
   - Handles retry logic

3. **service-worker.js**: Service worker implementation
   - Caches assets for offline use
   - Intercepts network requests
   - Provides fallbacks for offline requests

#### Offline Indicators

The system provides visual indicators for offline status:

```javascript
createDefaultOfflineIndicator() {
  // Check if there's already an offline indicator
  if (document.querySelector('.offline-indicator')) {
    return;
  }
  
  // Create a new offline indicator
  const indicator = document.createElement('div');
  indicator.className = 'offline-indicator';
  indicator.setAttribute('aria-live', 'polite');
  indicator.setAttribute('role', 'status');
  indicator.setAttribute('aria-hidden', this.isOnline ? 'true' : 'false');
  
  // Style the indicator
  Object.assign(indicator.style, {
    display: this.isOnline ? 'none' : 'block',
    position: 'fixed',
    top: '0',
    left: '0',
    right: '0',
    backgroundColor: '#f44336',
    color: 'white',
    textAlign: 'center',
    padding: '8px',
    zIndex: '9999',
    fontWeight: 'bold'
  });
  
  // Add content
  indicator.innerHTML = `
    <span>You are currently offline. Some features may be limited.</span>
  `;
  
  // Add to body
  document.body.appendChild(indicator);
  
  // Add to indicators list
  this.offlineIndicators.push(indicator);
}
```

#### Data Synchronization

When the device comes back online, the system automatically synchronizes data:

```javascript
async syncData() {
  if (!this.isOnline) {
    console.warn('Cannot sync data while offline');
    return { success: false, reason: 'offline' };
  }
  
  if (this.syncStatus.inProgress) {
    console.warn('Sync already in progress');
    return { success: false, reason: 'in-progress' };
  }
  
  try {
    this.syncStatus.inProgress = true;
    this.notifySyncListeners('start', { timestamp: new Date() });
    
    // Update service worker
    if (this.serviceWorkerRegistration) {
      try {
        await this.serviceWorkerRegistration.update();
      } catch (error) {
        console.warn('Failed to update service worker:', error);
      }
    }
    
    // Process sync queue
    const result = await syncQueue.processQueue();
    
    // Update sync status
    this.syncStatus.lastSync = new Date();
    await this.updateSyncStatus();
    
    this.notifySyncListeners('complete', { result, timestamp: new Date() });
    
    return { success: true, result };
  } catch (error) {
    console.error('Error syncing data:', error);
    this.notifySyncListeners('error', { error, timestamp: new Date() });
    return { success: false, error };
  } finally {
    this.syncStatus.inProgress = false;
  }
}
```

## 5. Feature Components

### AR Experience

The AR Experience component provides augmented reality functionality, allowing users to interact with virtual content in the real world.

#### Key Features

1. **Multiple AR Framework Support**:
   - AR.js for marker-based AR
   - A-Frame for 3D scene creation
   - model-viewer for 3D model display
   - WebXR for advanced AR experiences

2. **Content Types**:
   - 3D Models
   - Videos
   - Images
   - Text

3. **Marker-Based AR**:
   - Support for various marker types (Hiro, Kanji, pattern, barcode)
   - Custom marker support

4. **Offline Support**:
   - Asset caching for offline use
   - Local storage of experience data

5. **User Engagement Tracking**:
   - Duration tracking
   - Interaction counting
   - Marker scanning statistics

#### Implementation Details

The AR Experience is implemented in `ar-experience.js` and provides a comprehensive API for creating and managing AR experiences:

```javascript
class ARExperience {
  constructor(options) {
    this.options = {
      arFramework: 'ar.js',
      offlineSupport: true,
      trackEngagement: true,
      autoLoadAssets: true,
      ...options
    };
    
    this.experienceId = options.experienceId;
    this.container = typeof options.container === 'string' 
      ? document.querySelector(options.container) 
      : options.container;
      
    // Initialize properties
    this.experience = null;
    this.assets = [];
    this.markers = [];
    this.scannedMarkers = new Set();
    // ...other initialization
  }
  
  // Methods for loading and managing AR experiences
  async load() { /* ... */ }
  async startAR() { /* ... */ }
  stopAR() { /* ... */ }
  // ...other methods
}
```

#### AR Initialization Process

The AR initialization process involves several steps:

1. **Loading Experience Data**: Fetching experience configuration from Firebase or cache
2. **Loading AR Framework**: Dynamically loading the required AR framework
3. **Loading Assets**: Preloading assets for offline use
4. **Initializing AR Scene**: Creating the AR scene based on the selected framework
5. **Setting Up Markers**: Configuring markers and their associated content

#### Marker Handling

The component handles marker detection and content display:

```javascript
handleMarkerFound(markerId) {
  console.log(`Marker found: ${markerId}`);
  
  // Find marker data
  const marker = this.markers.find(m => m.id === markerId);
  if (!marker) return;
  
  // Track engagement
  if (this.options.trackEngagement) {
    this.engagementData.interactions++;
    this.engagementData.lastInteraction = new Date().toISOString();
    
    // Track as new marker if not already scanned
    if (!this.scannedMarkers.has(markerId)) {
      this.scannedMarkers.add(markerId);
      this.engagementData.markersScanned++;
    }
  }
  
  // Play video content if available
  if (marker.content && marker.content.type === AR_CONTENT_TYPES.VIDEO) {
    const videoElement = this.videoElements[markerId];
    if (videoElement) {
      videoElement.play().catch(error => {
        console.error(`Failed to play video for marker ${markerId}:`, error);
      });
    }
  }
  
  // Call onMarkerFound callback
  if (this.options.onMarkerFound) {
    this.options.onMarkerFound(markerId, marker);
  }
  
  // Check if all required markers have been scanned
  this.checkCompletionStatus();
}
```

#### Points and Leaderboard Integration

The AR experience integrates with the points system and leaderboard:

```javascript
async awardPoints(markerId, points) {
  try {
    const user = getCurrentUser();
    
    if (!user) {
      console.warn('User not authenticated, points not awarded');
      return;
    }
    
    const pointsData = {
      userId: user.uid,
      markerId,
      experienceId: this.experienceId,
      points,
      timestamp: new Date().toISOString()
    };
    
    // Save to Firebase or queue if offline
    if (navigator.onLine) {
      try {
        await firebaseService.addDocument('participation_points', pointsData);
        console.log(`${points} points awarded for marker ${markerId}`);
        
        // Update leaderboard
        this.updateLeaderboard(points);
      } catch (error) {
        // Queue for later if Firebase error
        if (this.options.offlineSupport) {
          await syncQueue.addToQueue({
            endpoint: 'firestore',
            method: 'ADD',
            data: {
              collection: 'participation_points',
              docData: pointsData
            }
          });
        }
      }
    } else if (this.options.offlineSupport) {
      // Queue for later if offline
      await syncQueue.addToQueue({
        endpoint: 'firestore',
        method: 'ADD',
        data: {
          collection: 'participation_points',
          docData: pointsData
        }
      });
      
      console.log(`${points} points queued for offline sync`);
    }
  } catch (error) {
    console.error('Failed to award points:', error);
  }
}
```

### Leaderboard

The Leaderboard component displays user rankings and scores, providing a competitive element to the application.

#### Key Features

1. **Real-time Updates**: Leaderboard updates in real-time as users earn points
2. **Filtering and Sorting**: Options for filtering and sorting leaderboard entries
3. **Offline Support**: Leaderboard data is cached for offline viewing
4. **User Highlighting**: Current user is highlighted in the leaderboard
5. **Customizable Display**: Options for displaying different leaderboard views

#### Implementation Details

The Leaderboard component fetches data from Firebase and displays it in a sorted list:

```javascript
async fetchLeaderboardData() {
  try {
    // Check if we have cached data
    if (this.options.offlineSupport) {
      const cachedData = await this.getCachedLeaderboardData();
      if (cachedData && !navigator.onLine) {
        console.log('Using cached leaderboard data');
        return cachedData;
      }
    }
    
    // If online, fetch from server
    if (navigator.onLine) {
      const query = {
        where: {
          field: 'boardId',
          operator: '==',
          value: this.boardId
        },
        orderBy: {
          field: 'score',
          direction: 'desc'
        },
        limit: this.options.limit || 100
      };
      
      const data = await firebaseService.getCollection('leaderboard_entries', query);
      
      // Cache the data for offline use
      if (this.options.offlineSupport) {
        await this.cacheLeaderboardData(data);
      }
      
      return data;
    }
    
    throw new Error('Cannot fetch leaderboard data: offline and no cached data available');
  } catch (error) {
    console.error('Error fetching leaderboard data:', error);
    throw error;
  }
}
```

### QR Scanner

The QR Scanner component enables users to scan QR codes for interactive experiences.

#### Key Features

1. **Multiple Scan Modes**:
   - Single scan mode (scan once and stop)
   - Continuous scan mode (scan multiple codes)

2. **QR Code Format Support**:
   - JSON format
   - URL format with parameters
   - Custom format (type:id:data)

3. **Camera Controls**:
   - Flashlight toggle
   - Camera switching (front/back)

4. **Offline Support**:
   - Scanning works offline
   - Scanned codes are queued for processing when online

5. **Error Handling**:
   - Comprehensive error messages
   - Permission handling
   - Device compatibility checks

#### Implementation Details

The QR Scanner is implemented in `qr-scanner.js` and uses the Html5Qrcode library for scanning:

```javascript
class QRScanner {
  constructor(options) {
    this.options = {
      startOnInit: false,
      scanMode: SCAN_MODES.CONTINUOUS,
      highlightScanRegion: true,
      highlightCodeOutline: true,
      ...options
    };
    
    this.videoElement = typeof options.videoElement === 'string' 
      ? document.querySelector(options.videoElement) 
      : options.videoElement;
      
    // Initialize properties
    this.scanning = false;
    this.lastResult = null;
    this.scanHistory = [];
    this.offlineQueue = [];
    this.isOnline = navigator.onLine;
    
    // Initialize QR code library
    this.initQRCodeLibrary();
    
    // Set up network listeners
    this.setupNetworkListeners();
    
    if (this.options.startOnInit) {
      this.start();
    }
  }
  
  // Methods for scanning QR codes
  async start(config = {}) { /* ... */ }
  stop() { /* ... */ }
  handleScanSuccess(decodedText, decodedResult) { /* ... */ }
  // ...other methods
}
```

#### QR Code Parsing

The component parses QR codes into a structured format:

```javascript
parseQRData(qrData) {
  try {
    // Try to parse as JSON first
    if (qrData.startsWith('{') && qrData.endsWith('}')) {
      return JSON.parse(qrData);
    }
    
    // Check for URL format with parameters
    if (qrData.includes('://')) {
      const url = new URL(qrData);
      const type = url.searchParams.get('type');
      const id = url.searchParams.get('id');
      const data = url.searchParams.get('data');
      
      return {
        type: type || QR_CODE_TYPES.UNKNOWN,
        id: id || null,
        url: qrData,
        data: data ? JSON.parse(decodeURIComponent(data)) : null,
        timestamp: new Date().toISOString()
      };
    }
    
    // Check for custom format (type:id:data)
    if (qrData.includes(':')) {
      const [type, id, ...dataParts] = qrData.split(':');
      const dataStr = dataParts.join(':');
      
      return {
        type: type || QR_CODE_TYPES.UNKNOWN,
        id: id || null,
        data: dataStr ? JSON.parse(dataStr) : null,
        timestamp: new Date().toISOString()
      };
    }
    
    // Default format
    return {
      type: QR_CODE_TYPES.UNKNOWN,
      data: qrData,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    // Return basic format if parsing fails
    return {
      type: QR_CODE_TYPES.UNKNOWN,
      data: qrData,
      rawData: qrData,
      timestamp: new Date().toISOString(),
      error: 'Parse error'
    };
  }
}
```

#### Offline Support

The QR Scanner supports offline scanning with synchronization when online:

```javascript
storeOfflineScan(qrData) {
  // Add to offline queue
  const offlineScan = {
    ...qrData,
    offlineTimestamp: Date.now()
  };
  
  this.offlineQueue.push(offlineScan);
  
  // Store in IndexedDB via sync queue
  this.queueScanForSync(offlineScan);
  
  console.log('Stored scan for offline processing:', offlineScan);
}

async queueScanForSync(scanData) {
  try {
    // Use the sync queue to store the scan
    await syncQueue.addToQueue({
      endpoint: 'qrScans',
      method: 'POST',
      data: scanData
    });
  } catch (error) {
    console.error('Failed to queue scan for sync:', error);
  }
}
```

### Quiz Engine

The Quiz Engine component provides an engaging quiz/trivia experience for users.

#### Key Features

1. **Multiple Question Types**:
   - Multiple choice
   - True/false
   - Fill in the blank
   - Image-based questions

2. **Scoring System**:
   - Points based on correctness
   - Time-based scoring
   - Streak bonuses

3